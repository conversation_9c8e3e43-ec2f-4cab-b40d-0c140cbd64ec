import pandas as pd
import os

# Dossier contenant tous tes fichiers CSV
data_dir = r"C:\work( sous disque C)\4eme Esprit\Stage d'été\FakeNews Project\FakeNewsNetData"

# Liste de tous les fichiers .csv dans le dossier
all_csv_files = [f for f in os.listdir(data_dir) if f.endswith(".csv")]

# Initialisation de la liste des DataFrames
dataframes = []

for file in all_csv_files:
    file_path = os.path.join(data_dir, file)
    
    try:
        # Lecture du CSV
        df = pd.read_csv(file_path)

        # Détection du label en fonction du nom de fichier
        file_lower = file.lower()
        if 'real' in file_lower:
            df['label'] = 1
        elif 'fake' in file_lower:
            df['label'] = 0
        else:
            print(f"⚠️ Fichier ignoré (pas de label détecté) : {file}")
            continue

        dataframes.append(df)
        print(f"✅ Chargé : {file} - Lignes : {df.shape[0]}")

    except Exception as e:
        print(f"❌ Erreur de chargement pour {file} : {e}")

# Fusion finale
df_all = pd.concat(dataframes, ignore_index=True)

# Aperçu
print(f"\n✅ Dataset fusionné - Total lignes : {df_all.shape[0]}, colonnes : {df_all.shape[1]}")
print(df_all['label'].value_counts())



from newspaper import Article

def extract_image(url):
    try:
        article = Article(url)
        article.download()
        article.parse()
        return article.top_image
    except:
        return None

df_all['image_url'] = df_all['url'].apply(extract_image)


#Appliquer la fonction extract_image(url) pour ajouter image_url
import os
import requests
from tqdm import tqdm

os.makedirs("images8", exist_ok=True)

def download_image(url, image_id):
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            image_path = f"images8/{image_id}.jpg"
            with open(image_path, "wb") as f:
                f.write(response.content)
            return image_path
    except:
        return None

df_all['image_path'] = [
    download_image(url, idx) if pd.notnull(url) else None
    for idx, url in tqdm(zip(df_all.index, df_all['image_url']), total=len(df_all))
]


#Télécharger les images et ajouter image_path
import os
import requests
from tqdm import tqdm

os.makedirs("images8", exist_ok=True)

def download_image(url, image_id):
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            image_path = f"images8/{image_id}.jpg"
            with open(image_path, "wb") as f:
                f.write(response.content)
            return image_path
    except:
        return None

df_all['image_path'] = [
    download_image(url, idx) if pd.notnull(url) else None
    for idx, url in tqdm(zip(df_all.index, df_all['image_url']), total=len(df_all))
]


print(df_all.columns)



# ✅ 1. Dimensions du DataFrame
print(f"Nombre de lignes : {df_all.shape[0]}")
print(f"Nombre de colonnes : {df_all.shape[1]}")

# ❓ 3. Vérifier les valeurs manquantes
print("\nValeurs manquantes par colonne :")
print(df_all.isnull().sum())


#Nettoyer le DataFrame final(Supprimer les lignes sans title ou image_path)
df_final = df_all[['title', 'text', 'image_path', 'label']].dropna()


#Sauvegarder ton fichier final 
df_final.to_csv("dataset_multimodal8.csv", index=False)


import pandas as pd

# 🔄 Recharger le dataset sauvegardé
df8 = pd.read_csv("dataset_multimodal8.csv")

# ✅ 1. Dimensions du DataFrame
print(f"Nombre de lignes : {df8.shape[0]}")
print(f"Nombre de colonnes : {df8.shape[1]}")

# 👀 2. Afficher les 5 premières lignes
print("\nPremières lignes :")
print(df8.head())

# ❓ 3. Vérifier les valeurs manquantes
print("\nValeurs manquantes par colonne :")
print(df8.isnull().sum())

# ❓ 4. Détection simple des outliers numériques (si applicable)
print("\nStatistiques descriptives :")
print(df8.describe())

# Tu peux aussi visualiser les colonnes non numériques :
print("\nColonnes non numériques :")
print(df8.select_dtypes(include=['object']).columns.tolist())


