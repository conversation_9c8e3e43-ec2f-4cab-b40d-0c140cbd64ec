Stack trace:
Frame         Function      Args
0007FFFF8B70  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF7A70) msys-2.0.dll+0x2118E
0007FFFF8B70  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF8B70  0002100469F2 (00021028DF99, 0007FFFF8A28, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF8B70  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF8B70  00021006A545 (0007FFFF8B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF8B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF947620000 ntdll.dll
7FF8FF870000 aswhook.dll
7FF946A10000 KERNEL32.DLL
7FF9449B0000 KERNELBASE.dll
7FF941670000 apphelp.dll
7FF9453C0000 USER32.dll
7FF9450F0000 win32u.dll
000210040000 msys-2.0.dll
7FF945600000 GDI32.dll
7FF944FB0000 gdi32full.dll
7FF945120000 msvcp_win.dll
7FF9451D0000 ucrtbase.dll
7FF9470B0000 advapi32.dll
7FF947530000 msvcrt.dll
7FF946C90000 sechost.dll
7FF945790000 RPCRT4.dll
7FF943C80000 CRYPTBASE.DLL
7FF945320000 bcryptPrimitives.dll
7FF946D40000 IMM32.DLL
