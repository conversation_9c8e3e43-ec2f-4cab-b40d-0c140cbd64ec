# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# Generated by:
#   $ cpp/cmake_modules/aws_sdk_cpp_generate_variables.sh 1.10.55

set(AWSSDK_UNUSED_DIRECTORIES
    .github
    AndroidSDKTesting
    CI
    Docs
    android-build
    android-unified-tests
    aws-cpp-sdk-AWSMigrationHub
    aws-cpp-sdk-access-management
    aws-cpp-sdk-accessanalyzer
    aws-cpp-sdk-account
    aws-cpp-sdk-acm
    aws-cpp-sdk-acm-pca
    aws-cpp-sdk-alexaforbusiness
    aws-cpp-sdk-amp
    aws-cpp-sdk-amplify
    aws-cpp-sdk-amplifybackend
    aws-cpp-sdk-amplifyuibuilder
    aws-cpp-sdk-apigateway
    aws-cpp-sdk-apigatewaymanagementapi
    aws-cpp-sdk-apigatewayv2
    aws-cpp-sdk-appconfig
    aws-cpp-sdk-appconfigdata
    aws-cpp-sdk-appflow
    aws-cpp-sdk-appintegrations
    aws-cpp-sdk-application-autoscaling
    aws-cpp-sdk-application-insights
    aws-cpp-sdk-applicationcostprofiler
    aws-cpp-sdk-appmesh
    aws-cpp-sdk-apprunner
    aws-cpp-sdk-appstream
    aws-cpp-sdk-appsync
    aws-cpp-sdk-arc-zonal-shift
    aws-cpp-sdk-athena
    aws-cpp-sdk-auditmanager
    aws-cpp-sdk-autoscaling
    aws-cpp-sdk-autoscaling-plans
    aws-cpp-sdk-awstransfer
    aws-cpp-sdk-backup
    aws-cpp-sdk-backup-gateway
    aws-cpp-sdk-backupstorage
    aws-cpp-sdk-batch
    aws-cpp-sdk-billingconductor
    aws-cpp-sdk-braket
    aws-cpp-sdk-budgets
    aws-cpp-sdk-ce
    aws-cpp-sdk-chime
    aws-cpp-sdk-chime-sdk-identity
    aws-cpp-sdk-chime-sdk-media-pipelines
    aws-cpp-sdk-chime-sdk-meetings
    aws-cpp-sdk-chime-sdk-messaging
    aws-cpp-sdk-chime-sdk-voice
    aws-cpp-sdk-cleanrooms
    aws-cpp-sdk-cloud9
    aws-cpp-sdk-cloudcontrol
    aws-cpp-sdk-clouddirectory
    aws-cpp-sdk-cloudformation
    aws-cpp-sdk-cloudfront
    aws-cpp-sdk-cloudfront-integration-tests
    aws-cpp-sdk-cloudhsm
    aws-cpp-sdk-cloudhsmv2
    aws-cpp-sdk-cloudsearch
    aws-cpp-sdk-cloudsearchdomain
    aws-cpp-sdk-cloudtrail
    aws-cpp-sdk-codeartifact
    aws-cpp-sdk-codebuild
    aws-cpp-sdk-codecatalyst
    aws-cpp-sdk-codecommit
    aws-cpp-sdk-codedeploy
    aws-cpp-sdk-codeguru-reviewer
    aws-cpp-sdk-codeguruprofiler
    aws-cpp-sdk-codepipeline
    aws-cpp-sdk-codestar
    aws-cpp-sdk-codestar-connections
    aws-cpp-sdk-codestar-notifications
    aws-cpp-sdk-cognito-idp
    aws-cpp-sdk-cognito-sync
    aws-cpp-sdk-cognitoidentity-integration-tests
    aws-cpp-sdk-comprehend
    aws-cpp-sdk-comprehendmedical
    aws-cpp-sdk-compute-optimizer
    aws-cpp-sdk-connect
    aws-cpp-sdk-connect-contact-lens
    aws-cpp-sdk-connectcampaigns
    aws-cpp-sdk-connectcases
    aws-cpp-sdk-connectparticipant
    aws-cpp-sdk-controltower
    aws-cpp-sdk-cur
    aws-cpp-sdk-custom-service-integration-tests
    aws-cpp-sdk-customer-profiles
    aws-cpp-sdk-databrew
    aws-cpp-sdk-dataexchange
    aws-cpp-sdk-datapipeline
    aws-cpp-sdk-datasync
    aws-cpp-sdk-dax
    aws-cpp-sdk-detective
    aws-cpp-sdk-devicefarm
    aws-cpp-sdk-devops-guru
    aws-cpp-sdk-directconnect
    aws-cpp-sdk-discovery
    aws-cpp-sdk-dlm
    aws-cpp-sdk-dms
    aws-cpp-sdk-docdb
    aws-cpp-sdk-docdb-elastic
    aws-cpp-sdk-drs
    aws-cpp-sdk-ds
    aws-cpp-sdk-dynamodb
    aws-cpp-sdk-dynamodb-integration-tests
    aws-cpp-sdk-dynamodbstreams
    aws-cpp-sdk-ebs
    aws-cpp-sdk-ec2
    aws-cpp-sdk-ec2-instance-connect
    aws-cpp-sdk-ec2-integration-tests
    aws-cpp-sdk-ecr
    aws-cpp-sdk-ecr-public
    aws-cpp-sdk-ecs
    aws-cpp-sdk-eks
    aws-cpp-sdk-elastic-inference
    aws-cpp-sdk-elasticache
    aws-cpp-sdk-elasticbeanstalk
    aws-cpp-sdk-elasticfilesystem
    aws-cpp-sdk-elasticfilesystem-integration-tests
    aws-cpp-sdk-elasticloadbalancing
    aws-cpp-sdk-elasticloadbalancingv2
    aws-cpp-sdk-elasticmapreduce
    aws-cpp-sdk-elastictranscoder
    aws-cpp-sdk-email
    aws-cpp-sdk-emr-containers
    aws-cpp-sdk-emr-serverless
    aws-cpp-sdk-es
    aws-cpp-sdk-eventbridge
    aws-cpp-sdk-eventbridge-tests
    aws-cpp-sdk-events
    aws-cpp-sdk-evidently
    aws-cpp-sdk-finspace
    aws-cpp-sdk-finspace-data
    aws-cpp-sdk-firehose
    aws-cpp-sdk-fis
    aws-cpp-sdk-fms
    aws-cpp-sdk-forecast
    aws-cpp-sdk-forecastquery
    aws-cpp-sdk-frauddetector
    aws-cpp-sdk-fsx
    aws-cpp-sdk-gamelift
    aws-cpp-sdk-gamesparks
    aws-cpp-sdk-glacier
    aws-cpp-sdk-globalaccelerator
    aws-cpp-sdk-glue
    aws-cpp-sdk-grafana
    aws-cpp-sdk-greengrass
    aws-cpp-sdk-greengrassv2
    aws-cpp-sdk-groundstation
    aws-cpp-sdk-guardduty
    aws-cpp-sdk-health
    aws-cpp-sdk-healthlake
    aws-cpp-sdk-honeycode
    aws-cpp-sdk-iam
    aws-cpp-sdk-identitystore
    aws-cpp-sdk-imagebuilder
    aws-cpp-sdk-importexport
    aws-cpp-sdk-inspector
    aws-cpp-sdk-inspector2
    aws-cpp-sdk-iot
    aws-cpp-sdk-iot-data
    aws-cpp-sdk-iot-jobs-data
    aws-cpp-sdk-iot-roborunner
    aws-cpp-sdk-iot1click-devices
    aws-cpp-sdk-iot1click-projects
    aws-cpp-sdk-iotanalytics
    aws-cpp-sdk-iotdeviceadvisor
    aws-cpp-sdk-iotevents
    aws-cpp-sdk-iotevents-data
    aws-cpp-sdk-iotfleethub
    aws-cpp-sdk-iotfleetwise
    aws-cpp-sdk-iotsecuretunneling
    aws-cpp-sdk-iotsitewise
    aws-cpp-sdk-iotthingsgraph
    aws-cpp-sdk-iottwinmaker
    aws-cpp-sdk-iotwireless
    aws-cpp-sdk-ivs
    aws-cpp-sdk-ivschat
    aws-cpp-sdk-kafka
    aws-cpp-sdk-kafkaconnect
    aws-cpp-sdk-kendra
    aws-cpp-sdk-kendra-ranking
    aws-cpp-sdk-keyspaces
    aws-cpp-sdk-kinesis
    aws-cpp-sdk-kinesis-integration-tests
    aws-cpp-sdk-kinesis-video-archived-media
    aws-cpp-sdk-kinesis-video-media
    aws-cpp-sdk-kinesis-video-signaling
    aws-cpp-sdk-kinesis-video-webrtc-storage
    aws-cpp-sdk-kinesisanalytics
    aws-cpp-sdk-kinesisanalyticsv2
    aws-cpp-sdk-kinesisvideo
    aws-cpp-sdk-kms
    aws-cpp-sdk-lakeformation
    aws-cpp-sdk-lambda
    aws-cpp-sdk-lambda-integration-tests
    aws-cpp-sdk-lex
    aws-cpp-sdk-lex-models
    aws-cpp-sdk-lexv2-models
    aws-cpp-sdk-lexv2-runtime
    aws-cpp-sdk-license-manager
    aws-cpp-sdk-license-manager-linux-subscriptions
    aws-cpp-sdk-license-manager-user-subscriptions
    aws-cpp-sdk-lightsail
    aws-cpp-sdk-location
    aws-cpp-sdk-logs
    aws-cpp-sdk-logs-integration-tests
    aws-cpp-sdk-lookoutequipment
    aws-cpp-sdk-lookoutmetrics
    aws-cpp-sdk-lookoutvision
    aws-cpp-sdk-m2
    aws-cpp-sdk-machinelearning
    aws-cpp-sdk-macie
    aws-cpp-sdk-macie2
    aws-cpp-sdk-managedblockchain
    aws-cpp-sdk-marketplace-catalog
    aws-cpp-sdk-marketplace-entitlement
    aws-cpp-sdk-marketplacecommerceanalytics
    aws-cpp-sdk-mediaconnect
    aws-cpp-sdk-mediaconvert
    aws-cpp-sdk-medialive
    aws-cpp-sdk-mediapackage
    aws-cpp-sdk-mediapackage-vod
    aws-cpp-sdk-mediastore
    aws-cpp-sdk-mediastore-data
    aws-cpp-sdk-mediastore-data-integration-tests
    aws-cpp-sdk-mediatailor
    aws-cpp-sdk-memorydb
    aws-cpp-sdk-meteringmarketplace
    aws-cpp-sdk-mgn
    aws-cpp-sdk-migration-hub-refactor-spaces
    aws-cpp-sdk-migrationhub-config
    aws-cpp-sdk-migrationhuborchestrator
    aws-cpp-sdk-migrationhubstrategy
    aws-cpp-sdk-mobile
    aws-cpp-sdk-monitoring
    aws-cpp-sdk-mq
    aws-cpp-sdk-mturk-requester
    aws-cpp-sdk-mwaa
    aws-cpp-sdk-neptune
    aws-cpp-sdk-network-firewall
    aws-cpp-sdk-networkmanager
    aws-cpp-sdk-nimble
    aws-cpp-sdk-oam
    aws-cpp-sdk-omics
    aws-cpp-sdk-opensearch
    aws-cpp-sdk-opensearchserverless
    aws-cpp-sdk-opsworks
    aws-cpp-sdk-opsworkscm
    aws-cpp-sdk-organizations
    aws-cpp-sdk-outposts
    aws-cpp-sdk-panorama
    aws-cpp-sdk-personalize
    aws-cpp-sdk-personalize-events
    aws-cpp-sdk-personalize-runtime
    aws-cpp-sdk-pi
    aws-cpp-sdk-pinpoint
    aws-cpp-sdk-pinpoint-email
    aws-cpp-sdk-pinpoint-sms-voice-v2
    aws-cpp-sdk-pipes
    aws-cpp-sdk-polly
    aws-cpp-sdk-polly-sample
    aws-cpp-sdk-pricing
    aws-cpp-sdk-privatenetworks
    aws-cpp-sdk-proton
    aws-cpp-sdk-qldb
    aws-cpp-sdk-qldb-session
    aws-cpp-sdk-queues
    aws-cpp-sdk-quicksight
    aws-cpp-sdk-ram
    aws-cpp-sdk-rbin
    aws-cpp-sdk-rds
    aws-cpp-sdk-rds-data
    aws-cpp-sdk-rds-integration-tests
    aws-cpp-sdk-redshift
    aws-cpp-sdk-redshift-data
    aws-cpp-sdk-redshift-integration-tests
    aws-cpp-sdk-redshift-serverless
    aws-cpp-sdk-rekognition
    aws-cpp-sdk-resiliencehub
    aws-cpp-sdk-resource-explorer-2
    aws-cpp-sdk-resource-groups
    aws-cpp-sdk-resourcegroupstaggingapi
    aws-cpp-sdk-robomaker
    aws-cpp-sdk-rolesanywhere
    aws-cpp-sdk-route53
    aws-cpp-sdk-route53-recovery-cluster
    aws-cpp-sdk-route53-recovery-control-config
    aws-cpp-sdk-route53-recovery-readiness
    aws-cpp-sdk-route53domains
    aws-cpp-sdk-route53resolver
    aws-cpp-sdk-rum
    aws-cpp-sdk-sagemaker
    aws-cpp-sdk-sagemaker-a2i-runtime
    aws-cpp-sdk-sagemaker-edge
    aws-cpp-sdk-sagemaker-featurestore-runtime
    aws-cpp-sdk-sagemaker-geospatial
    aws-cpp-sdk-sagemaker-metrics
    aws-cpp-sdk-sagemaker-runtime
    aws-cpp-sdk-savingsplans
    aws-cpp-sdk-scheduler
    aws-cpp-sdk-schemas
    aws-cpp-sdk-sdb
    aws-cpp-sdk-secretsmanager
    aws-cpp-sdk-securityhub
    aws-cpp-sdk-securitylake
    aws-cpp-sdk-serverlessrepo
    aws-cpp-sdk-service-quotas
    aws-cpp-sdk-servicecatalog
    aws-cpp-sdk-servicecatalog-appregistry
    aws-cpp-sdk-servicediscovery
    aws-cpp-sdk-sesv2
    aws-cpp-sdk-shield
    aws-cpp-sdk-signer
    aws-cpp-sdk-simspaceweaver
    aws-cpp-sdk-sms
    aws-cpp-sdk-sms-voice
    aws-cpp-sdk-snow-device-management
    aws-cpp-sdk-snowball
    aws-cpp-sdk-sns
    aws-cpp-sdk-sqs
    aws-cpp-sdk-sqs-integration-tests
    aws-cpp-sdk-ssm
    aws-cpp-sdk-ssm-contacts
    aws-cpp-sdk-ssm-incidents
    aws-cpp-sdk-ssm-sap
    aws-cpp-sdk-sso
    aws-cpp-sdk-sso-admin
    aws-cpp-sdk-sso-oidc
    aws-cpp-sdk-states
    aws-cpp-sdk-storagegateway
    aws-cpp-sdk-support
    aws-cpp-sdk-support-app
    aws-cpp-sdk-swf
    aws-cpp-sdk-synthetics
    aws-cpp-sdk-text-to-speech
    aws-cpp-sdk-text-to-speech-tests
    aws-cpp-sdk-textract
    aws-cpp-sdk-timestream-query
    aws-cpp-sdk-timestream-write
    aws-cpp-sdk-transcribe
    aws-cpp-sdk-transcribestreaming
    aws-cpp-sdk-transcribestreaming-integration-tests
    aws-cpp-sdk-translate
    aws-cpp-sdk-voice-id
    aws-cpp-sdk-waf
    aws-cpp-sdk-waf-regional
    aws-cpp-sdk-wafv2
    aws-cpp-sdk-wellarchitected
    aws-cpp-sdk-wisdom
    aws-cpp-sdk-workdocs
    aws-cpp-sdk-worklink
    aws-cpp-sdk-workmail
    aws-cpp-sdk-workmailmessageflow
    aws-cpp-sdk-workspaces
    aws-cpp-sdk-workspaces-web
    aws-cpp-sdk-xray
    code-generation
    crt
    doc_crosslinks
    doc_crosslinks_new
    doxygen
    generated
    scripts
    testing-resources)
