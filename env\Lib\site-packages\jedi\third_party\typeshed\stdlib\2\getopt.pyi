from typing import List, <PERSON>ple

class GetoptError(Exception):
    opt: str
    msg: str
    def __init__(self, msg: str, opt: str = ...) -> None: ...
    def __str__(self) -> str: ...

error = GetoptError

def getopt(args: List[str], shortopts: str, longopts: List[str] = ...) -> <PERSON><PERSON>[List[Tuple[str, str]], List[str]]: ...
def gnu_getopt(args: List[str], shortopts: str, longopts: List[str] = ...) -> <PERSON><PERSON>[List[Tuple[str, str]], List[str]]: ...
