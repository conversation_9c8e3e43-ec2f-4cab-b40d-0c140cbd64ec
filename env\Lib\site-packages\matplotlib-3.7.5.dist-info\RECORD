__pycache__/pylab.cpython-38.pyc,,
matplotlib-3.7.5-py3.8-nspkg.pth,sha256=8AmPeV-f78aJb0I1LJrRHtvqIMlLBZGCBlG6PXj5EAY,498
matplotlib-3.7.5.dist-info/DELVEWHEEL,sha256=dwb4Vi5H3vAwAl0GwwJUFS9iU8LTTUr2YKNi2euG6Us,397
matplotlib-3.7.5.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
matplotlib-3.7.5.dist-info/LICENSE,sha256=ojr3trhyymyw7GeYxkhO2JYoAxUVscbgMFz9b1qrcVM,4928
matplotlib-3.7.5.dist-info/LICENSE_AMSFONTS,sha256=1nBvhOSH8d3ceR8cyG_bknr7Wg4RSmoB5M_HE72FVyE,12915
matplotlib-3.7.5.dist-info/LICENSE_BAKOMA,sha256=1eFitoSKdvnOH02OCZL5B1v1I5GSKCJC-ac36uUaokc,1480
matplotlib-3.7.5.dist-info/LICENSE_CARLOGO,sha256=zpO9wbKCiF7rqj4STQcHWjzLj_67kzhx1mzdwjnLdIE,4499
matplotlib-3.7.5.dist-info/LICENSE_COLORBREWER,sha256=Q00sna66MPEEZTdg-5R_kRwLq8dF76MjYl__SWvziEk,707
matplotlib-3.7.5.dist-info/LICENSE_COURIERTEN,sha256=qFB_cSWBi-FP0wZcT9yRn-gWk1i6MwbnRRWseq8hyuo,820
matplotlib-3.7.5.dist-info/LICENSE_JSXTOOLS_RESIZE_OBSERVER,sha256=7QUCZFhhOjyfNEcDDsbUAPaUy3QWkpKPWGIIv-Hl5y4,6907
matplotlib-3.7.5.dist-info/LICENSE_QHULL,sha256=EG1VyTH9aoSCLlNF2QAnPQWfHCcxDQJWfMsxPF0YxV0,1720
matplotlib-3.7.5.dist-info/LICENSE_QT4_EDITOR,sha256=AlDgmC0knGnjFWMZHYO0xVFQ4ws699gKFMSnKueffdM,1260
matplotlib-3.7.5.dist-info/LICENSE_SOLARIZED,sha256=RrSaK9xcK12Uhuka3LOhEB_QW5ibbYX3kdwCakxULM0,1141
matplotlib-3.7.5.dist-info/LICENSE_STIX,sha256=I3calycBxqh5ggJcyDvyYU4vu6Qf2bpleUWbTmWKDL4,3985
matplotlib-3.7.5.dist-info/LICENSE_YORICK,sha256=iw-4fuTKjfpFYXIStZJ_pmLmIuZZWzUIpz6RwIKCSkk,2362
matplotlib-3.7.5.dist-info/METADATA,sha256=S4XvirqdE7RHkaYrjXhVFh1lL4XhEq-CZGAZesRqZgw,5799
matplotlib-3.7.5.dist-info/RECORD,,
matplotlib-3.7.5.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib-3.7.5.dist-info/WHEEL,sha256=3SeyPJ5-Us2Ct5GSftUVKtLSlm-bNefW4m5qd0GLzww,100
matplotlib-3.7.5.dist-info/namespace_packages.txt,sha256=A2PHFg9NKYOU4pEQ1h97U0Qd-rB-65W34XqC-56ZN9g,13
matplotlib-3.7.5.dist-info/top_level.txt,sha256=9tEw2ni8DdgX8CceoYHqSH1s50vrJ9SDfgtLIG8e3Y4,30
matplotlib.libs/.load-order-matplotlib-3.7.5,sha256=03_YoWv4VxPDcj_O_qvTpHW3LzYzOfiyFCwlNwC2bWk,46
matplotlib.libs/msvcp140-456d948669199b545d061b84c160bebc.dll,sha256=j1nF9clJpqZTLsG7o_4IX6jBVzVPZuyBZqCcxbfsrsg,621960
matplotlib/__init__.py,sha256=VC5FmaK73mIvoeDCN5zUwn0EDqItIxuUHr0RuEfmVrY,56261
matplotlib/__pycache__/__init__.cpython-38.pyc,,
matplotlib/__pycache__/_afm.cpython-38.pyc,,
matplotlib/__pycache__/_animation_data.cpython-38.pyc,,
matplotlib/__pycache__/_blocking_input.cpython-38.pyc,,
matplotlib/__pycache__/_cm.cpython-38.pyc,,
matplotlib/__pycache__/_cm_listed.cpython-38.pyc,,
matplotlib/__pycache__/_color_data.cpython-38.pyc,,
matplotlib/__pycache__/_constrained_layout.cpython-38.pyc,,
matplotlib/__pycache__/_docstring.cpython-38.pyc,,
matplotlib/__pycache__/_enums.cpython-38.pyc,,
matplotlib/__pycache__/_fontconfig_pattern.cpython-38.pyc,,
matplotlib/__pycache__/_internal_utils.cpython-38.pyc,,
matplotlib/__pycache__/_layoutgrid.cpython-38.pyc,,
matplotlib/__pycache__/_mathtext.cpython-38.pyc,,
matplotlib/__pycache__/_mathtext_data.cpython-38.pyc,,
matplotlib/__pycache__/_pylab_helpers.cpython-38.pyc,,
matplotlib/__pycache__/_text_helpers.cpython-38.pyc,,
matplotlib/__pycache__/_tight_bbox.cpython-38.pyc,,
matplotlib/__pycache__/_tight_layout.cpython-38.pyc,,
matplotlib/__pycache__/_type1font.cpython-38.pyc,,
matplotlib/__pycache__/_version.cpython-38.pyc,,
matplotlib/__pycache__/afm.cpython-38.pyc,,
matplotlib/__pycache__/animation.cpython-38.pyc,,
matplotlib/__pycache__/artist.cpython-38.pyc,,
matplotlib/__pycache__/axis.cpython-38.pyc,,
matplotlib/__pycache__/backend_bases.cpython-38.pyc,,
matplotlib/__pycache__/backend_managers.cpython-38.pyc,,
matplotlib/__pycache__/backend_tools.cpython-38.pyc,,
matplotlib/__pycache__/bezier.cpython-38.pyc,,
matplotlib/__pycache__/category.cpython-38.pyc,,
matplotlib/__pycache__/cm.cpython-38.pyc,,
matplotlib/__pycache__/collections.cpython-38.pyc,,
matplotlib/__pycache__/colorbar.cpython-38.pyc,,
matplotlib/__pycache__/colors.cpython-38.pyc,,
matplotlib/__pycache__/container.cpython-38.pyc,,
matplotlib/__pycache__/contour.cpython-38.pyc,,
matplotlib/__pycache__/dates.cpython-38.pyc,,
matplotlib/__pycache__/docstring.cpython-38.pyc,,
matplotlib/__pycache__/dviread.cpython-38.pyc,,
matplotlib/__pycache__/figure.cpython-38.pyc,,
matplotlib/__pycache__/font_manager.cpython-38.pyc,,
matplotlib/__pycache__/fontconfig_pattern.cpython-38.pyc,,
matplotlib/__pycache__/gridspec.cpython-38.pyc,,
matplotlib/__pycache__/hatch.cpython-38.pyc,,
matplotlib/__pycache__/image.cpython-38.pyc,,
matplotlib/__pycache__/layout_engine.cpython-38.pyc,,
matplotlib/__pycache__/legend.cpython-38.pyc,,
matplotlib/__pycache__/legend_handler.cpython-38.pyc,,
matplotlib/__pycache__/lines.cpython-38.pyc,,
matplotlib/__pycache__/markers.cpython-38.pyc,,
matplotlib/__pycache__/mathtext.cpython-38.pyc,,
matplotlib/__pycache__/mlab.cpython-38.pyc,,
matplotlib/__pycache__/offsetbox.cpython-38.pyc,,
matplotlib/__pycache__/patches.cpython-38.pyc,,
matplotlib/__pycache__/path.cpython-38.pyc,,
matplotlib/__pycache__/patheffects.cpython-38.pyc,,
matplotlib/__pycache__/pylab.cpython-38.pyc,,
matplotlib/__pycache__/pyplot.cpython-38.pyc,,
matplotlib/__pycache__/quiver.cpython-38.pyc,,
matplotlib/__pycache__/rcsetup.cpython-38.pyc,,
matplotlib/__pycache__/sankey.cpython-38.pyc,,
matplotlib/__pycache__/scale.cpython-38.pyc,,
matplotlib/__pycache__/spines.cpython-38.pyc,,
matplotlib/__pycache__/stackplot.cpython-38.pyc,,
matplotlib/__pycache__/streamplot.cpython-38.pyc,,
matplotlib/__pycache__/table.cpython-38.pyc,,
matplotlib/__pycache__/texmanager.cpython-38.pyc,,
matplotlib/__pycache__/text.cpython-38.pyc,,
matplotlib/__pycache__/textpath.cpython-38.pyc,,
matplotlib/__pycache__/ticker.cpython-38.pyc,,
matplotlib/__pycache__/tight_bbox.cpython-38.pyc,,
matplotlib/__pycache__/tight_layout.cpython-38.pyc,,
matplotlib/__pycache__/transforms.cpython-38.pyc,,
matplotlib/__pycache__/type1font.cpython-38.pyc,,
matplotlib/__pycache__/units.cpython-38.pyc,,
matplotlib/__pycache__/widgets.cpython-38.pyc,,
matplotlib/_afm.py,sha256=87K7we4I0PqPcTZI5pISo6XOSZ9jwesXe15x3cxzqcs,17225
matplotlib/_animation_data.py,sha256=acARDo0guBWKqylRmrPlsoXo4HZ8aDX2hOQiNCHSpAo,8234
matplotlib/_api/__init__.py,sha256=ztNziAxV7CkIT-CZo6LxNRs7RI6V7jSxT1g0pAeaW-Q,13988
matplotlib/_api/__pycache__/__init__.cpython-38.pyc,,
matplotlib/_api/__pycache__/deprecation.cpython-38.pyc,,
matplotlib/_api/deprecation.py,sha256=HbM-JYjBsNth1Klk8GgKXI-qdO-cG5ali6oHcyDP0VQ,20488
matplotlib/_blocking_input.py,sha256=GZa-p3Ly_2H-JiK0hzViiuvP_RBZ56C4heyP6lfJd1U,1254
matplotlib/_c_internal_utils.cp38-win_amd64.pyd,sha256=X7UHVyVugYLRtkPZqfAooo6g-iKlKSiDEl8lE2yx7b4,13824
matplotlib/_cm.py,sha256=mCpO1jttbM22_ub1uwrXdSdyI1WgihHJ1gGRMHj3GCk,67879
matplotlib/_cm_listed.py,sha256=hA_9d8M187heFixIHIY9ywETb7eIOr9Ei4cDOx1y_pc,111533
matplotlib/_color_data.py,sha256=7pIaZFS1d8zlRtlt7uOifeWgSQ9EwRkFzyzBEdltxTs,35921
matplotlib/_constrained_layout.py,sha256=PiKSTNScaKk9A_PrnXHJS_y6uZ48pOcDE7Q5UuJdPXU,31260
matplotlib/_docstring.py,sha256=IWDGAk3fmGEdurC9aZ1J6ntYLEFd2g_jChUoZLLlInA,3166
matplotlib/_enums.py,sha256=_x6okxUXCiSe10tGAELAFC4ucm0QfpuncLyXnt1BWU8,6659
matplotlib/_fontconfig_pattern.py,sha256=RPmbUY5YJcdbZntiXeWh2QF4urBYGoMQxBoK8mRa7dk,4866
matplotlib/_image.cp38-win_amd64.pyd,sha256=QrCpeulwJSBsxLaHO-5yMs83wTjpjLmkWo4dVcjlObo,142848
matplotlib/_internal_utils.py,sha256=MI2ymzqrQ1IH2yy6-n9mtm765vzgGSdEx7myejgbzt4,2204
matplotlib/_layoutgrid.py,sha256=DoBPwmpVAPtf6lPZ0OMINQtSDYN-6KBZIhpJto-TvKw,22223
matplotlib/_mathtext.py,sha256=e0lWfM9G5MzHSAl8XZrgw9BpoDrIAanrPCoe4tAMTtI,96276
matplotlib/_mathtext_data.py,sha256=hy-vtvnAMooDaogFMbash_v0AooPgPrtJ_sjFqmqPc0,49613
matplotlib/_path.cp38-win_amd64.pyd,sha256=fDu00K_PjOh2bVGifYOF9edtuChcPC14ycljo4spxSQ,141824
matplotlib/_pylab_helpers.py,sha256=Otw-AzZQXwoqIACPdwFiHVV-c4n6oI2Jxt3TvjKxFts,4466
matplotlib/_qhull.cp38-win_amd64.pyd,sha256=fk9Ohah2h2XyON0ewCHaHq5MBgEdyq-qjMDn449s3xs,450048
matplotlib/_text_helpers.py,sha256=1fBAod23cS2mrH-93gnxwmvXNhPPFP9ACx7ofYuVSDg,2570
matplotlib/_tight_bbox.py,sha256=KmtsgP3yvY7HZxwbonj3J_Wi6EzTIyXcr6ETDjHObLA,2871
matplotlib/_tight_layout.py,sha256=s3OshZ6AX9fKfHkgvX1SjE81ep4J1dz0iZ-cTuqY2lQ,12976
matplotlib/_tri.cp38-win_amd64.pyd,sha256=tF4aeFktplAmt6worUBGnvTe38COlfW4BCG1jugy4vE,209920
matplotlib/_ttconv.cp38-win_amd64.pyd,sha256=6P-R7c9FdGiAONYyuOIjUqwdo9nBamIwwJ7-krV06u0,47104
matplotlib/_type1font.py,sha256=2I7XLdSBvdFrhVwH3WX1AC3885cwTnMeaMWhPSO7Z5k,29234
matplotlib/_version.py,sha256=W7N_TujCLPKiL2H5zolRO2O5kd4R1GspCbTHRtTmxLg,427
matplotlib/afm.py,sha256=pYcvr6wVcxuhQiqI01ccvkMpOxY59WxP2cEf0f8iRrg,143
matplotlib/animation.py,sha256=xO72uoQBhLqbRZRevFFj6QyssZhqOGbMbHyHe_If5iY,72480
matplotlib/artist.py,sha256=CvZi6vuRcVN10D0Was1RAR_EwZo8bMglJYmTBqrtOUo,65339
matplotlib/axes/__init__.py,sha256=aM5YBL7totvj3OL8D-FUN4Wn2eqqbqUrEagWHyYusx4,386
matplotlib/axes/__pycache__/__init__.cpython-38.pyc,,
matplotlib/axes/__pycache__/_axes.cpython-38.pyc,,
matplotlib/axes/__pycache__/_base.cpython-38.pyc,,
matplotlib/axes/__pycache__/_secondary_axes.cpython-38.pyc,,
matplotlib/axes/_axes.py,sha256=L7yimsCvAA_OtpQnbFz7y1paS8XmI49Wh2ZjMphRmgc,332829
matplotlib/axes/_base.py,sha256=4UhKGHLcxN8KzzPb6rLPQZrY40JMwYMenti07mez55s,180567
matplotlib/axes/_secondary_axes.py,sha256=BETIQSS-kAkSFCS0Uw_tzv4A3sueVizs9V9a22rtDQc,10747
matplotlib/axis.py,sha256=hjnNP17thjq8U8rioOyMBgkhwG8b5WlQiLplv5l3heE,104710
matplotlib/backend_bases.py,sha256=CsgK4cemM5L5Y73IT4SVjdQbyhxKLXGBxdkyxP5mIik,135383
matplotlib/backend_managers.py,sha256=itysUI9BoiLUVlHTmlQfD5jIrUZZr1EIew3O9HL6Qic,12422
matplotlib/backend_tools.py,sha256=AQcLeksr-1-Wpq8g-ndayBxpSMbVOK8_pFVhSkNWj_s,33998
matplotlib/backends/__init__.py,sha256=CCKdqIC_OOME13OyDDCOQQ9Xh7j3Sh33En7WV5bf8Pk,140
matplotlib/backends/__pycache__/__init__.cpython-38.pyc,,
matplotlib/backends/__pycache__/_backend_gtk.cpython-38.pyc,,
matplotlib/backends/__pycache__/_backend_pdf_ps.cpython-38.pyc,,
matplotlib/backends/__pycache__/_backend_tk.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_agg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_cairo.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_gtk3.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_gtk3agg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_gtk3cairo.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_gtk4.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_gtk4agg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_gtk4cairo.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_macosx.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_mixed.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_nbagg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_pdf.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_pgf.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_ps.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_qt.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_qt5.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_qt5agg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_qt5cairo.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_qtagg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_qtcairo.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_svg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_template.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_tkagg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_tkcairo.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_webagg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_webagg_core.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_wx.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_wxagg.cpython-38.pyc,,
matplotlib/backends/__pycache__/backend_wxcairo.cpython-38.pyc,,
matplotlib/backends/__pycache__/qt_compat.cpython-38.pyc,,
matplotlib/backends/_backend_agg.cp38-win_amd64.pyd,sha256=hfWRb8AGmG0YNC6JMnDl1Jvt3lvGehl2_e0EsbQu4Y0,197120
matplotlib/backends/_backend_gtk.py,sha256=QkQeMPWmTK7ehC3_VJ_NsANWrmQu4PHai9ng-Cp4-FI,11676
matplotlib/backends/_backend_pdf_ps.py,sha256=DAvS8CuhBVw7J-YaaUs5K4vzpHnTyCt0rbW5-7q_Tzw,4587
matplotlib/backends/_backend_tk.py,sha256=U3eRdoUtDFz2kbZINVJMqaPgdYt3BjjC7j9Mk_kKkiw,43383
matplotlib/backends/_tkagg.cp38-win_amd64.pyd,sha256=FzH53-Jbm8Gq3pImD89X4kCjwuWKi-YcFFB39FK359Q,17920
matplotlib/backends/backend_agg.py,sha256=x46c6f8KuiqJRfh1n-up20JrsozZ-EqTiddsj4GGkHk,21553
matplotlib/backends/backend_cairo.py,sha256=45f2rttHBcSO51Vww38-KosbRnKbtxtkuGnWCSTztt8,18724
matplotlib/backends/backend_gtk3.py,sha256=uh5bohrhyOEqW7Ol7o6rC0fB3nWXfGYZH-ANzrxCPHA,23466
matplotlib/backends/backend_gtk3agg.py,sha256=o-xS-hLTRiiD4o2IgQJ2mvAZofjH04ZkHjiYdS5snRY,2538
matplotlib/backends/backend_gtk3cairo.py,sha256=C1JKXQ3-zAwZ3FO_4L73FO_jG16ZDbkCl7ucxrKPHmE,1049
matplotlib/backends/backend_gtk4.py,sha256=fGIQYlDeaj9R1kPdTiSddaq4O684gZx2VC437EiWEM8,22334
matplotlib/backends/backend_gtk4agg.py,sha256=qWKtpznJ850W2zm3cR9U9QKE2PuItzVZCqpXCVCM_xA,1302
matplotlib/backends/backend_gtk4cairo.py,sha256=ymXWmAtmFMw0MTmBOkxQociXvbRgqOTrUcVBLQe2iq0,1082
matplotlib/backends/backend_macosx.py,sha256=QWAN5dKG6UAjpwIVhgYBb7spUE3Vih0b5Pe5_KLfqNw,7037
matplotlib/backends/backend_mixed.py,sha256=EkLnKv8Jfd_gJZMBtLqttGiOz80YxIDOHHaJ0ueIJPs,4817
matplotlib/backends/backend_nbagg.py,sha256=muHlvpz-FdQqiYL2mNjkBmAI6vCikLKOJM4QkFLu2J4,8260
matplotlib/backends/backend_pdf.py,sha256=BoQJqtv9sCf6fYIojWb1mFPipi9_DZ_9zLbFU39VKsQ,108905
matplotlib/backends/backend_pgf.py,sha256=CIwo1PIrJGeJtXTqlRWFm-1XkBE-ymUMIHf5rb7XsYw,41911
matplotlib/backends/backend_ps.py,sha256=z9fqJyKdibXPZb7QlW3_eUnF6PvfKbO4UZ1meN7OM2M,49386
matplotlib/backends/backend_qt.py,sha256=AWQuT68eCEgknndC-VbYPc8ji7f7bmS0qJrkZojYzvQ,40740
matplotlib/backends/backend_qt5.py,sha256=Nbk522p3m19JxTIhq1k1fY2sD9H6vQ5qtSXKY-0VaGU,815
matplotlib/backends/backend_qt5agg.py,sha256=UrG49RnW4mRSZ0Fy2eZ_2A_RXHXVb5cgXoDoMuj95Bg,366
matplotlib/backends/backend_qt5cairo.py,sha256=7eOyMlEUFYx0XeXKrFZ8bLcxYRzqD-rTO37FC9kNtKE,303
matplotlib/backends/backend_qtagg.py,sha256=y4AY66OR46mPlyRyJs4O5fePyC2K32W1ZG4ZOQfC4ug,3085
matplotlib/backends/backend_qtcairo.py,sha256=Rvt6qKgQuapJL9u-Aca_TNZzr6n2EmGOPyrfcQTmI_0,1832
matplotlib/backends/backend_svg.py,sha256=_qK7Hu7c4ndDr0VXDoClennKsaqCGsqaINYJ1P9_ZYM,52063
matplotlib/backends/backend_template.py,sha256=zA5wCYuihb57zaqRO6oNcQn1c4lYKzLhEeeXfANHfgA,8223
matplotlib/backends/backend_tkagg.py,sha256=K-8GbaMypZJseZM_0sKaf0D6cyBGjM5QxGlkc0x2QQM,612
matplotlib/backends/backend_tkcairo.py,sha256=i9-H43dyHyChd7YrRIbOCfW_ujMUcOazBznBkC9-lgM,871
matplotlib/backends/backend_webagg.py,sha256=oK8fFzycAlUtW0SkqMMNNd9hpwzVEClyi3TL4-LvtTg,11706
matplotlib/backends/backend_webagg_core.py,sha256=Y2M0oyb56XOvlD-UwSSyHET0E3tpywmZwgdrf3aWFCE,18790
matplotlib/backends/backend_wx.py,sha256=tszVigh6k-7mK_GKC6azhRDf5bPYuTzdTVw6imC1skA,52845
matplotlib/backends/backend_wxagg.py,sha256=L-vhZ60MhXiT6d8OUQjDQ4YwqsF3AlHppK6ZkOMqK1w,2061
matplotlib/backends/backend_wxcairo.py,sha256=nqZ4FpWZdL5Y4mRxYdNX3-RLCdT_mRXpoLOfJdkqVzI,1508
matplotlib/backends/qt_compat.py,sha256=yln-0kp0y2BWKLey4CpfI0e3qlgNFK7YpEP3j6PmniY,9054
matplotlib/backends/qt_editor/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/backends/qt_editor/__pycache__/__init__.cpython-38.pyc,,
matplotlib/backends/qt_editor/__pycache__/_formlayout.cpython-38.pyc,,
matplotlib/backends/qt_editor/__pycache__/figureoptions.cpython-38.pyc,,
matplotlib/backends/qt_editor/_formlayout.py,sha256=Uo3pzC2AeHXxDT6mCRZZOpX5EnW461F_l4FXo0CEIVs,21610
matplotlib/backends/qt_editor/figureoptions.py,sha256=yWmg1wyjMc9fvTEphy2JVy2iE0H5uJ2dAQH8fcFy3GU,9784
matplotlib/backends/web_backend/.eslintrc.js,sha256=Dv3YGyMCOxbDobwrxr332zNYMCxb6s_o07kQeizIko8,698
matplotlib/backends/web_backend/.prettierignore,sha256=fhFE5YEVNHXvenOGu5fVvhzhGEMjutAocXz36mDB0iw,104
matplotlib/backends/web_backend/.prettierrc,sha256=Yz-e2yrtBxjx8MeDh7Z55idCjKgOxGZwSe6PQJo-4z0,156
matplotlib/backends/web_backend/all_figures.html,sha256=tLJxFoE2O1Q6o0tLS-NCXGXB9VMZT6q0FOGFynaMVik,1805
matplotlib/backends/web_backend/css/boilerplate.css,sha256=y2DbHYWFOmDcKhmUwACIwgZdL8mRqldKiQfABqlrCtA,2387
matplotlib/backends/web_backend/css/fbm.css,sha256=9p6d8mOOMoXlJMMsVTy-G14-MZzeJzFRRVJCCPG2d6A,1553
matplotlib/backends/web_backend/css/mpl.css,sha256=VfGbqCCnb-3ZCSgUyv1PcmfEPvank9340v-F2oyhapw,1695
matplotlib/backends/web_backend/css/page.css,sha256=QajhMz08CIWvxq_OvA82NyCKLI3xQrEmT0s8Iig8Zwg,1705
matplotlib/backends/web_backend/ipython_inline_figure.html,sha256=Zi-403BcJ13tVNCxcFPc8J6o1drqd8xMFtajyklbA7Y,1345
matplotlib/backends/web_backend/js/mpl.js,sha256=EGHpeTDcFtUQtOUWkuSFgnwIN1jd6xfmX3o8d-v2FGA,24646
matplotlib/backends/web_backend/js/mpl_tornado.js,sha256=k3JjkEWP-C2DCk8qrA3JPXFRl8Xu6gCf7zMtBZO4s5c,310
matplotlib/backends/web_backend/js/nbagg_mpl.js,sha256=rGWFaMlXgFhtEeWP_68152vpllc0St6N2atHqALvEUA,9789
matplotlib/backends/web_backend/nbagg_uat.ipynb,sha256=hOYF_VedchwuXXB_7abxWQ_2BS0bBzuEDks-QLLZ5RE,17058
matplotlib/backends/web_backend/package.json,sha256=f8YHuIsmdbdBkAGmymVs_wWmHYAQNPI8JFOyWgfQu3M,563
matplotlib/backends/web_backend/single_figure.html,sha256=1whLObtbAxFnTiiR_LCGDsw9HlyUllrgYpuQ5CBbnQc,1396
matplotlib/bezier.py,sha256=jaSAvn0o3PF0OnYjUBdp_6L9yJfQc0ECR9VKpXSdD3o,19273
matplotlib/category.py,sha256=jTqvoQI0f9C7JfUcqzxwS1VSDZCxUdOnWsnxSEdhyu0,7549
matplotlib/cbook/__init__.py,sha256=7sHGSKMnarnD60spH4s5UavPaVHoDHoiwiMKOX9w_oo,79334
matplotlib/cbook/__pycache__/__init__.cpython-38.pyc,,
matplotlib/cm.py,sha256=Gv5pGMGwA6zabe47jRXXwCskDDGJsDoS-krxewVJIMI,25960
matplotlib/collections.py,sha256=Mca7arul5oO47sGTIcH6X76giZLmYuU2i44CfxAMV7I,80660
matplotlib/colorbar.py,sha256=R43P2X9vTRT5WLxRpoQoJLXm4jShXg-eTNgCJuHJDjM,62818
matplotlib/colors.py,sha256=l1RkbwMopyjLJzwjduuSGdsIGJAOgANsgXqQWNG2Vz0,99555
matplotlib/container.py,sha256=OqV5XfCrrKOqMCUK64uGJPRMtJtEbBLxxWvaQ2CfCPE,4740
matplotlib/contour.py,sha256=TccVHSQxJoIWguao-Ld7XI780MyPyDkMhJ_2UuNUVZM,71963
matplotlib/dates.py,sha256=DuXQXhi9DG_nfxR70OuBFrCQ1izjRys26uEp6oIf2_A,71574
matplotlib/docstring.py,sha256=EVxiw_m2L-1cTHwYrIQUFAQlKe23GO1HG1KFlvhOxBA,160
matplotlib/dviread.py,sha256=g0TlNYKV9bemthrhmkAvP2Pv3OmjZUbvSc_LL3kQhVU,43601
matplotlib/figure.py,sha256=04gfJ-6_DTX_6iCVBZ3fawF3wGz7GVx3cK7Zw4-IzPk,139720
matplotlib/font_manager.py,sha256=Xt-usaKQ128HGruE6Ihn95DE3M5jc7VZeiBuTK0nYoA,55695
matplotlib/fontconfig_pattern.py,sha256=tQ1MasNVcd8sTOXqqoM3ldxyfhp8I4p8cWJWeohUCKo,700
matplotlib/ft2font.cp38-win_amd64.pyd,sha256=D3cy6D6__AXcQfntjhrpCbXKjFXENv2GudsrHo7_bb8,605184
matplotlib/gridspec.py,sha256=mqXknnPv1dmu4jnzE00UBkiHtUZG0U0ttSsxJ62Njvs,28602
matplotlib/hatch.py,sha256=fO4KqWZ_HrScbjTADtGBeF2ZZ_lBOlXMlGLyBUUqcP0,7674
matplotlib/image.py,sha256=U7D9dljBftNdSGmhhGRA3IbIQ6YW3s52kQoaovnb2Nk,73486
matplotlib/layout_engine.py,sha256=xWY5tE-lrSH2XpCAsaSP1W6Kfr4_4h4rlA6Bj_Uy63c,11640
matplotlib/legend.py,sha256=J2Qr8XI69LOafOeHJoYEI-nghgyokLc-6nkG-dEM8Dc,55430
matplotlib/legend_handler.py,sha256=BbbtFO-LHcAjdGbvXQejrZ7OEqVkvee6NFbTXgxFPEY,30963
matplotlib/lines.py,sha256=IKjQt8oJkJYvG4pLHt8oPrpxxq0B7xtm0V97osjC32Q,56143
matplotlib/markers.py,sha256=64zVCvkV2VRjh7c5dDny7viodACCrTZRZjxTpW1S0h8,36078
matplotlib/mathtext.py,sha256=YDt_vgK5T7OesE-KYAY6ElOd5Rjuc7NGoxehpMN1VUI,9380
matplotlib/mlab.py,sha256=hBwC6PJcExSRImxALmYiKqbtRhTK1LH6j0tZPMarOpo,33623
matplotlib/mpl-data/fonts/afm/cmex10.afm,sha256=zdDttyyqQ6Aa5AMVWowpNWgEksTX5KUAFqUjFMTBiUc,12290
matplotlib/mpl-data/fonts/afm/cmmi10.afm,sha256=dCq-QWC9Vl4WmcD9IOi-CMMB-vmVj8VBTOJS20ODMC0,10742
matplotlib/mpl-data/fonts/afm/cmr10.afm,sha256=bGb6XAS-H48vh-vmvrF0lMunP1c-RGNB3Uzm1Am9vew,10444
matplotlib/mpl-data/fonts/afm/cmsy10.afm,sha256=lxhR0CjcTVxKsQ4GPe8ypC4DdYCbn5j4IXlq2QTAcDM,8490
matplotlib/mpl-data/fonts/afm/cmtt10.afm,sha256=kFzBQ0WX0GZBss0jl_MogJ7ZvECCE8MnLpX58IFRUFU,6657
matplotlib/mpl-data/fonts/afm/pagd8a.afm,sha256=_-81-K4IGcnEXZmOqkIMyL42gBRcxMEuk8N8onDtLIM,17759
matplotlib/mpl-data/fonts/afm/pagdo8a.afm,sha256=GrsbRiN4fuoPK-SiaMngnmi5KyZC_nOFf_LYFk_Luxg,17831
matplotlib/mpl-data/fonts/afm/pagk8a.afm,sha256=Fjz-OUzE9qB-MCosDuUrBnMq8BXmldx6j_zgj2mKc1k,17814
matplotlib/mpl-data/fonts/afm/pagko8a.afm,sha256=pS716alw6ytmYYSRnz6hPvb1BZPlq3aUiViJFYagsHk,17919
matplotlib/mpl-data/fonts/afm/pbkd8a.afm,sha256=WOJW5hnEnwBQGQsVxtdXI2PJ1m8qwF-xC4n6aD3-hRI,15572
matplotlib/mpl-data/fonts/afm/pbkdi8a.afm,sha256=6D2SRhcYc-apq_Qq62bj_FwU_uHxIK09Tt_wjSSJS7c,15695
matplotlib/mpl-data/fonts/afm/pbkl8a.afm,sha256=_ZJuFBKoz70E-SBlhG4Tb9Yqxm3I6D-jALpy-R-vcew,15407
matplotlib/mpl-data/fonts/afm/pbkli8a.afm,sha256=1_6b55YPDXk9a3RUnNvja7y2iR3P6BKnh6DvkPnZ5pA,15591
matplotlib/mpl-data/fonts/afm/pcrb8a.afm,sha256=5CDJe3t71aM5qinDYSE8svWhc6RjteZPNslOvNVp8NA,15696
matplotlib/mpl-data/fonts/afm/pcrbo8a.afm,sha256=jNfbbBHfwvu0RglxuLeuh1K10KWEkj1lEVJR_hXQSWE,15766
matplotlib/mpl-data/fonts/afm/pcrr8a.afm,sha256=Hx4X9kbsRm_S3eo9WLjuOQzuOmeZHO33b6uhDTsH1NQ,15683
matplotlib/mpl-data/fonts/afm/pcrro8a.afm,sha256=DLrBm4iOvjCpKBnyC7yGAO4fdIvRUCO0uV_kQiy9VfQ,15787
matplotlib/mpl-data/fonts/afm/phvb8a.afm,sha256=PR_ybr2HVx6aOXVRza7a-255AtGEyDwj_pC_xK1VH1o,17725
matplotlib/mpl-data/fonts/afm/phvb8an.afm,sha256=pFHdjRgEoKxxmlf1PcTc-3Hyzh1Fzz2Xe2A8KzT3JuA,17656
matplotlib/mpl-data/fonts/afm/phvbo8a.afm,sha256=4ocMbnfWYxd-YhpzbWPDRzDdckBRIlEHPZfAORFiaZQ,17800
matplotlib/mpl-data/fonts/afm/phvbo8an.afm,sha256=cOAehWQUbLAtfhcWjTgZc8aLvKo_cWom0JdqmKDPsTo,17765
matplotlib/mpl-data/fonts/afm/phvl8a.afm,sha256=QTqJU4cVVtbvZhqGXFAMRNyZxlJtmq6HE6UIbh6vLYE,16072
matplotlib/mpl-data/fonts/afm/phvlo8a.afm,sha256=fAEd2GRQzamnndBw4ARWkJNKIBgmW24Jvo4wZDUVPRg,16174
matplotlib/mpl-data/fonts/afm/phvr8a.afm,sha256=7G6gNk10zsb_wkQ2qov_SuIMtspNafAGppFQ9V-7Fmo,18451
matplotlib/mpl-data/fonts/afm/phvr8an.afm,sha256=9TCWRgRyCgpwpKiF98j10hq9mHjdGv09aU96mcgfx2k,18393
matplotlib/mpl-data/fonts/afm/phvro8a.afm,sha256=9eXW8tsJO-8iw98HCd9H7sIbG5d3fQ-ik5-XXXMkm-8,18531
matplotlib/mpl-data/fonts/afm/phvro8an.afm,sha256=Rgr4U-gChgMcWU5VyFMwPg2gGXB5D9DhrbtYtWb7jSE,18489
matplotlib/mpl-data/fonts/afm/pncb8a.afm,sha256=ZAfYR6gDZoTjPw1X9CKXAKhdGZzgSlDfdmxFIAaNMP0,16500
matplotlib/mpl-data/fonts/afm/pncbi8a.afm,sha256=h6gIWhFKh3aiUMuA4QPCKN39ja1CJWDnagz-rLJWeJA,18098
matplotlib/mpl-data/fonts/afm/pncr8a.afm,sha256=wqphv_7-oIEDrGhzQspyDeFD07jzAs5uaSbLnrZ53q0,17189
matplotlib/mpl-data/fonts/afm/pncri8a.afm,sha256=mXZEWq-pTgsOG8_Nx5F52DMF8RB63RzAVOnH3QcRWPo,17456
matplotlib/mpl-data/fonts/afm/pplb8a.afm,sha256=Yd8M-qXEemyVsBt4OY7vKYqr7Yc_KfRnb2E505ij3Ts,16096
matplotlib/mpl-data/fonts/afm/pplbi8a.afm,sha256=BLReUiSSOvoiaTymK8hwqWCR2ndXJR2U2FAU2YKVhgM,16251
matplotlib/mpl-data/fonts/afm/pplr8a.afm,sha256=OdM7mp--HfFWfp9IwolGoviuHphoATNFl88OG3h3Uw8,16197
matplotlib/mpl-data/fonts/afm/pplri8a.afm,sha256=n0_vo-JC8voK6FKHvnZCygzsvTfNllQRya3L0dtTRZY,16172
matplotlib/mpl-data/fonts/afm/psyr.afm,sha256=HItpBqCppGKaLaLUdijTZ31jzUV13UVEohYVUPSk1Kc,9853
matplotlib/mpl-data/fonts/afm/ptmb8a.afm,sha256=td8VINDw_7_X3U6dHLcNxT-_2wU_CqaTSntSW696-M8,18631
matplotlib/mpl-data/fonts/afm/ptmbi8a.afm,sha256=ZbK1H28xxIwcZGSqGR6iVtpMrZ15LRN1OfVLpt1yiZ8,18718
matplotlib/mpl-data/fonts/afm/ptmr8a.afm,sha256=lwDiLF8RkJ46RoVFY0qEQ9J_h54cQHF6MCRx3ehLG_Y,18590
matplotlib/mpl-data/fonts/afm/ptmri8a.afm,sha256=rYNz084EPuCgdbZcIvuaXA77ozg5sOgmGZjwpFzDGKQ,18716
matplotlib/mpl-data/fonts/afm/putb8a.afm,sha256=RJuuhzr-dyocKMX4pgC9UoK3Ive2bnWtk74Oyx7ZBPk,22537
matplotlib/mpl-data/fonts/afm/putbi8a.afm,sha256=1gyQknXqpiVmyKrcRhmenSJrP0kot5h84HmWtDQ3Leg,22948
matplotlib/mpl-data/fonts/afm/putr8a.afm,sha256=Y_v97ZJKRPfPI2cFuFstBtBxRbAf3AK-hHYDVswMtdA,23177
matplotlib/mpl-data/fonts/afm/putri8a.afm,sha256=Hxu2gSVpV93zKO6ecqtZSJZHQyE1xhwcUnh-RJ8TYPo,22899
matplotlib/mpl-data/fonts/afm/pzcmi8a.afm,sha256=BhzLcod8-nVd2MWeZsO_GoZUXso4OhQktIZ2e7txJY8,16730
matplotlib/mpl-data/fonts/afm/pzdr.afm,sha256=a7-NgSTSEyakts84h-hXLrbRrxmFhxr_NR51bhOLZYQ,9689
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Bold.afm,sha256=rQFQ1L7cyId3Qr-UJR_OwT40jdWZ1GA_Z52SAn0ebpk,15675
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-BoldOblique.afm,sha256=y4LmnvX21CHo9AT-ALsNmTQlqueXJTMdDr-EfpTpfpI,15741
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Oblique.afm,sha256=snEDsqLvYDDBEGJll-KrR7uCeQdaA5q5Fw-ssKofcOE,15783
matplotlib/mpl-data/fonts/pdfcorefonts/Courier.afm,sha256=Uh4NfHUh79S-eKWpxTmOTGfQdx45YRWwNGvE73StpT0,15677
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Bold.afm,sha256=uIDZa69W0MwFnyWPYLTXZO9JtVWrnbKUuVnAAW3uQfI,72096
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-BoldOblique.afm,sha256=aZhKNcomlzo58mHPg-DTZ-ouZRfFkLCwMNTUohnZwmk,72192
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Oblique.afm,sha256=tGCbcbZgo5KsCd81BgJxqHbCzmZhfdg7-Cb3QrudlyE,77443
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica.afm,sha256=2jPxhwR0yOaL_j4jU_8QerbG7qH5g2ziqvHhoHsXmC8,77343
matplotlib/mpl-data/fonts/pdfcorefonts/Symbol.afm,sha256=PSEoqCA3WhDem8i_bPsV3tSCwByg-VzAsyd_N-yL3mY,9953
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Bold.afm,sha256=tKAA7YXLIsbN2YWqD9P2947VB5t8aGDcTwI04NDjxSI,66839
matplotlib/mpl-data/fonts/pdfcorefonts/Times-BoldItalic.afm,sha256=k8R0S6lVIV3gLEquC3dxM0Qq2i96taKvMKBAt5KzxV0,62026
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Italic.afm,sha256=7Tf6LmpntbF9_Ufzb8fpDfMokaRAiGDbyNTLvplZ4kI,68995
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Roman.afm,sha256=do4cq-oIXUiaY9o-gLlrxavw7JjTBzybTWunbnvMumQ,62879
matplotlib/mpl-data/fonts/pdfcorefonts/ZapfDingbats.afm,sha256=oyVlyQr9G1enAI_FZ7eNlc8cIq3_XghglNZm2IsDmFk,9752
matplotlib/mpl-data/fonts/pdfcorefonts/readme.txt,sha256=yQ1iD9441TPPu5-v-4nng62AUWpOPwW1M_NoeYTwGYQ,843
matplotlib/mpl-data/fonts/ttf/DejaVuSans-Bold.ttf,sha256=sYS4njwQdfIva3FXW2_CDUlys8_TsjMiym_Vltyu8Wc,704128
matplotlib/mpl-data/fonts/ttf/DejaVuSans-BoldOblique.ttf,sha256=bt8CgxYBhq9FHL7nHnuEXy5Mq_Jku5ks5mjIPCVGXm8,641720
matplotlib/mpl-data/fonts/ttf/DejaVuSans-Oblique.ttf,sha256=zN90s1DxH9PdV3TeUOXmNGoaXaH1t9X7g1kGZel6UhM,633840
matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf,sha256=P99pyr8GBJ6nCgC1kZNA4s4ebQKwzDxLRPtoAb0eDSI,756072
matplotlib/mpl-data/fonts/ttf/DejaVuSansDisplay.ttf,sha256=ggmdz7paqGjN_CdFGYlSX-MpL3N_s8ngMozpzvWWUvY,25712
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Bold.ttf,sha256=uq2ppRcv4giGJRr_BDP8OEYZEtXa8HKH577lZiCo2pY,331536
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-BoldOblique.ttf,sha256=ppCBwVx2yCfgonpaf1x0thNchDSZlVSV_6jCDTqYKIs,253116
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Oblique.ttf,sha256=KAUoE_enCfyJ9S0ZLcmV708P3Fw9e3OknWhJsZFtDNA,251472
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono.ttf,sha256=YC7Ia4lIz82VZIL-ZPlMNshndwFJ7y95HUYT9EO87LM,340240
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Bold.ttf,sha256=w3U_Lta8Zz8VhG3EWt2-s7nIcvMvsY_VOiHxvvHtdnY,355692
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-BoldItalic.ttf,sha256=2T7-x6nS6CZ2jRou6VuVhw4V4pWZqE80hK8d4c7C4YE,347064
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Italic.ttf,sha256=PnmU-8VPoQzjNSpC1Uj63X2crbacsRCbydlg9trFfwQ,345612
matplotlib/mpl-data/fonts/ttf/DejaVuSerif.ttf,sha256=EHJElW6ZYrnpb6zNxVGCXgrgiYrhNzcTPhuSGi_TX_o,379740
matplotlib/mpl-data/fonts/ttf/DejaVuSerifDisplay.ttf,sha256=KRTzLkfHd8J75Wd6-ufbTeefnkXeb8kJfZlJwjwU99U,14300
matplotlib/mpl-data/fonts/ttf/LICENSE_DEJAVU,sha256=xhup6GaKURy9C8_e6DKeAspspASKvabKfuisaKBZd2o,4915
matplotlib/mpl-data/fonts/ttf/LICENSE_STIX,sha256=IHQkYsHcFJE7OfAMoeBqLpiFtktZOgbeQgkGaYjb244,5600
matplotlib/mpl-data/fonts/ttf/STIXGeneral.ttf,sha256=FnN4Ax4t3cYhbWeBnJJg6aBv_ExHjk4jy5im_USxg8I,448228
matplotlib/mpl-data/fonts/ttf/STIXGeneralBol.ttf,sha256=6FM9xwg_o0a9oZM9YOpKg7Z9CUW86vGzVB-CtKDixqA,237360
matplotlib/mpl-data/fonts/ttf/STIXGeneralBolIta.ttf,sha256=mHiP1LpI37sr0CbA4gokeosGxzcoeWKLemuw1bsJc2w,181152
matplotlib/mpl-data/fonts/ttf/STIXGeneralItalic.ttf,sha256=bPyzM9IrfDxiO9_UAXTxTIXD1nMcphZsHtyAFA6uhSc,175040
matplotlib/mpl-data/fonts/ttf/STIXNonUni.ttf,sha256=Ulb34CEzWsSFTRgPDovxmJZOwvyCAXYnbhaqvGU3u1c,59108
matplotlib/mpl-data/fonts/ttf/STIXNonUniBol.ttf,sha256=XRBqW3jR_8MBdFU0ObhiV7-kXwiBIMs7QVClHcT5tgs,30512
matplotlib/mpl-data/fonts/ttf/STIXNonUniBolIta.ttf,sha256=pb22DnbDf2yQqizotc3wBDqFGC_g27YcCGJivH9-Le8,41272
matplotlib/mpl-data/fonts/ttf/STIXNonUniIta.ttf,sha256=BMr9pWiBv2YIZdq04X4c3CgL6NPLUPrl64aV1N4w9Ug,46752
matplotlib/mpl-data/fonts/ttf/STIXSizFiveSymReg.ttf,sha256=wYuH1gYUpCuusqItRH5kf9p_s6mUD-9X3L5RvRtKSxs,13656
matplotlib/mpl-data/fonts/ttf/STIXSizFourSymBol.ttf,sha256=yNdvjUoSmsZCULmD7SVq9HabndG9P4dPhboL1JpAf0s,12228
matplotlib/mpl-data/fonts/ttf/STIXSizFourSymReg.ttf,sha256=-9xVMYL4_1rcO8FiCKrCfR4PaSmKtA42ddLGqwtei1w,15972
matplotlib/mpl-data/fonts/ttf/STIXSizOneSymBol.ttf,sha256=cYexyo8rZcdqMlpa9fNF5a2IoXLUTZuIvh0JD1Qp0i4,12556
matplotlib/mpl-data/fonts/ttf/STIXSizOneSymReg.ttf,sha256=0lbHzpndzJmO8S42mlkhsz5NbvJLQCaH5Mcc7QZRDzc,19760
matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymBol.ttf,sha256=3eBc-VtYbhQU3BnxiypfO6eAzEu8BdDvtIJSFbkS2oY,12192
matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymReg.ttf,sha256=XFSKCptbESM8uxHtUFSAV2cybwxhSjd8dWVByq6f3w0,15836
matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymBol.ttf,sha256=MUCYHrA0ZqFiSE_PjIGlJZgMuv79aUgQqE7Dtu3kuo0,12116
matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymReg.ttf,sha256=_sdxDuEwBDtADpu9CyIXQxV7sIqA2TZVBCUiUjq5UCk,15704
matplotlib/mpl-data/fonts/ttf/cmb10.ttf,sha256=B0SXtQxD6ldZcYFZH5iT04_BKofpUQT1ZX_CSB9hojo,25680
matplotlib/mpl-data/fonts/ttf/cmex10.ttf,sha256=ryjwwXByOsd2pxv6WVrKCemNFa5cPVTOGa_VYZyWqQU,21092
matplotlib/mpl-data/fonts/ttf/cmmi10.ttf,sha256=MJKWW4gR_WpnZXmWZIRRgfwd0TMLk3-RWAjEhdMWI00,32560
matplotlib/mpl-data/fonts/ttf/cmr10.ttf,sha256=Tdl2GwWMAJ25shRfVe5mF9CTwnPdPWxbPkP_YRD6m_Y,26348
matplotlib/mpl-data/fonts/ttf/cmss10.ttf,sha256=ffkag9BbLkcexjjLC0NaNgo8eSsJ_EKn2mfpHy55EVo,20376
matplotlib/mpl-data/fonts/ttf/cmsy10.ttf,sha256=uyJu2TLz8QDNDlL15JEu5VO0G2nnv9uNOFTbDrZgUjI,29396
matplotlib/mpl-data/fonts/ttf/cmtt10.ttf,sha256=YhHwmuk1mZka_alwwkZp2tGnfiU9kVYk-_IS9wLwcdc,28136
matplotlib/mpl-data/images/back-symbolic.svg,sha256=yRdMiKsa-awUm2x_JE_rEV20rNTa7FInbFBEoMo-6ik,1512
matplotlib/mpl-data/images/back.pdf,sha256=ZR7CJo_dAeCM-KlaGvskgtHQyRtrPIolc8REOmcoqJk,1623
matplotlib/mpl-data/images/back.png,sha256=E4dGf4Gnz1xJ1v2tMygHV0YNQgShreDeVApaMb-74mU,380
matplotlib/mpl-data/images/back.svg,sha256=yRdMiKsa-awUm2x_JE_rEV20rNTa7FInbFBEoMo-6ik,1512
matplotlib/mpl-data/images/back_large.png,sha256=9A6hUSQeszhYONE4ZuH3kvOItM0JfDVu6tkfromCbsQ,620
matplotlib/mpl-data/images/filesave-symbolic.svg,sha256=oxPVbLS9Pzelz71C1GCJWB34DZ0sx_pUVPRHBrCZrGs,2029
matplotlib/mpl-data/images/filesave.pdf,sha256=P1EPPV2g50WTt8UaX-6kFoTZM1xVqo6S2H6FJ6Zd1ec,1734
matplotlib/mpl-data/images/filesave.png,sha256=b7ctucrM_F2mG-DycTedG_a_y4pHkx3F-zM7l18GLhk,458
matplotlib/mpl-data/images/filesave.svg,sha256=oxPVbLS9Pzelz71C1GCJWB34DZ0sx_pUVPRHBrCZrGs,2029
matplotlib/mpl-data/images/filesave_large.png,sha256=LNbRD5KZ3Kf7nbp-stx_a1_6XfGBSWUfDdpgmnzoRvk,720
matplotlib/mpl-data/images/forward-symbolic.svg,sha256=NnQDOenfjsn-o0aJMUfErrP320Zcx9XHZkLh0cjMHsk,1531
matplotlib/mpl-data/images/forward.pdf,sha256=KIqIL4YId43LkcOxV_TT5uvz1SP8k5iUNUeJmAElMV8,1630
matplotlib/mpl-data/images/forward.png,sha256=pKbLepgGiGeyY2TCBl8svjvm7Z4CS3iysFxcq4GR-wk,357
matplotlib/mpl-data/images/forward.svg,sha256=NnQDOenfjsn-o0aJMUfErrP320Zcx9XHZkLh0cjMHsk,1531
matplotlib/mpl-data/images/forward_large.png,sha256=36h7m7DZDHql6kkdpNPckyi2LKCe_xhhyavWARz_2kQ,593
matplotlib/mpl-data/images/hand.pdf,sha256=hspwkNY915KPD7AMWnVQs7LFPOtlcj0VUiLu76dMabQ,4172
matplotlib/mpl-data/images/hand.png,sha256=2cchRETGKa0hYNKUxnJABwkyYXEBPqJy_VqSPlT0W2Q,979
matplotlib/mpl-data/images/hand.svg,sha256=tsVIES_nINrAbH4FqdsCGOx0SVE37vcofSYBhnnaOP0,4888
matplotlib/mpl-data/images/help-symbolic.svg,sha256=KXabvQhqIWen_t2SvZuddFYa3S0iI3W8cAKm3s1fI8Q,1870
matplotlib/mpl-data/images/help.pdf,sha256=CeE978IMi0YWznWKjIT1R8IrP4KhZ0S7usPUvreSgcA,1813
matplotlib/mpl-data/images/help.png,sha256=s4pQrqaQ0py8I7vc9hv3BI3DO_tky-7YBMpaHuBDCBY,472
matplotlib/mpl-data/images/help.svg,sha256=KXabvQhqIWen_t2SvZuddFYa3S0iI3W8cAKm3s1fI8Q,1870
matplotlib/mpl-data/images/help_large.png,sha256=1IwEyWfGRgnoCWM-r9CJHEogTJVD5n1c8LXTK4AJ4RE,747
matplotlib/mpl-data/images/home-symbolic.svg,sha256=n_AosjJVXET3McymFuHgXbUr5vMLdXK2PDgghX8Cch4,1891
matplotlib/mpl-data/images/home.pdf,sha256=e0e0pI-XRtPmvUCW2VTKL1DeYu1pvPmUUeRSgEbWmik,1737
matplotlib/mpl-data/images/home.png,sha256=IcFdAAUa6_A0qt8IO3I8p4rpXpQgAlJ8ndBECCh7C1w,468
matplotlib/mpl-data/images/home.svg,sha256=n_AosjJVXET3McymFuHgXbUr5vMLdXK2PDgghX8Cch4,1891
matplotlib/mpl-data/images/home_large.png,sha256=uxS2O3tWOHh1iau7CaVV4ermIJaZ007ibm5Z3i8kXYg,790
matplotlib/mpl-data/images/matplotlib.pdf,sha256=BkSUf-2xoij-eXfpV2t7y1JFKG1zD1gtV6aAg3Xi_wE,22852
matplotlib/mpl-data/images/matplotlib.png,sha256=w8KLRYVa-voUZXa41hgJauQuoois23f3NFfdc72pUYY,1283
matplotlib/mpl-data/images/matplotlib.svg,sha256=QiTIcqlQwGaVPtHsEk-vtmJk1wxwZSvijhqBe_b9VCI,62087
matplotlib/mpl-data/images/matplotlib_large.png,sha256=ElRoue9grUqkZXJngk-nvh4GKfpvJ4gE69WryjCbX5U,3088
matplotlib/mpl-data/images/move-symbolic.svg,sha256=_ZKpcwGD6DMTkZlbyj0nQbT8Ygt5vslEZ0OqXaXGd4E,2509
matplotlib/mpl-data/images/move.pdf,sha256=CXk3PGK9WL5t-5J-G2X5Tl-nb6lcErTBS5oUj2St6aU,1867
matplotlib/mpl-data/images/move.png,sha256=TmjR41IzSzxGbhiUcV64X0zx2BjrxbWH3cSKvnG2vzc,481
matplotlib/mpl-data/images/move.svg,sha256=_ZKpcwGD6DMTkZlbyj0nQbT8Ygt5vslEZ0OqXaXGd4E,2509
matplotlib/mpl-data/images/move_large.png,sha256=Skjz2nW_RTA5s_0g88gdq2hrVbm6DOcfYW4Fu42Fn9U,767
matplotlib/mpl-data/images/qt4_editor_options.pdf,sha256=2qu6GVyBrJvVHxychQoJUiXPYxBylbH2j90QnytXs_w,1568
matplotlib/mpl-data/images/qt4_editor_options.png,sha256=EryQjQ5hh2dwmIxtzCFiMN1U6Tnd11p1CDfgH5ZHjNM,380
matplotlib/mpl-data/images/qt4_editor_options.svg,sha256=E00YoX7u4NrxMHm_L1TM8PDJ88bX5qRdCrO-Uj59CEA,1244
matplotlib/mpl-data/images/qt4_editor_options_large.png,sha256=-Pd-9Vh5aIr3PZa8O6Ge_BLo41kiEnpmkdDj8a11JkY,619
matplotlib/mpl-data/images/subplots-symbolic.svg,sha256=8acBogXIr9OWGn1iD6mUkgahdFZgDybww385zLCLoIs,2130
matplotlib/mpl-data/images/subplots.pdf,sha256=Q0syPMI5EvtgM-CE-YXKOkL9eFUAZnj_X2Ihoj6R4p4,1714
matplotlib/mpl-data/images/subplots.png,sha256=MUfCItq3_yzb9yRieGOglpn0Y74h8IA7m5i70B63iRc,445
matplotlib/mpl-data/images/subplots.svg,sha256=8acBogXIr9OWGn1iD6mUkgahdFZgDybww385zLCLoIs,2130
matplotlib/mpl-data/images/subplots_large.png,sha256=Edu9SwVMQEXJZ5ogU5cyW7VLcwXJdhdf-EtxxmxdkIs,662
matplotlib/mpl-data/images/zoom_to_rect-symbolic.svg,sha256=1vRxr3cl8QTwTuRlQzD1jxu0fXZofTJ2PMgG97E7Bco,1479
matplotlib/mpl-data/images/zoom_to_rect.pdf,sha256=SEvPc24gfZRpl-dHv7nx8KkxPyU66Kq4zgQTvGFm9KA,1609
matplotlib/mpl-data/images/zoom_to_rect.png,sha256=aNz3QZBrIgxu9E-fFfaQweCVNitGuDUFoC27e5NU2L4,530
matplotlib/mpl-data/images/zoom_to_rect.svg,sha256=1vRxr3cl8QTwTuRlQzD1jxu0fXZofTJ2PMgG97E7Bco,1479
matplotlib/mpl-data/images/zoom_to_rect_large.png,sha256=V6pkxmm6VwFExdg_PEJWdK37HB7k3cE_corLa7RbUMk,1016
matplotlib/mpl-data/kpsewhich.lua,sha256=34nb05ms1NPkBpKoXrhJraGO1-rJSpCGGINjqj6ch4o,142
matplotlib/mpl-data/matplotlibrc,sha256=qGoAlhPZJGAC6a0MlMKiTItSSIc8F5-2EJzpvhxAjZs,42626
matplotlib/mpl-data/plot_directive/plot_directive.css,sha256=dYBfao5OEGXxWGHeBQVOgTWeh6kPRpGFqiM-3sV3jbw,334
matplotlib/mpl-data/sample_data/Minduka_Present_Blue_Pack.png,sha256=XnKGiCanpDKalQ5anvo5NZSAeDP7fyflzQAaivuc0IE,13634
matplotlib/mpl-data/sample_data/README.txt,sha256=c8JfhUG72jHZj6SyS0hWvlXEtWUJbjRNfMZlA85SWIo,130
matplotlib/mpl-data/sample_data/Stocks.csv,sha256=zFYJIja-YuHSGqb9vs_xNXahlaXcqpbD8gTXxW--coA,68450
matplotlib/mpl-data/sample_data/axes_grid/bivariate_normal.npy,sha256=DpWZ9udAh6ospYqneEa27D6EkRgORFwHosacZXVu98U,1880
matplotlib/mpl-data/sample_data/data_x_x2_x3.csv,sha256=IG7mazfIlEyJnqIcZrKBEhjitrI3Wv35uVFVV6hBgMo,143
matplotlib/mpl-data/sample_data/eeg.dat,sha256=KGVjFt8ABKz7p6XZirNfcxSTOpGGNuyA8JYErRKLRBc,25600
matplotlib/mpl-data/sample_data/embedding_in_wx3.xrc,sha256=IcJ5PddMI2wSxlUGUUv3He3bsmGaRfBp9ZwEQz5QTdo,2250
matplotlib/mpl-data/sample_data/goog.npz,sha256=QAkXzzDmtmT3sNqT18dFhg06qQCNqLfxYNLdEuajGLE,22845
matplotlib/mpl-data/sample_data/grace_hopper.jpg,sha256=qMptc0dlcDsJcoq0f-WfRz2Trjln_CTHwCiMPHrbcTA,61306
matplotlib/mpl-data/sample_data/jacksboro_fault_dem.npz,sha256=1JP1CjPoKkQgSUxU0fyhU50Xe9wnqxkLxf5ukvYvtjc,174061
matplotlib/mpl-data/sample_data/logo2.png,sha256=ITxkJUsan2oqXgJDy6DJvwJ4aHviKeWGnxPkTjXUt7A,33541
matplotlib/mpl-data/sample_data/membrane.dat,sha256=q3lbQpIBpbtXXGNw1eFwkN_PwxdDGqk4L46IE2b0M1c,48000
matplotlib/mpl-data/sample_data/msft.csv,sha256=4JtKT5me60-GNMUoCMuIDAYAIpylT_EroyBbGh0yi_U,3276
matplotlib/mpl-data/sample_data/percent_bachelors_degrees_women_usa.csv,sha256=Abap-NFjqwp1ELGNYCoTL4S5vRniAzM5R3ixgEFFpTU,5723
matplotlib/mpl-data/sample_data/s1045.ima.gz,sha256=MrQk1k9it-ccsk0p_VOTitVmTWCAVaZ6srKvQ2n4uJ4,33229
matplotlib/mpl-data/sample_data/topobathy.npz,sha256=AkTgMpFwLfRQJNy1ysvE89TLMNct-n_TccSsYcQrT78,45224
matplotlib/mpl-data/stylelib/Solarize_Light2.mplstyle,sha256=GK3NqgUOwXv42Em3GQYQ6FiiQXMkyyWoIHufrfKXShU,1310
matplotlib/mpl-data/stylelib/_classic_test_patch.mplstyle,sha256=9XRyb2XzCtS6piLIYFbNHpU-bF4f7YliWLdbLXvBojI,173
matplotlib/mpl-data/stylelib/_mpl-gallery-nogrid.mplstyle,sha256=FWSzV2mZN4uuyNz6Bb1GfdImjtiub85I4pfd6HNKX04,508
matplotlib/mpl-data/stylelib/_mpl-gallery.mplstyle,sha256=ozPz-B09ieBm777FeDEifxRH_xVkPEGRiMVeFtnMps8,523
matplotlib/mpl-data/stylelib/bmh.mplstyle,sha256=UTO__T6YaaUY6u5NjAsBGBsv_AOK45nKi1scf-ORxzU,741
matplotlib/mpl-data/stylelib/classic.mplstyle,sha256=0Dk52wWw7kXDhQolBpMMMO9b3OFc2yTlNk8npg9m57M,25091
matplotlib/mpl-data/stylelib/dark_background.mplstyle,sha256=Vei27QYOP3dNTaHzmRYneNLTCw30nE75JOUDYuOjnXc,687
matplotlib/mpl-data/stylelib/fast.mplstyle,sha256=HDqa0GATC9GjNeRA8rYiZM-qh7hTxsraeyYziGlbgzg,299
matplotlib/mpl-data/stylelib/fivethirtyeight.mplstyle,sha256=IfXwiatqkv6rkauNnjcfDDS6pU-UabtEhbokK5-qAes,872
matplotlib/mpl-data/stylelib/ggplot.mplstyle,sha256=S7Oc_ty3DpUM0A2kctZBemMjj7IS0ccJl06QJFQh2Xw,995
matplotlib/mpl-data/stylelib/grayscale.mplstyle,sha256=MnigXJy2ckyQZuiwb-nCXQ0-0cJBz1WPu-CEJXEHWpA,555
matplotlib/mpl-data/stylelib/seaborn-v0_8-bright.mplstyle,sha256=DIo92H5LVQVPMeJOcVaOPOovchqMeDvkKoEQ0BX--wA,147
matplotlib/mpl-data/stylelib/seaborn-v0_8-colorblind.mplstyle,sha256=M7OYVR1choIo_jlDfMsGSADJahLDauZEOUJJpuDK8Hs,151
matplotlib/mpl-data/stylelib/seaborn-v0_8-dark-palette.mplstyle,sha256=HLb5n5XgW-IQ8b5YcTeIlA1QyHjP7wiNPAHD2syptW4,145
matplotlib/mpl-data/stylelib/seaborn-v0_8-dark.mplstyle,sha256=IZMc2QEnkTmbOfAr5HIiu6SymcdRbKWSIYGOtprNlDw,697
matplotlib/mpl-data/stylelib/seaborn-v0_8-darkgrid.mplstyle,sha256=lY9aae1ZeSJ1WyT42fi0lfuQi2t0vwhic8TBEphKA5c,700
matplotlib/mpl-data/stylelib/seaborn-v0_8-deep.mplstyle,sha256=djxxvf898QicTlmeDHJW5HVjrvHGZEOSIPWgFK0wqpw,145
matplotlib/mpl-data/stylelib/seaborn-v0_8-muted.mplstyle,sha256=5t2wew5ydrrJraEuuxH918TuAboCzuCVVj4kYq78_LU,146
matplotlib/mpl-data/stylelib/seaborn-v0_8-notebook.mplstyle,sha256=g0nB6xP2N5VfW31pBa4mRHZU5kLqZLQncj9ExpTuTi8,403
matplotlib/mpl-data/stylelib/seaborn-v0_8-paper.mplstyle,sha256=StESYj-S2Zv9Cngd5bpFqJVw4oBddpqB3C5qHESmzi8,414
matplotlib/mpl-data/stylelib/seaborn-v0_8-pastel.mplstyle,sha256=8KO6r5H2jWIophEf7XJVYKyrXSrYGEn2f1F_KXoEEIc,147
matplotlib/mpl-data/stylelib/seaborn-v0_8-poster.mplstyle,sha256=8xZxeZiSX2npJ-vCqsSsDcc4GeFrXwfrSNu0xXfA2Uk,424
matplotlib/mpl-data/stylelib/seaborn-v0_8-talk.mplstyle,sha256=_c29c8iDdsCMNVERcHHwD8khIcUVxeuoHI2o1eE0Phg,424
matplotlib/mpl-data/stylelib/seaborn-v0_8-ticks.mplstyle,sha256=Annui6BdMJqYZsIGCkdmk88r4m_H4esa7bSszkBpm-A,695
matplotlib/mpl-data/stylelib/seaborn-v0_8-white.mplstyle,sha256=VY6sw8wkqbl0leWtWa5gz8xfDMfqt5yEhITIjP4FsOI,695
matplotlib/mpl-data/stylelib/seaborn-v0_8-whitegrid.mplstyle,sha256=IOm2H1utXO_zR7FWqMLBiRxHyxABL3kq1fh0-6BDJ0E,694
matplotlib/mpl-data/stylelib/seaborn-v0_8.mplstyle,sha256=N9lUFHvOn06wT4MODXpVVGQMSueONejeAfCX5UfWrIM,1187
matplotlib/mpl-data/stylelib/tableau-colorblind10.mplstyle,sha256=PzUMoOtw0V6l0bPk8ApRAKvcxdJmzRU2bVOkNqz8DnU,192
matplotlib/offsetbox.py,sha256=jkBxGfNFgH30V3o-FOXnGT_PI6KN6lMAAcPPZucM5FI,56343
matplotlib/patches.py,sha256=8zALKfjdXCJeCnBHhBNUuuawEG-duG_xUvHgzz3hyJs,164034
matplotlib/path.py,sha256=UnMqN8Qiqqs-nuvYSqJtkNnY14yJE3xiuAIRE6_3f84,42388
matplotlib/patheffects.py,sha256=9fgNK8RHYAP9YnAyOA0wjFH6jnAFe_urWDjkWuqjM-0,19108
matplotlib/projections/__init__.py,sha256=rp_EZZwnOj3cnHEw46g7Diyxje7Bl7ahxabXyOfMsGk,4112
matplotlib/projections/__pycache__/__init__.cpython-38.pyc,,
matplotlib/projections/__pycache__/geo.cpython-38.pyc,,
matplotlib/projections/__pycache__/polar.cpython-38.pyc,,
matplotlib/projections/geo.py,sha256=JYoEhxTflXtAf97359sDgzMqWM-ryZN5kPS-yIjg5-w,17834
matplotlib/projections/polar.py,sha256=vAiLBdADyoMC6LUbNMvHEJAwujuH5d1KbqiWTk0pYG4,57906
matplotlib/pylab.py,sha256=ONAn6gF06i9jp92oFa1mZyaIjti3gwhkIXZVbwviTUY,1886
matplotlib/pyplot.py,sha256=F4YsQCmUDhU59B4zx-KmxbfKKjSwol0gWlf5y6N93hA,119293
matplotlib/quiver.py,sha256=a95bqvQVLh4XFnJKYx4_uyjeuCHSuCGtJPwYj35BJho,47366
matplotlib/rcsetup.py,sha256=RGPzT0gkTqfj4rFjsK_oTt0w00c2CeWEtvUuCqRvfeg,49845
matplotlib/sankey.py,sha256=QIklfaHYh53EmWyUTsPPekoWkI_w4wIyhrHTgenn9_I,37519
matplotlib/scale.py,sha256=BAeWoxm6dW6y73DAaPDkTlnO4Xsygsfg0Z1DT9EdspQ,26238
matplotlib/sphinxext/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/sphinxext/__pycache__/__init__.cpython-38.pyc,,
matplotlib/sphinxext/__pycache__/mathmpl.cpython-38.pyc,,
matplotlib/sphinxext/__pycache__/plot_directive.cpython-38.pyc,,
matplotlib/sphinxext/mathmpl.py,sha256=Bcn7RVQoltsgGR_1E7mfr-mXLUklW8bwBJQig5dapd0,7692
matplotlib/sphinxext/plot_directive.py,sha256=VYNBgiimGlZklrUaoXnXlL-W2JtHR72D2qgLy048644,29629
matplotlib/spines.py,sha256=tKzgUT_3jqkS1VkxI7tKnIV5SGufZKk-y4COq2ZXD8o,21909
matplotlib/stackplot.py,sha256=B717EOSLe7rcNwhLD2CiK6GxX0Rqj1bhxPFmjsVtfI4,4316
matplotlib/streamplot.py,sha256=w4BEXYzJ_4MdoWGHdh3-iIXu1qQ695Mq71c9YrHQBYg,24464
matplotlib/style/__init__.py,sha256=Qy5tpx50U6SjO0yI5Szmzv601Nh52sYVc5dhWD9g4ik,144
matplotlib/style/__pycache__/__init__.cpython-38.pyc,,
matplotlib/style/__pycache__/core.cpython-38.pyc,,
matplotlib/style/core.py,sha256=1UC3B-LGqz_z9dY1hNcdwTGveGv0rMJ6sy_tOEu2euY,10188
matplotlib/table.py,sha256=MugpmyPf1rMIsDljXVGdR2HSsFb1-55r6KkTpYy5f8Y,27798
matplotlib/testing/__init__.py,sha256=ZIqPtuilhC83DXBF905NCVgU8FRoHAU4DFGRUM0gnT8,3347
matplotlib/testing/__pycache__/__init__.cpython-38.pyc,,
matplotlib/testing/__pycache__/_markers.cpython-38.pyc,,
matplotlib/testing/__pycache__/compare.cpython-38.pyc,,
matplotlib/testing/__pycache__/conftest.cpython-38.pyc,,
matplotlib/testing/__pycache__/decorators.cpython-38.pyc,,
matplotlib/testing/__pycache__/exceptions.cpython-38.pyc,,
matplotlib/testing/__pycache__/widgets.cpython-38.pyc,,
matplotlib/testing/_markers.py,sha256=h0soMvgi2SUHtyOswDGMxEHlzxRCzZAsUELsYhieER4,1460
matplotlib/testing/compare.py,sha256=24TybHUDvLGly4xovRghLSJ0_mQNp4CjcO5XxgvU3PY,19626
matplotlib/testing/conftest.py,sha256=SfopgF9SRXbQdsPKDcuwvrrXuUSXeLHTjpwPW5R8ikA,3702
matplotlib/testing/decorators.py,sha256=MyGPfxvmSL-UsRqenxGqryPSQqgB1OxEYHEnmL60VYU,20827
matplotlib/testing/exceptions.py,sha256=rTxMs5B6lKjXH6c53eVH7iVPrG5Ty7wInRSgzNiMKK4,142
matplotlib/testing/jpl_units/Duration.py,sha256=aITDzCvB54NyqdMGT7iFde24Do5CTRWxhV_A0wojMOI,4112
matplotlib/testing/jpl_units/Epoch.py,sha256=TdD79pAGbsvrpSmkX47tLiCugdwHcUUXmIrPs1fvaSE,6315
matplotlib/testing/jpl_units/EpochConverter.py,sha256=bQ9_hqV0b6RYi31Uq6vC0x8pW5mTFxeZxuVyxzb_Mps,3154
matplotlib/testing/jpl_units/StrConverter.py,sha256=ONpRXI3jspcTwwb5YgM687c-qMuUHsEGlqMBGfAQp9w,2962
matplotlib/testing/jpl_units/UnitDbl.py,sha256=HsWiAqtL8Czz1cbPtosyga_oOvN5SBi1H_O8bKM1Ltc,6070
matplotlib/testing/jpl_units/UnitDblConverter.py,sha256=2Kj__EnyhQVuFjrdQ1fBL89ewi4vcKwD4UpG0zcJv5c,2913
matplotlib/testing/jpl_units/UnitDblFormatter.py,sha256=3ZrRTulxYh-fTRtSpN9gCFqmyMA_ZR2_znkQjy3UCJc,709
matplotlib/testing/jpl_units/__init__.py,sha256=gdnES2cnASttTSfKQn-g400gQ0dDZAQ_Kn09J_HyFJY,2760
matplotlib/testing/jpl_units/__pycache__/Duration.cpython-38.pyc,,
matplotlib/testing/jpl_units/__pycache__/Epoch.cpython-38.pyc,,
matplotlib/testing/jpl_units/__pycache__/EpochConverter.cpython-38.pyc,,
matplotlib/testing/jpl_units/__pycache__/StrConverter.cpython-38.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDbl.cpython-38.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDblConverter.cpython-38.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDblFormatter.cpython-38.pyc,,
matplotlib/testing/jpl_units/__pycache__/__init__.cpython-38.pyc,,
matplotlib/testing/widgets.py,sha256=EBEoATQH_du9YySK2zbhqxQT7CXUWO4xjowVA0Gw8g0,3587
matplotlib/tests/__init__.py,sha256=y2ftcuJhePrKnF_GHdqlGPT_SY-rhoASd2m4iyHqpfE,376
matplotlib/tests/__pycache__/__init__.cpython-38.pyc,,
matplotlib/tests/__pycache__/conftest.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_afm.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_agg.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_agg_filter.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_animation.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_api.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_arrow_patches.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_artist.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_axes.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_axis.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_bases.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_cairo.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_gtk3.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_macosx.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_nbagg.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_pdf.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_pgf.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_ps.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_qt.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_svg.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_template.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_tk.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_tools.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backend_webagg.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_backends_interactive.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_basic.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_bbox_tight.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_category.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_cbook.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_collections.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_colorbar.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_colors.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_compare_images.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_constrainedlayout.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_container.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_contour.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_cycles.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_dates.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_determinism.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_doc.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_dviread.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_figure.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_font_manager.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_fontconfig_pattern.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_ft2font.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_getattr.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_gridspec.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_image.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_legend.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_lines.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_marker.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_mathtext.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_matplotlib.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_mlab.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_offsetbox.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_patches.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_path.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_patheffects.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_pickle.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_png.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_polar.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_preprocess_data.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_pyplot.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_quiver.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_rcparams.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_sankey.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_scale.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_simplification.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_skew.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_sphinxext.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_spines.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_streamplot.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_style.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_subplots.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_table.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_testing.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_texmanager.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_text.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_textpath.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_ticker.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_tightlayout.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_transforms.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_triangulation.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_ttconv.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_type1font.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_units.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_usetex.cpython-38.pyc,,
matplotlib/tests/__pycache__/test_widgets.cpython-38.pyc,,
matplotlib/tests/conftest.py,sha256=Ugt97ieMRXmHAlCF7_m0Gh4Zm_Orl2oaJmTMXLtdljo,121
matplotlib/tests/test_afm.py,sha256=OKC6K8p3Mgxb1i-ivRniJx-BGIDqg2vzajogNS1XkAE,3838
matplotlib/tests/test_agg.py,sha256=cmatMgOhm-WPhpcQTsmLAzoCd9KDlfV_H0tjAZtnQ64,11080
matplotlib/tests/test_agg_filter.py,sha256=K7Fo8HvgYG_5NA4HbbNJroWqZfmOoqD6u0Bc7PtKQok,1100
matplotlib/tests/test_animation.py,sha256=Ysx6NJyeqqufp3JoclMVchEYARD8jXP2-TdSmVHMJD0,17117
matplotlib/tests/test_api.py,sha256=vhRLhkXvxnvetV7-XeRbMkGKS-EnbF90u3k3BStZOUY,2935
matplotlib/tests/test_arrow_patches.py,sha256=JYOEeYAOHAh6sQBj5z2XcUMfI_pJTafmnZxbTqlqHsc,6611
matplotlib/tests/test_artist.py,sha256=Q2tQV64qC1ijxEyXcafmsEvHXm2x9ENryBW5nKPYYgk,18005
matplotlib/tests/test_axes.py,sha256=DOi6OHhNAuotKURqPDjxXtw3QGcJIh9NwfZiw7tTs0I,293190
matplotlib/tests/test_axis.py,sha256=g18MO2TI-ELkccOwEw4FK09T4fPXEH_OmheJky2qYro,278
matplotlib/tests/test_backend_bases.py,sha256=8ZRVm2kOBNFCKpPy90lvZdB4Wj7FDCTLNZKBNZnB97k,16154
matplotlib/tests/test_backend_cairo.py,sha256=2jejOSazlpDheGuSBe2C6R2D37bMjmMy3Ds2NRCzFnA,1869
matplotlib/tests/test_backend_gtk3.py,sha256=WVzEGNm35cqvORI5_im91hTYowe3_ijrbqB3lmiJvto,1835
matplotlib/tests/test_backend_macosx.py,sha256=1Zm-qknGyeej4P10oYXLh4QTNDyJuFN_ArsqX4EGKaI,1587
matplotlib/tests/test_backend_nbagg.py,sha256=SHiafOKvwXZYJcJ1MIdIM_EbwPQngjKTIE9vST4xNGo,1003
matplotlib/tests/test_backend_pdf.py,sha256=6d8QHFXDU2WhK0XOlhw5AX0rLbTEwaNfA8ayoC86zns,14906
matplotlib/tests/test_backend_pgf.py,sha256=SWszcYD-t1hEc7GaPKTXxAjCskKYivCK2jiJOKYrVr8,12297
matplotlib/tests/test_backend_ps.py,sha256=rbkHpog6TvhykorxgyzzqVJzmNEi9vToGKhUOPw6Ivs,11078
matplotlib/tests/test_backend_qt.py,sha256=qEiKllY54uZ9mQNkv8kLwA4guMxD5RyezghO78Hcd5I,21896
matplotlib/tests/test_backend_svg.py,sha256=53fA0v_BD0DQ7kxhAnSWZDJViiM5J4zGXXdiGNeFf5w,21705
matplotlib/tests/test_backend_template.py,sha256=OlGU7YZS68Xew_R2tOX5v7UK0FLGqlOpO8bv098_zQY,1832
matplotlib/tests/test_backend_tk.py,sha256=vOu0F5hMMTLPLFBlKFpRpIOiH0Xu_K1kuHxL87fA0BQ,8587
matplotlib/tests/test_backend_tools.py,sha256=6StNyy5SGVdfh1r-UwXyQnMg_XJCiXL_hC40HehT-zA,521
matplotlib/tests/test_backend_webagg.py,sha256=CwQn3cfhSf3fttBphizjHYD-uTyrL9uQUtMSR6lWd7A,926
matplotlib/tests/test_backends_interactive.py,sha256=z-bgTT_XQsyrQnBU6l_iJpzOuvQGbjT7peD2bc_pRF0,23810
matplotlib/tests/test_basic.py,sha256=_x4D9C-qMIgkgbSHY-QdjStZxHEw-lRd_-U5oMJfYF0,1097
matplotlib/tests/test_bbox_tight.py,sha256=0mUUdh3ZtLnXvq9Rc6ZAGHPqtY7jj0G-AV5WuqT4YzM,5743
matplotlib/tests/test_category.py,sha256=gjX3HFntVdEzFzpQKmBqhN8Yf3eAiGs4ARwT326gW7A,12023
matplotlib/tests/test_cbook.py,sha256=TBfbt9k3z0A7i23E8-o0PfVnJIgp8CTZ2gFVgzXC9ic,30882
matplotlib/tests/test_collections.py,sha256=R0BmLh86Dx6C4Wb_YgGeA9i_CKK5Oz3PEa_JU22pr5E,42862
matplotlib/tests/test_colorbar.py,sha256=K8WGUR-wWcoocvLJlgWZOSvdZav0Nj4WokGEt9UW1gE,46780
matplotlib/tests/test_colors.py,sha256=0QlrZImKzyH9Zk3aAP8W_fi8CPYOPr5BzDzd1P_-xj4,57885
matplotlib/tests/test_compare_images.py,sha256=Q9wqpMYzRKeV9b7qjWTKb2VCp0oywSZG5CboiYS797A,3333
matplotlib/tests/test_constrainedlayout.py,sha256=kcE72B_Itu5Bbocd592qaSHcELwARSRDmA2uhI1WmxM,23353
matplotlib/tests/test_container.py,sha256=QgNodtC50-2SyP_8coGyp1nQsmLKhfivlJrfX4cujds,580
matplotlib/tests/test_contour.py,sha256=CcnrVdKivgXrwMEVUPtq5i2zqAfsUgo8n3oEJJvKxHw,26382
matplotlib/tests/test_cycles.py,sha256=7SnFRFaoAB9B77dtNkiDCBwr6QsQA4sSOQZxkzYZmYs,5820
matplotlib/tests/test_dates.py,sha256=WMXs_kJMsFN_MAoCnnTWvrPVFFbbs_xznBleOHgf-g4,58583
matplotlib/tests/test_determinism.py,sha256=bERlpej5nZG8AI90L8kjOykOqnFeeeqnZhVbFmBAd0U,4602
matplotlib/tests/test_doc.py,sha256=P-Qcj5epwqpzlLRF7rQY-maY01VBWV7mf1OooXZkKjI,1006
matplotlib/tests/test_dviread.py,sha256=o2LCbnAWjpspxcNvwtnjxEo0hDoT3Z-96RtjbO7TdGo,2840
matplotlib/tests/test_figure.py,sha256=8opAtapIsK_EHpOlYXf3xhBpKCzynCpvOYLkKpJVkjc,53830
matplotlib/tests/test_font_manager.py,sha256=9CBymvlJjj6BxKIUzSYjJ0JUbtBswA9o5czr6nSDnPA,12056
matplotlib/tests/test_fontconfig_pattern.py,sha256=cNQ-Sw85DMUYx_DOLxf0n6mtF5JRonTur7G6VZrurSk,2228
matplotlib/tests/test_ft2font.py,sha256=WaNtb43w6XsN15hYS8BtowFqhv7XVkHeub88M2GUuxI,3836
matplotlib/tests/test_getattr.py,sha256=yAtSNLVzvT__esBKZkrybenHXYk_A-i2Q6YqyQjmJVQ,1062
matplotlib/tests/test_gridspec.py,sha256=JiyMWPmsW_wJc7m4f8b2KRqCKlBSSlUt3LUyhak0heM,997
matplotlib/tests/test_image.py,sha256=932qVmHuV92Y6YkNFb4KHT_sI5inpIYoDTeOab7iorI,49413
matplotlib/tests/test_legend.py,sha256=psEyz5dQaLLBL3SB9O1kRBRJAAeYJuwlDtyezLy4AV0,46052
matplotlib/tests/test_lines.py,sha256=7ilyy6EOowl2ibsrXC39AZ_1p4muRwtVKKbkh6MdsaQ,13836
matplotlib/tests/test_marker.py,sha256=d_QP22lgSSUU00mtdKTT-FcTvH8N6GuakYdImXQBaSI,11600
matplotlib/tests/test_mathtext.py,sha256=qDUyBrjGUXA2gBwkkULsY95Nw0Vlj0F28xWrhRIriqw,22812
matplotlib/tests/test_matplotlib.py,sha256=IsWT0O1oQX6f5MXHvHiarxHPDXE_KjoB1YNVEF8p9t0,2705
matplotlib/tests/test_mlab.py,sha256=rAYTJI5tnuhXV_AFdMimXM1wagD3HiD6JddaNSnFtS4,46223
matplotlib/tests/test_offsetbox.py,sha256=aQzkMjiNmkbEXncJi_ui7Lxb9VtzN3uqEBgAG-0TEFc,14249
matplotlib/tests/test_patches.py,sha256=Icf-7D1iPhtnF6OlDZRk4Tnwe7aaA8zBXB-r0eLr21c,30445
matplotlib/tests/test_path.py,sha256=2rwYMSWFrZvQJvTWAmcZhUlmtky62W7VBto87p1SMg0,18330
matplotlib/tests/test_patheffects.py,sha256=ynF_YlBm-DOLlcbKm23rsXaPfI-YrECdJ-4xMnRm9B8,7791
matplotlib/tests/test_pickle.py,sha256=wEy0K5DkHamKY-Jhc7NKxLvkYe5htJRvCSGjXinQWVM,8525
matplotlib/tests/test_png.py,sha256=uDkdjD0bmaEJHNOW_PVZanR5WRyQ_10H6ncYaE8lF-c,1290
matplotlib/tests/test_polar.py,sha256=JOkQkKPQc_f8DjwQvpkeBg9nKbQ_Sd--dKpsRzO-ngQ,15733
matplotlib/tests/test_preprocess_data.py,sha256=-BQLwq8JUVTXIGEX8ycfVnDozYFfZEy19EwJhyOdXz4,11659
matplotlib/tests/test_pyplot.py,sha256=6LDCRib6GMzKM7j7QUMNE-c_4VYEzKEkOnD4VhADuAY,13491
matplotlib/tests/test_quiver.py,sha256=2Q2S2bZqqyoLucmCzW0UsdqNidSKI1MGHt1eH0VCMk8,8810
matplotlib/tests/test_rcparams.py,sha256=zISXGYJ15tzRZWMe4_gFAmJRwU04Bt9eOQAk1rUBPuo,24012
matplotlib/tests/test_sankey.py,sha256=nM5pfE0o73ULc9g1B6jzKkombH0v33GmGqrF7AXGEXc,4005
matplotlib/tests/test_scale.py,sha256=BPJkUJqty_oK18kMEZj1IYM9OJ9H8WpqOUiUbG06AuY,8706
matplotlib/tests/test_simplification.py,sha256=U83U2WwLe3op5skvwGps6HcAjK9u_TiUHNg4mStRLws,19302
matplotlib/tests/test_skew.py,sha256=VWsvEHjlOWkheJYE2j82GvkYPW_3UBdsUA_1CWMbMn0,6435
matplotlib/tests/test_sphinxext.py,sha256=knpgf3drhgfO1EGVan64j7JoawX-Qph2oZbm58Lmlew,8180
matplotlib/tests/test_spines.py,sha256=iCIut707yfr7uUISgPbRrumacQVjGqO1uLc8mjjUq8k,4877
matplotlib/tests/test_streamplot.py,sha256=6x4BLssLt1scm6mBgbmFaIynx8J5WtFLkfS7LR1X4ks,5892
matplotlib/tests/test_style.py,sha256=c33fO5bz9XzulMQ54NMA2lbl8-Y0LNpHLuYBmFw9wS8,7190
matplotlib/tests/test_subplots.py,sha256=e6wBRj8GyYIOOdcIYD2GqvQ38WbACcmVymMsLYuQ524,9960
matplotlib/tests/test_table.py,sha256=sAwCfAE-loy9Ag5Q5L03BAzsiwb7tNCod17J5kt_zPI,6773
matplotlib/tests/test_testing.py,sha256=br5I_Hld4xKgSeWCoFtN60RD-xfDXyKSlNjdYVb_gkY,1098
matplotlib/tests/test_texmanager.py,sha256=cxsxx_jl2vnFxhJifcIftCafOkBDTPmPAT4wTlrzhFI,2669
matplotlib/tests/test_text.py,sha256=MI4As6CQtVWcwhrNIPOKQ_n_QfX_Q9cmvaG8wMYdmDE,29982
matplotlib/tests/test_textpath.py,sha256=WxloBscSvbSgE_BQEGwezHYccdJh7qWI83u_xizisXI,281
matplotlib/tests/test_ticker.py,sha256=ZxdWqc_z4kpRpNw0aWoiA37DSKncAZfhgxTjvFcdFbI,60498
matplotlib/tests/test_tightlayout.py,sha256=Jme3JjkZrVVYGldXrVyWRSflKLD3rAw9264gPvlaanA,13097
matplotlib/tests/test_transforms.py,sha256=UUQpIqNzDrUGBu11YEn5Vb2ll1qRNqjP77GusGj-RGw,30536
matplotlib/tests/test_triangulation.py,sha256=ygaFqVeV8EKeXOMLQ8HGj0EZ4pOSR2QUrBGbc2JhJCs,53799
matplotlib/tests/test_ttconv.py,sha256=MwDVqn8I2a09l7sN9vfluhABCpzoPasZOzrvoadYn8o,557
matplotlib/tests/test_type1font.py,sha256=yaW0JGzWTMgfAMi4xaJp_U764hkZArwZ3tyVQtIaFTI,6529
matplotlib/tests/test_units.py,sha256=7vPxOn0RbO6_QidqCYVwP5LZupLoZFkCiWC4-aSUt_8,9591
matplotlib/tests/test_usetex.py,sha256=U482VsGU0miF6Tz9rdrMcKm7bD9_VNFs8sr-2juX7aY,5241
matplotlib/tests/test_widgets.py,sha256=wUaLxyCMPf0juRN4q2TtbSfGV1mRDkBIqgsX3VH8fx0,67280
matplotlib/texmanager.py,sha256=4YmCbAPZ-piPzOaH7P3dY0COk6602wtoHRCToFqcbrM,15778
matplotlib/text.py,sha256=0WUK0jkacQ4bLxwfI-ocPZF0WDUS-feXqkhWFe31G8U,73001
matplotlib/textpath.py,sha256=bUyrF_LnqYAENeWA0zZVDiPJKej3B7qMHY2bYkWUrdc,13958
matplotlib/ticker.py,sha256=SsmtOIlAqHXx6UcTQzT53Bk6qXX6mR1LJwy153sUmuQ,107614
matplotlib/tight_bbox.py,sha256=br64Xl8KL79JEVyImw52l0eZpgZ6v_MKtT5Epzsybqc,150
matplotlib/tight_layout.py,sha256=9awpnqWXV-9ZuESv8uH7L1myAmlYuxd-jpV8iZLHld0,461
matplotlib/transforms.py,sha256=hoezF9iX1BIAKSQ8SD0GZrQ_sxPDRxYDBL_q0DX3iac,102573
matplotlib/tri/__init__.py,sha256=mLV09CniPqAr8zTTl80n1BFI9j4TVlBO49zP5mqcRZo,843
matplotlib/tri/__pycache__/__init__.cpython-38.pyc,,
matplotlib/tri/__pycache__/_triangulation.cpython-38.pyc,,
matplotlib/tri/__pycache__/_tricontour.cpython-38.pyc,,
matplotlib/tri/__pycache__/_trifinder.cpython-38.pyc,,
matplotlib/tri/__pycache__/_triinterpolate.cpython-38.pyc,,
matplotlib/tri/__pycache__/_tripcolor.cpython-38.pyc,,
matplotlib/tri/__pycache__/_triplot.cpython-38.pyc,,
matplotlib/tri/__pycache__/_trirefine.cpython-38.pyc,,
matplotlib/tri/__pycache__/_tritools.cpython-38.pyc,,
matplotlib/tri/__pycache__/triangulation.cpython-38.pyc,,
matplotlib/tri/__pycache__/tricontour.cpython-38.pyc,,
matplotlib/tri/__pycache__/trifinder.cpython-38.pyc,,
matplotlib/tri/__pycache__/triinterpolate.cpython-38.pyc,,
matplotlib/tri/__pycache__/tripcolor.cpython-38.pyc,,
matplotlib/tri/__pycache__/triplot.cpython-38.pyc,,
matplotlib/tri/__pycache__/trirefine.cpython-38.pyc,,
matplotlib/tri/__pycache__/tritools.cpython-38.pyc,,
matplotlib/tri/_triangulation.py,sha256=jxns99lw5efHgkmBFaRXopqSI7CJq7lvzx79mw5Px-4,10023
matplotlib/tri/_tricontour.py,sha256=jflJqQ5fQS4mdLDtDb3uEq5MfSkNFqR57AEFdupXKqM,10590
matplotlib/tri/_trifinder.py,sha256=v4nqw4KD1_obgpk8p_lW3HtuSfRIs_SjF7RfuZg9NcA,3550
matplotlib/tri/_triinterpolate.py,sha256=9kJp4OyWVYx6ticCWJWtGmwgt6OI78BugHi5pDzYb3M,64067
matplotlib/tri/_tripcolor.py,sha256=qz_jfLCWQxilHjdKy5TRq7Nk1bYXd-xhKPeEAM--3WM,6693
matplotlib/tri/_triplot.py,sha256=PAwE1WeJLE33kPEMw2HS3yS-P5gKWYl_eD_hUKtlu2U,3188
matplotlib/tri/_trirefine.py,sha256=SQDa-gac2OS8_FqNOndoFr8BMzBbnp6MOY1IS4cKae8,13499
matplotlib/tri/_tritools.py,sha256=6fxeI2QQy3iYZVuJX_-O7hch1yuEoS1NtDbbvgwssxk,10838
matplotlib/tri/triangulation.py,sha256=iJm0pYRWpVtnOel-eT8NA2hJZt_7EOfZXqi-QQhh278,341
matplotlib/tri/tricontour.py,sha256=oQP8ILzxmYP4bBo4Wl3OOKSq7CC4wBn9WvTm2s8aZx8,338
matplotlib/tri/trifinder.py,sha256=03C7BhJ8AYFQw34emdQ0yEo9leXuyGK2ykqjXqb7Y6s,337
matplotlib/tri/triinterpolate.py,sha256=e2QXG0TYA3I59RVSvjH26j3P0UOuwjlgpHvfca8wj9U,342
matplotlib/tri/tripcolor.py,sha256=sWmlJKZRznL_yUmJK-cC6a5WtnP1jra9NGUhstomaaM,337
matplotlib/tri/triplot.py,sha256=0OcYat6CYdf1B8gGmP0v_HxNkYJzUbMtEOZtZJ1V8xU,335
matplotlib/tri/trirefine.py,sha256=eu83KwX1D75vmsL0Q9lj_KuXFk6kHiV5VspaFFNDulg,337
matplotlib/tri/tritools.py,sha256=OmWskCzkBcOkzveb6WyjA99M6GET1FIbik5x0nahvtU,336
matplotlib/type1font.py,sha256=tQ7Yn7sP7lOpVzrGhB8y2x4hRa-30KPERKldMj0h_Go,149
matplotlib/units.py,sha256=XUuAicXmf2Tvx68wjYThGNjMszsqJXzm-ok64a1qr4I,6624
matplotlib/widgets.py,sha256=D-XDjOyN-UhSkjXTRIk_ijwkU83L8aoEbPDtcpZErBk,160405
mpl_toolkits/__init__.py,sha256=SbCZGi15cPEMzq9fCu1Y4jhOJ4YRB8uhbOl_HBFswgs,1378
mpl_toolkits/__pycache__/__init__.cpython-38.pyc,,
mpl_toolkits/axes_grid1/__init__.py,sha256=ZX_SklWuH-vR9VT4taFQQGqeZ0NJ1R2lSwAf9KMCyoE,381
mpl_toolkits/axes_grid1/__pycache__/__init__.cpython-38.pyc,,
mpl_toolkits/axes_grid1/__pycache__/anchored_artists.cpython-38.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_divider.cpython-38.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_grid.cpython-38.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_rgb.cpython-38.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_size.cpython-38.pyc,,
mpl_toolkits/axes_grid1/__pycache__/inset_locator.cpython-38.pyc,,
mpl_toolkits/axes_grid1/__pycache__/mpl_axes.cpython-38.pyc,,
mpl_toolkits/axes_grid1/__pycache__/parasite_axes.cpython-38.pyc,,
mpl_toolkits/axes_grid1/anchored_artists.py,sha256=1lczKUqAI7qe25YI9vJYI_9-qAIyNME1P-eZub5xpiI,19642
mpl_toolkits/axes_grid1/axes_divider.py,sha256=d1R_5VtUBixmLk2sBTilRX3cxgGRKexsxMCi-oLiYt8,23381
mpl_toolkits/axes_grid1/axes_grid.py,sha256=YHs_G5xu2AsjVivZ1F5wu4JkMcLvT7Wttt4SddE5ajI,22740
mpl_toolkits/axes_grid1/axes_rgb.py,sha256=w29XHoz2KQXUl7MyNpKdu1UKfjulHIL6udQJpLL4U5A,4864
mpl_toolkits/axes_grid1/axes_size.py,sha256=eHMenG2dKipUIuYTAKUs7Tj2WpHmuoZwwFPTw5YrVKw,9005
mpl_toolkits/axes_grid1/inset_locator.py,sha256=4kjGUWXHw9OUk3DJ7eDcckIgTSvrHHOaedwUcesNKes,22348
mpl_toolkits/axes_grid1/mpl_axes.py,sha256=e_nmULw6dNfWiuf17dcFvyCXz3LB4nTe7m1W9Ad_K4g,4379
mpl_toolkits/axes_grid1/parasite_axes.py,sha256=KAzLuy76mIr4MJ1tuAqoIB7logdLueXdfGdnvefASkA,9614
mpl_toolkits/axes_grid1/tests/__init__.py,sha256=jY2lF4letZKOagkrt6B_HnnKouuCgo8hG3saDzq8eGI,375
mpl_toolkits/axes_grid1/tests/__pycache__/__init__.cpython-38.pyc,,
mpl_toolkits/axes_grid1/tests/__pycache__/conftest.cpython-38.pyc,,
mpl_toolkits/axes_grid1/tests/__pycache__/test_axes_grid1.cpython-38.pyc,,
mpl_toolkits/axes_grid1/tests/conftest.py,sha256=qP2IfI9HHjCAss4IpCIInk4F3NR475nz4A32LlBdUIM,149
mpl_toolkits/axes_grid1/tests/test_axes_grid1.py,sha256=dVC1NS7kI8yvVHIPBTSr1lsi_gz0B5rX8z0rQjdN8uM,29637
mpl_toolkits/axisartist/__init__.py,sha256=P32y8zmXICvtBHk2bpeIFWKv8Dz5_khS4vCMLHHBXfA,566
mpl_toolkits/axisartist/__pycache__/__init__.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/angle_helper.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_divider.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_grid.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_rgb.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/axis_artist.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/axisline_style.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/axislines.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/floating_axes.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/grid_finder.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/grid_helper_curvelinear.cpython-38.pyc,,
mpl_toolkits/axisartist/__pycache__/parasite_axes.cpython-38.pyc,,
mpl_toolkits/axisartist/angle_helper.py,sha256=iP-uSrgvdqMBA7iHE_iQkUhTr2T5Og0KDKndp8DQro8,13346
mpl_toolkits/axisartist/axes_divider.py,sha256=5pPQ8b_til8k9-d6j8Vmi0kjuR2mqeNMX0_R2wD9UE4,137
mpl_toolkits/axisartist/axes_grid.py,sha256=aOEGpmfnQwFdxDIcWmhniKLJQJD0mFchFSKQQ65AcJU,261
mpl_toolkits/axisartist/axes_rgb.py,sha256=9_5OG-7aErZGNg8H63PsGX_8r8SazDm2ryqXOn0DPI0,306
mpl_toolkits/axisartist/axis_artist.py,sha256=zc4grfgLSyfO0_0CGIaW3Ck7WJkII0k1od3P8uh4SfI,39272
mpl_toolkits/axisartist/axisline_style.py,sha256=Ex6PQQ4v4IX-hqmYajgH8JHwC406q4dFww78TowL-nk,6905
mpl_toolkits/axisartist/axislines.py,sha256=azJaebp2OvM63ohtE49SjvrducKfEn8M-6cv6Z7EkJQ,20323
mpl_toolkits/axisartist/floating_axes.py,sha256=busJ-6PNyLfbI8GIwC83LHq1ulFXYrYvR4fHp6fIoQA,11083
mpl_toolkits/axisartist/grid_finder.py,sha256=HiaJzVSgIiHnecX8K5UgRpYpMeQKSrGN_HEIFObXaS4,12611
mpl_toolkits/axisartist/grid_helper_curvelinear.py,sha256=d8fii3AakEbNzKYd7HHGoGCwMuJhiSOi9qeQrDQfB_g,13063
mpl_toolkits/axisartist/parasite_axes.py,sha256=rxl0rH0c2zmp1DR7W0J4T9dWlJXirZveEWkD7m2dXW0,251
mpl_toolkits/axisartist/tests/__init__.py,sha256=jY2lF4letZKOagkrt6B_HnnKouuCgo8hG3saDzq8eGI,375
mpl_toolkits/axisartist/tests/__pycache__/__init__.cpython-38.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/conftest.cpython-38.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_angle_helper.cpython-38.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_axis_artist.cpython-38.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_axislines.cpython-38.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_floating_axes.cpython-38.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_grid_finder.cpython-38.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_grid_helper_curvelinear.cpython-38.pyc,,
mpl_toolkits/axisartist/tests/conftest.py,sha256=qP2IfI9HHjCAss4IpCIInk4F3NR475nz4A32LlBdUIM,149
mpl_toolkits/axisartist/tests/test_angle_helper.py,sha256=SI_lyCLbVKikZp9DRBpq--MbfT10vSQsNx1Z5HMeqMw,5811
mpl_toolkits/axisartist/tests/test_axis_artist.py,sha256=Q60eL7y9rIjZ234U5v4TQ5P5jY7OB5PsaHtgqkiE4FY,3081
mpl_toolkits/axisartist/tests/test_axislines.py,sha256=uhYSB7T9sFmCcaq7hQ1ycvWoIY9MJPlQ82BVIkUYJuU,4616
mpl_toolkits/axisartist/tests/test_floating_axes.py,sha256=MxvuALWWWB0SMhGedIjgut8mhbL1WItUKBIRPitOxzg,4062
mpl_toolkits/axisartist/tests/test_grid_finder.py,sha256=7a23BnKQOj3Zzmm7fA0NYOyzlWJDc1mIX7IZi9pDV10,1190
mpl_toolkits/axisartist/tests/test_grid_helper_curvelinear.py,sha256=zDMJ1RpQ4BO9_NlJNXAz9CBr_d5qSOCdpL-AD9m5a2k,7477
mpl_toolkits/mplot3d/__init__.py,sha256=vedj7_W04csdRPSDwBkZb3Hu3vucJFQieeiXEPDIGo4,52
mpl_toolkits/mplot3d/__pycache__/__init__.cpython-38.pyc,,
mpl_toolkits/mplot3d/__pycache__/art3d.cpython-38.pyc,,
mpl_toolkits/mplot3d/__pycache__/axes3d.cpython-38.pyc,,
mpl_toolkits/mplot3d/__pycache__/axis3d.cpython-38.pyc,,
mpl_toolkits/mplot3d/__pycache__/proj3d.cpython-38.pyc,,
mpl_toolkits/mplot3d/art3d.py,sha256=NNbcRhuWowmyfeycn4QocjPrB_-Ri427-mIWru-oy6s,42783
mpl_toolkits/mplot3d/axes3d.py,sha256=eH9D9GPMmO0igHhd5uvNx3rKjIH4QDKDKCT6p6Rw9gY,129166
mpl_toolkits/mplot3d/axis3d.py,sha256=8aBhWOGdHHiFSfUt8mjJ0YRjrAq9hKL9xDt-1agmxYo,24289
mpl_toolkits/mplot3d/proj3d.py,sha256=Hky2ex1ah7mFcxUn_ir-AR0cQDlJHhokHVzanilcwfg,7181
mpl_toolkits/mplot3d/tests/__init__.py,sha256=jY2lF4letZKOagkrt6B_HnnKouuCgo8hG3saDzq8eGI,375
mpl_toolkits/mplot3d/tests/__pycache__/__init__.cpython-38.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/conftest.cpython-38.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/test_art3d.cpython-38.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/test_axes3d.cpython-38.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/test_legend3d.cpython-38.pyc,,
mpl_toolkits/mplot3d/tests/conftest.py,sha256=qP2IfI9HHjCAss4IpCIInk4F3NR475nz4A32LlBdUIM,149
mpl_toolkits/mplot3d/tests/test_art3d.py,sha256=I1z7YnPDILpIb5J9K7BBG_beynotSZizNNMKIZHrXU0,1873
mpl_toolkits/mplot3d/tests/test_axes3d.py,sha256=LYrbtImTH6opa-eWWHEhc5sA4HADutK5ewj0gUanOvs,75134
mpl_toolkits/mplot3d/tests/test_legend3d.py,sha256=mAGJb9nZA68IwhuzHtrZ6xQeaj9HHzT7RB5NDZTDMvI,4303
pylab.py,sha256=Ni2YJ31pBmyfkWr5WyTFmS1qM40JuEeKrJhYKWbd6KY,93
