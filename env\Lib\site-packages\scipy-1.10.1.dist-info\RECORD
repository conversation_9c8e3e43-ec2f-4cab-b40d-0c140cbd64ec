scipy-1.10.1-cp38-cp38-win_amd64.whl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy-1.10.1.dist-info/DELVEWHEEL,sha256=RJaRx9wrCF8sbxOO79NxxmkDhaXGeRaoSqVE_rJcFz8,439
scipy-1.10.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
scipy-1.10.1.dist-info/LICENSE.txt,sha256=SJ6k4IhfCNtp1_GyRT28WIcDTc1LHezT-jPAi-Apgt4,46652
scipy-1.10.1.dist-info/METADATA,sha256=C0W1fhnPT386ibPJYZaZB5GSSPmtHHlOMoiQXx54Ro4,58976
scipy-1.10.1.dist-info/RECORD,,
scipy-1.10.1.dist-info/WHEEL,sha256=vIXzP6jLUy4sdmrQppnovVBqmdfNCkEM0I7EHxeJ-zs,83
scipy.libs/.load-order-scipy-1.10.1,sha256=D2bijUoHvPGk9oPrdGMCN3bCWEIEofSb7bL54eB80zk,50
scipy.libs/libopenblas-802f9ed1179cb9c9b03d67ff79f48187.dll,sha256=V9sJz-F0do-0Caa7WlMNTPOI3ZTAWsLPp36ovVojRqY,35559981
scipy/__config__.py,sha256=9WkXETZFZwRbNbNrFUPP6GoZyPstY3xGl6ySR3W_TU8,4912
scipy/__init__.py,sha256=lp5DO7YdhRaAhvP60_pHK-Q17HR8tvXst5ZIXlbBIKk,8619
scipy/__pycache__/__config__.cpython-38.pyc,,
scipy/__pycache__/__init__.cpython-38.pyc,,
scipy/__pycache__/_distributor_init.cpython-38.pyc,,
scipy/__pycache__/conftest.cpython-38.pyc,,
scipy/__pycache__/version.cpython-38.pyc,,
scipy/_distributor_init.py,sha256=JOqSUAVCNcvuXYwzfs41kJFHvhLdrqRfp3ZYk5U71Pc,1247
scipy/_lib/__init__.py,sha256=lcHbxe9IkuoUZjXA8VX6-SYYCREBLq1cqoPo2mR6V1w,367
scipy/_lib/__pycache__/__init__.cpython-38.pyc,,
scipy/_lib/__pycache__/_bunch.cpython-38.pyc,,
scipy/_lib/__pycache__/_ccallback.cpython-38.pyc,,
scipy/_lib/__pycache__/_disjoint_set.cpython-38.pyc,,
scipy/_lib/__pycache__/_docscrape.cpython-38.pyc,,
scipy/_lib/__pycache__/_finite_differences.cpython-38.pyc,,
scipy/_lib/__pycache__/_gcutils.cpython-38.pyc,,
scipy/_lib/__pycache__/_pep440.cpython-38.pyc,,
scipy/_lib/__pycache__/_testutils.cpython-38.pyc,,
scipy/_lib/__pycache__/_threadsafety.cpython-38.pyc,,
scipy/_lib/__pycache__/_tmpdirs.cpython-38.pyc,,
scipy/_lib/__pycache__/_util.cpython-38.pyc,,
scipy/_lib/__pycache__/decorator.cpython-38.pyc,,
scipy/_lib/__pycache__/deprecation.cpython-38.pyc,,
scipy/_lib/__pycache__/doccer.cpython-38.pyc,,
scipy/_lib/__pycache__/uarray.cpython-38.pyc,,
scipy/_lib/_bunch.py,sha256=SpHtgIJdX6_VQ5Zda-9n0HSqxnKX2WLRSX5z23Q-UDo,8343
scipy/_lib/_ccallback.py,sha256=143MXhd6nlanoMHcKQZsADp4R9XWmL99geeWKzq5S6Y,6440
scipy/_lib/_ccallback_c.cp38-win_amd64.dll.a,sha256=OPKVUqi0ly6zQZZgBn_-5QfXfHy5IZYq3w4Rgg_XRCo,1596
scipy/_lib/_ccallback_c.cp38-win_amd64.pyd,sha256=EZgM6u8A2w3Ycwb9ceeo1tKAmejPTcR8wOfNnYqLLbA,66048
scipy/_lib/_disjoint_set.py,sha256=CD8UFMF-KdDAAup4nPM76TSedAYIgrAq8rGVtIL37i4,5711
scipy/_lib/_docscrape.py,sha256=kI7_kfdfwqBn6FoYe40sZ3VvYir-pTcJXdG96BanbXo,22264
scipy/_lib/_finite_differences.py,sha256=Uu28sJ1PNvgW0Y7ESs0voon5AVYtj0QRrbqsVPjoL70,4317
scipy/_lib/_fpumode.cp38-win_amd64.dll.a,sha256=dIbQpqLWRxK2iWsDuj5a3JPu3-PkMZqz7RcHDAYua8o,1548
scipy/_lib/_fpumode.cp38-win_amd64.pyd,sha256=q-kvQI_G6KQR8CgtN7FeZjW4ZqbdbcgkrFuoWDObbuo,15872
scipy/_lib/_gcutils.py,sha256=ULLIREutjgyo8dLsvU2hhI0lDGeu5LqX6557X60xPi0,2774
scipy/_lib/_pep440.py,sha256=y5Oppq3Kxn2dH3EWBYSENv_j8XjGUXWvNAiNCEJ-euI,14556
scipy/_lib/_test_ccallback.cp38-win_amd64.dll.a,sha256=XBDc6HnU1-Ha-lKU-NKQuLaAXCKIcFhcsgZ4Uf7OIjw,1636
scipy/_lib/_test_ccallback.cp38-win_amd64.pyd,sha256=taMvzmTe-YNZn0ru35k4YOXJACSxh9xR4WSG4zfDiQw,52224
scipy/_lib/_test_deprecation_call.cp38-win_amd64.dll.a,sha256=09_lzuM_qp_TahqOmdpUgE5hoRtOqhlIWGZJuEBXlt4,1716
scipy/_lib/_test_deprecation_call.cp38-win_amd64.pyd,sha256=RtUhtczlXoOujwxCr52zIpvvGluydY008SAgSZPxTqg,23552
scipy/_lib/_test_deprecation_def.cp38-win_amd64.dll.a,sha256=4UzWB_KbSIOmWd6waeP5nm1nJEdxp6i3Dogd1UiZeYc,1708
scipy/_lib/_test_deprecation_def.cp38-win_amd64.pyd,sha256=VeZ1gRyf9VXbE8kZBngVAetmrv51tVqU6XRWB4SWNUk,24576
scipy/_lib/_testutils.py,sha256=aKFTvb6BD6o2NQ5grAnkVxYp_UvMw4Qslol4iLTkm5g,7058
scipy/_lib/_threadsafety.py,sha256=2dkby9bQV_BrdlThHUfiqZJsQq-Mte5R28_ueFpNikA,1513
scipy/_lib/_tmpdirs.py,sha256=r8sz8dcfBYiPAYondvpGHVVFB2_rB5sajkTeV1gt1fw,2460
scipy/_lib/_uarray/LICENSE,sha256=5MSswx6ChwZtfJldJuPvkCyhl37hh7JgsRwng2ZQOBE,1543
scipy/_lib/_uarray/__init__.py,sha256=_rTAltFwgd5_QrYGqs0h5iGLaQaTZWG_YYoBL9z8EPc,4609
scipy/_lib/_uarray/__pycache__/__init__.cpython-38.pyc,,
scipy/_lib/_uarray/__pycache__/_backend.cpython-38.pyc,,
scipy/_lib/_uarray/_backend.py,sha256=gRnJT3vlWmxi5UPPyT1USqteHn8FHzCXZNMy_ChilEk,21201
scipy/_lib/_uarray/_uarray.cp38-win_amd64.dll.a,sha256=1pDfYB9Du7dHx4Tm-M5HeZpce5Ujy1NdozKqSZDBJ2Y,1540
scipy/_lib/_uarray/_uarray.cp38-win_amd64.pyd,sha256=bjRia5JzY7Uv0vumJlBHD8Wi2thF1wC7NjX1ceeQfvA,229888
scipy/_lib/_util.py,sha256=5rbV6HdejLEuq7MDdvJdpb2niPEexcDfUpGG6tCeiPM,25197
scipy/_lib/decorator.py,sha256=Mjb8Lzofjmkf06OMPvS-uEfuLPX-8fxJFPmE_NeLZzw,15458
scipy/_lib/deprecation.py,sha256=kUT4HEY13_PihJrMKChFePj32cRYl0Mhm0kWNtdbvkI,3283
scipy/_lib/doccer.py,sha256=9Ins8j58b2YQkD-vWCH1nMJ7pklhhRWqjELTeiJ-a_w,8637
scipy/_lib/messagestream.cp38-win_amd64.dll.a,sha256=agU4XTQkCVOYqmQJh-JlDOW8ri6fmh3fSAMv_29cgUM,1612
scipy/_lib/messagestream.cp38-win_amd64.pyd,sha256=iylSefPixwgs1RCTbRURqzyLu-_8iSGewWLy6Odo8JQ,46080
scipy/_lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/_lib/tests/__pycache__/test__gcutils.cpython-38.pyc,,
scipy/_lib/tests/__pycache__/test__pep440.cpython-38.pyc,,
scipy/_lib/tests/__pycache__/test__testutils.cpython-38.pyc,,
scipy/_lib/tests/__pycache__/test__threadsafety.cpython-38.pyc,,
scipy/_lib/tests/__pycache__/test__util.cpython-38.pyc,,
scipy/_lib/tests/__pycache__/test_bunch.cpython-38.pyc,,
scipy/_lib/tests/__pycache__/test_ccallback.cpython-38.pyc,,
scipy/_lib/tests/__pycache__/test_deprecation.cpython-38.pyc,,
scipy/_lib/tests/__pycache__/test_import_cycles.cpython-38.pyc,,
scipy/_lib/tests/__pycache__/test_public_api.cpython-38.pyc,,
scipy/_lib/tests/__pycache__/test_scipy_version.cpython-38.pyc,,
scipy/_lib/tests/__pycache__/test_tmpdirs.cpython-38.pyc,,
scipy/_lib/tests/__pycache__/test_warnings.cpython-38.pyc,,
scipy/_lib/tests/test__gcutils.py,sha256=ZFbF7iy2fb0n8kI2YIhOWZnOWQrN1ggT6z97rN0_QzY,3517
scipy/_lib/tests/test__pep440.py,sha256=4AS6UhkhxfVqvZfZmxM0pgIOvQVbAjPdWN3YtgfXWkY,2344
scipy/_lib/tests/test__testutils.py,sha256=cI6oLinJsZDXIi_pmPMTVAYTvepddvhYVGEMd2MlW2Q,832
scipy/_lib/tests/test__threadsafety.py,sha256=nnAypNwYK4YNe2s3xcYfGslLKXqdcrOeza73EeybiAQ,1373
scipy/_lib/tests/test__util.py,sha256=b-R0IMOlcgKsb38crmcXTE5B3qx79xGLstWtNuBDIds,13705
scipy/_lib/tests/test_bunch.py,sha256=eDyepPw1poLF_8ZSGK4xIQSOOvB1ywZMsrIq2aEhjWk,6332
scipy/_lib/tests/test_ccallback.py,sha256=dn4sB3kvhk3oS1WieiK6wmKROHFgvfpBDjGI_UGUuQY,6192
scipy/_lib/tests/test_deprecation.py,sha256=NGUuuv24fSTGyTUZWd2saZkapR2NOpIpx0tijRjBQ7Y,374
scipy/_lib/tests/test_import_cycles.py,sha256=L7Au-vrtCR1C0li72dDW7R0TVDVRF0dfdbAo3aUOSIk,1359
scipy/_lib/tests/test_public_api.py,sha256=Xoppe3nefgvZbBpXDKc_NK4fMOaV4K8yL_Cs8-tvXNk,10268
scipy/_lib/tests/test_scipy_version.py,sha256=j-i3VewqD2gfdZZiJQCdlB3x_4EVekZkND1IFWszqhc,624
scipy/_lib/tests/test_tmpdirs.py,sha256=Ui_Q8ilnNWmmocIeOvIdDxN7cuVG7RFC49QKGdKANAQ,1284
scipy/_lib/tests/test_warnings.py,sha256=oLnXqtAcgumEgtu1CHWo7wCY4seHORf2Q2LKIK9VhKs,4426
scipy/_lib/uarray.py,sha256=60KNupQxJ6EUbiNisIrzqFQtlSMEzgEUa8kvoItKE-E,804
scipy/cluster/__init__.py,sha256=c4Yqsiekr9Uje7Q_o2R0H5GCuv_cenuVejksR53O2kw,900
scipy/cluster/__pycache__/__init__.cpython-38.pyc,,
scipy/cluster/__pycache__/hierarchy.cpython-38.pyc,,
scipy/cluster/__pycache__/vq.cpython-38.pyc,,
scipy/cluster/_hierarchy.cp38-win_amd64.dll.a,sha256=3jVoYnkXqC6D3Q5BaXsXQzNL4QjuKeM6UiM5qMK95uE,1572
scipy/cluster/_hierarchy.cp38-win_amd64.pyd,sha256=5E-_YDJiaSE7FNP9uS2TI3i8PZzG1lSm5-xQeJQHuME,324096
scipy/cluster/_optimal_leaf_ordering.cp38-win_amd64.dll.a,sha256=tvO8jFk7EWoZkwO5BMKBXifE6qnFGrxvSOj3VfZa6Fc,1716
scipy/cluster/_optimal_leaf_ordering.cp38-win_amd64.pyd,sha256=PbNgfzXdEHvBjhoGqRy75FLUn0vwD8NYFZCxMGNioCU,259584
scipy/cluster/_vq.cp38-win_amd64.dll.a,sha256=K9xd8coqLCeJaF4DHufUomGHWovF0OjdMAfvAm-Zvo8,1492
scipy/cluster/_vq.cp38-win_amd64.pyd,sha256=8YxAbISPlL3YW2Dps3DRlNPbldXOD5vnGGW6UrbMHQY,93184
scipy/cluster/hierarchy.py,sha256=RW-OcFlhSUvwUd9-5G9hFzIx8bN8ImHHjFndh0pua8M,152635
scipy/cluster/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/cluster/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/cluster/tests/__pycache__/hierarchy_test_data.cpython-38.pyc,,
scipy/cluster/tests/__pycache__/test_disjoint_set.cpython-38.pyc,,
scipy/cluster/tests/__pycache__/test_hierarchy.cpython-38.pyc,,
scipy/cluster/tests/__pycache__/test_vq.cpython-38.pyc,,
scipy/cluster/tests/hierarchy_test_data.py,sha256=lvVMk2L5iEKlhX32aFRBJ3clecx6AXLYw75Epl07OBo,6995
scipy/cluster/tests/test_disjoint_set.py,sha256=MCC3kjmht_2S5hByyRt8DVwmZW2MP9gJW97B-7zs9KU,5670
scipy/cluster/tests/test_hierarchy.py,sha256=ENHQ4NKSWmTJL5bPfOLgcP1_o8Is2xhyMDgq00k0kYk,44750
scipy/cluster/tests/test_vq.py,sha256=NHPcKZhTo70kRXe0r7HDWZ-zAo_Am0MYdbP7nxTNzdY,13770
scipy/cluster/vq.py,sha256=0lz0y1VOm0gE-qsXz3x5L6gVcscHpNl8y7YI1O3AFoI,30017
scipy/conftest.py,sha256=myjiPPkW1OtlZN64F-AzEIw7BdnK4ZR3nj5bDfta4Ys,3573
scipy/constants/__init__.py,sha256=FkieKRu87BHEFIsrkHRbpsyJz4N-416huaWNOVPOQ9M,12640
scipy/constants/__pycache__/__init__.cpython-38.pyc,,
scipy/constants/__pycache__/_codata.cpython-38.pyc,,
scipy/constants/__pycache__/_constants.cpython-38.pyc,,
scipy/constants/__pycache__/codata.cpython-38.pyc,,
scipy/constants/__pycache__/constants.cpython-38.pyc,,
scipy/constants/_codata.py,sha256=iatMmpwe6ths0JSzJYBy4noPtdbsRcYgeZmUNIXMb7Q,157654
scipy/constants/_constants.py,sha256=aU-_Ll17_Kdv3NCkpkb-WoxljpEMpb82pKXUdgALK3s,10632
scipy/constants/codata.py,sha256=O34xrpPg4Pz6ARh1IkA4BNDqL_Fgu5kps-LtoQDo5OA,1047
scipy/constants/constants.py,sha256=ElE0VaXOuGlGx4NJEVOSNS1eH4YKPzTZr_0wwUH32T8,2538
scipy/constants/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/constants/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/constants/tests/__pycache__/test_codata.cpython-38.pyc,,
scipy/constants/tests/__pycache__/test_constants.cpython-38.pyc,,
scipy/constants/tests/test_codata.py,sha256=0p4cAPH0UMK3JWHGI2Ha05dQpxBOzNSme3_pWcIVTDw,2016
scipy/constants/tests/test_constants.py,sha256=oZy6J36DLygy5IVdcNY_NFJlbvuTTFoAt3EM4JGCIkM,1667
scipy/datasets/__init__.py,sha256=sq5j7Lo90MdMXgQPuWnscm5m62hLPabgZ-n6SnGjmPU,2906
scipy/datasets/__pycache__/__init__.cpython-38.pyc,,
scipy/datasets/__pycache__/_download_all.cpython-38.pyc,,
scipy/datasets/__pycache__/_fetchers.cpython-38.pyc,,
scipy/datasets/__pycache__/_registry.cpython-38.pyc,,
scipy/datasets/__pycache__/_utils.cpython-38.pyc,,
scipy/datasets/_download_all.py,sha256=i-fMQWgAfvOgCEoEabsd68iUYDki-4OUfPjjcGLHRDI,1758
scipy/datasets/_fetchers.py,sha256=QMAi8ei2vmdPxr6VpAYNTkqYA6sdBAzrMH454SUPyhQ,6979
scipy/datasets/_registry.py,sha256=3_ZNYpe3lCXvUGalpNsrhRwKcnFA2P6yWV7MFdKb2Hw,1098
scipy/datasets/_utils.py,sha256=S24H8df4vUQ_daiPltGtnTlTCYdbKhA_-I-1L7lPM5U,2995
scipy/datasets/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/datasets/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/datasets/tests/__pycache__/test_data.cpython-38.pyc,,
scipy/datasets/tests/test_data.py,sha256=loTZrDHWZEQjIihEcSYi6S6HlzltygW3HeRQp61SdVA,4187
scipy/fft/__init__.py,sha256=n4Of6Yu9RZ6sZxgqZdbZeE74dRJ7_TLtPjwhkQiWU6M,3680
scipy/fft/__pycache__/__init__.cpython-38.pyc,,
scipy/fft/__pycache__/_backend.cpython-38.pyc,,
scipy/fft/__pycache__/_basic.cpython-38.pyc,,
scipy/fft/__pycache__/_debug_backends.cpython-38.pyc,,
scipy/fft/__pycache__/_fftlog.cpython-38.pyc,,
scipy/fft/__pycache__/_fftlog_multimethods.cpython-38.pyc,,
scipy/fft/__pycache__/_helper.cpython-38.pyc,,
scipy/fft/__pycache__/_realtransforms.cpython-38.pyc,,
scipy/fft/_backend.py,sha256=PthqHC_RhmLLNQpW3_TgxfUNq6Wc7zONxTKJ4O2Yr48,6587
scipy/fft/_basic.py,sha256=r8S3dKUv4LQkvcPHQKhMEA2Uo0QvZXa_NKYp9VvJWBM,64620
scipy/fft/_debug_backends.py,sha256=R0zCc_CgDubYnaGbQGjk0GTmd5HlYFpBLw7WSlqljpQ,620
scipy/fft/_fftlog.py,sha256=ev6kJ_ejioQE5TAEHxWEC9p6Rl8qvqMOQuhUQ5Bg2WM,12269
scipy/fft/_fftlog_multimethods.py,sha256=XdQnCMC548a7wnJXC1I8tC8OyDs01kQVVPeY65xU_b0,604
scipy/fft/_helper.py,sha256=yFBNbaTYS-cvxij03tABUM278v7Tn_bmEIabmWwa_UA,3517
scipy/fft/_pocketfft/LICENSE.md,sha256=wEZhaFz_nYA1_hxMOrNX5_djPK_AanPp2Qw8X-3oqGo,1523
scipy/fft/_pocketfft/__init__.py,sha256=WKXb59wx9DK9zXq9DwKchp-zg1SuJIynTQ_POQrFXxo,216
scipy/fft/_pocketfft/__pycache__/__init__.cpython-38.pyc,,
scipy/fft/_pocketfft/__pycache__/basic.cpython-38.pyc,,
scipy/fft/_pocketfft/__pycache__/helper.cpython-38.pyc,,
scipy/fft/_pocketfft/__pycache__/realtransforms.cpython-38.pyc,,
scipy/fft/_pocketfft/basic.py,sha256=E6kauapd81flliabn_GYjGDjG_lnIs7z5Lj2pX19FMI,10142
scipy/fft/_pocketfft/helper.py,sha256=FeOcAbG0iddkPZC6WPbBMow_E5VcqMVw3yJEJdtwAUM,5941
scipy/fft/_pocketfft/pypocketfft.cp38-win_amd64.dll.a,sha256=_o48RjFQNogtvL2jOgms5W7WYU6rd4Cf7OmqSm7zgL4,1588
scipy/fft/_pocketfft/pypocketfft.cp38-win_amd64.pyd,sha256=s1f0xf3PdF5wRFJzrZhCtWgVAYhQr47FwRWyetsYFvg,913408
scipy/fft/_pocketfft/realtransforms.py,sha256=Piqkg2-Bobtdw8a8uYGZ-y9cp3coUQm5lK0es93bNcw,3489
scipy/fft/_pocketfft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/_pocketfft/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_basic.cpython-38.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_real_transforms.cpython-38.pyc,,
scipy/fft/_pocketfft/tests/test_basic.py,sha256=tJAb0l9O0B0jIP2tjMo7X0HvXLso7cE8K__5r_KfGRM,36728
scipy/fft/_pocketfft/tests/test_real_transforms.py,sha256=IdRNjeXshoXTmuLo-WGYg5NE0WFJlQQuKyNe_jO0FjI,16919
scipy/fft/_realtransforms.py,sha256=M5p8GbeXNZUlw_Femb3fT-1XrhiyK5kKrNsJS2sKKl8,25973
scipy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/fft/tests/__pycache__/mock_backend.cpython-38.pyc,,
scipy/fft/tests/__pycache__/test_backend.cpython-38.pyc,,
scipy/fft/tests/__pycache__/test_fft_function.cpython-38.pyc,,
scipy/fft/tests/__pycache__/test_fftlog.cpython-38.pyc,,
scipy/fft/tests/__pycache__/test_helper.cpython-38.pyc,,
scipy/fft/tests/__pycache__/test_multithreading.cpython-38.pyc,,
scipy/fft/tests/__pycache__/test_numpy.cpython-38.pyc,,
scipy/fft/tests/__pycache__/test_real_transforms.cpython-38.pyc,,
scipy/fft/tests/mock_backend.py,sha256=lucfCVPsqehDtLXe0cdLB4LL_MaU1xwhzN0u1yqnOCM,1828
scipy/fft/tests/test_backend.py,sha256=dJehAFjzYlIsYqpknfnd_sikp2qI_ndyiL2BkM5cD28,4354
scipy/fft/tests/test_fft_function.py,sha256=365KMEHdBO3ekahEWJdK5uIURtT4DvAwzFtFZEc1Evc,1091
scipy/fft/tests/test_fftlog.py,sha256=5VQG1V9c9NneAQzQmjIcRmkGd1DWif31J2zbavVDiWM,5980
scipy/fft/tests/test_helper.py,sha256=1c5phrBUL41gRZfUTQo1q1JmAJOtHg7jKON6yvTvFH8,10107
scipy/fft/tests/test_multithreading.py,sha256=AD3naMjpD9aQ0sJwLs8n7ln3IzeCzPm5Y6QV8su0YgI,2215
scipy/fft/tests/test_numpy.py,sha256=BN_ic9gJwqJdt2XoEFJyAELTACZ_tj1KvQos98t6NWg,14796
scipy/fft/tests/test_real_transforms.py,sha256=-iwFTTjHhdt0u7871XZIT-JfVFCgHBQMhgQ2xy8djjI,7808
scipy/fftpack/__init__.py,sha256=_MC-4yAJeG9SNdCNi7Bwk_cKGjc4uLmpBhk1NZmNFsw,3304
scipy/fftpack/__pycache__/__init__.cpython-38.pyc,,
scipy/fftpack/__pycache__/_basic.cpython-38.pyc,,
scipy/fftpack/__pycache__/_helper.cpython-38.pyc,,
scipy/fftpack/__pycache__/_pseudo_diffs.cpython-38.pyc,,
scipy/fftpack/__pycache__/_realtransforms.cpython-38.pyc,,
scipy/fftpack/__pycache__/basic.cpython-38.pyc,,
scipy/fftpack/__pycache__/helper.cpython-38.pyc,,
scipy/fftpack/__pycache__/pseudo_diffs.cpython-38.pyc,,
scipy/fftpack/__pycache__/realtransforms.cpython-38.pyc,,
scipy/fftpack/_basic.py,sha256=6AFPL2JeBvZsIZV-C3kAmvG3ybQ8XtiRLSJQ9Go9L2E,13526
scipy/fftpack/_helper.py,sha256=B1AnxDFXJwbpmsOn16CTfzOITo3dBBdy_Fq0ndj1vhY,3466
scipy/fftpack/_pseudo_diffs.py,sha256=5IZuPf96AqFK17zyOv3PJUICEd5qrzQPqW7Aq-B-Pa8,14751
scipy/fftpack/_realtransforms.py,sha256=gG8Q39JAiO1Y3k8neJR1rfXLNhb9IvfBn7w8URM-Ndw,19812
scipy/fftpack/basic.py,sha256=YP6CJx8M1QhCpeqEP60W_1INHc-QRyzTOq1DIrpqGJI,818
scipy/fftpack/convolve.cp38-win_amd64.dll.a,sha256=t2HuOfJtTE7H8ceHyq1jRvy7b0R24IFA7RLuXatLlj4,1548
scipy/fftpack/convolve.cp38-win_amd64.pyd,sha256=unYVbtdP-sqFLvomzZjbTBySaqbMEak1xWIwN5lEsMw,194048
scipy/fftpack/helper.py,sha256=k6qzrP69nZfGgRkgKn_WJJJizN2IOqYgA7USqpfZ5ck,822
scipy/fftpack/pseudo_diffs.py,sha256=XBr7mUmY95jh_1cKGhpjqwCDvBeRI83K6aQBEBchsTE,931
scipy/fftpack/realtransforms.py,sha256=Tgdj9FnqaCUApfALyR772ZHPmaeYfNGk7QjFtvVepHo,853
scipy/fftpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fftpack/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/fftpack/tests/__pycache__/test_basic.cpython-38.pyc,,
scipy/fftpack/tests/__pycache__/test_helper.cpython-38.pyc,,
scipy/fftpack/tests/__pycache__/test_import.cpython-38.pyc,,
scipy/fftpack/tests/__pycache__/test_pseudo_diffs.cpython-38.pyc,,
scipy/fftpack/tests/__pycache__/test_real_transforms.cpython-38.pyc,,
scipy/fftpack/tests/fftw_double_ref.npz,sha256=pgxklBW2RSI5JNg0LMxcCXgByGkBKHo2nlP8kln17E4,162120
scipy/fftpack/tests/fftw_longdouble_ref.npz,sha256=pAbL1NrQTQxZ3Tj1RBb7SUJMgiKcGgdLakTsDN4gAOM,296072
scipy/fftpack/tests/fftw_single_ref.npz,sha256=J2qRQTGOb8NuSrb_VKYbZAVO-ISbZg8XNZ5fVBtDxSY,95144
scipy/fftpack/tests/test.npz,sha256=Nt6ASiLY_eoFRZDOSd3zyFmDi32JGTxWs7y2YMv0N5c,11968
scipy/fftpack/tests/test_basic.py,sha256=KVuoiBJZPjqAq6YQV7QzHz01NLqheTQuWDGPGjCrFnM,31250
scipy/fftpack/tests/test_helper.py,sha256=HuCAP5esSm4lMnw9f9i-ByJj1DURntgC64s2fX5RVIg,1729
scipy/fftpack/tests/test_import.py,sha256=DrdB1xPXkz6EpaeoF8VOSIysA-RrxNTzeLB_Y-mJPxs,1160
scipy/fftpack/tests/test_pseudo_diffs.py,sha256=5wmGeVj5QXbFkxrRG7Tl162v4pFSxiMSONtm4Uoy-AQ,13769
scipy/fftpack/tests/test_real_transforms.py,sha256=-kiG9AuWHQwRIizlB9iaJ-Yya96qKXHbdeyddqkvShg,24756
scipy/integrate/__init__.py,sha256=geK5q5CFCQiq26rlawjEK2bfchfweg35wftXzuh91qE,4110
scipy/integrate/__pycache__/__init__.cpython-38.pyc,,
scipy/integrate/__pycache__/_bvp.cpython-38.pyc,,
scipy/integrate/__pycache__/_ode.cpython-38.pyc,,
scipy/integrate/__pycache__/_odepack_py.cpython-38.pyc,,
scipy/integrate/__pycache__/_quad_vec.cpython-38.pyc,,
scipy/integrate/__pycache__/_quadpack_py.cpython-38.pyc,,
scipy/integrate/__pycache__/_quadrature.cpython-38.pyc,,
scipy/integrate/__pycache__/dop.cpython-38.pyc,,
scipy/integrate/__pycache__/lsoda.cpython-38.pyc,,
scipy/integrate/__pycache__/odepack.cpython-38.pyc,,
scipy/integrate/__pycache__/quadpack.cpython-38.pyc,,
scipy/integrate/__pycache__/vode.cpython-38.pyc,,
scipy/integrate/_bvp.py,sha256=x89uqM4fMnBuG4w67YBFbfi7VwVEUQPgtdxoLrIMliM,42242
scipy/integrate/_dop.cp38-win_amd64.dll.a,sha256=ZqUIy_10hNdxooFhuLVaoTdfsHypPYR9M8skmuxYbgM,1500
scipy/integrate/_dop.cp38-win_amd64.pyd,sha256=1ELA3NAJ_GEEf6krA91xudCwAHhG3kPBqy0W_6KmWY0,421888
scipy/integrate/_ivp/__init__.py,sha256=XVoxnj-1q1Xm4StWijukIoIrqc6Ny1Dxi625GurInWs,264
scipy/integrate/_ivp/__pycache__/__init__.cpython-38.pyc,,
scipy/integrate/_ivp/__pycache__/base.cpython-38.pyc,,
scipy/integrate/_ivp/__pycache__/bdf.cpython-38.pyc,,
scipy/integrate/_ivp/__pycache__/common.cpython-38.pyc,,
scipy/integrate/_ivp/__pycache__/dop853_coefficients.cpython-38.pyc,,
scipy/integrate/_ivp/__pycache__/ivp.cpython-38.pyc,,
scipy/integrate/_ivp/__pycache__/lsoda.cpython-38.pyc,,
scipy/integrate/_ivp/__pycache__/radau.cpython-38.pyc,,
scipy/integrate/_ivp/__pycache__/rk.cpython-38.pyc,,
scipy/integrate/_ivp/base.py,sha256=LIq_X9RTOYLfN7Y6MpW_erQShxhRN-ZgQ0BrWhOpcHg,9824
scipy/integrate/_ivp/bdf.py,sha256=Qmbj2J32vMDqzlmPmQt9khNoYFSXOvlBKTwwyIGbBf4,17631
scipy/integrate/_ivp/common.py,sha256=BRQjJyqhWwOOmHsUQXkGUxiXcEHWKEYIpAeMRm9KhXo,15213
scipy/integrate/_ivp/dop853_coefficients.py,sha256=4oMhmg8eXyzDeSSrlh2dJJMvaabWn-aO44S3wx8Ig5o,7430
scipy/integrate/_ivp/ivp.py,sha256=ykDRQfaJyvV6Uh28b6x2cIYBTtustculCyNNUrmlf9g,28960
scipy/integrate/_ivp/lsoda.py,sha256=qy6QGbGqcLG5jkBEzpw32X_qiINj7RoWe7D5tk39TTU,8490
scipy/integrate/_ivp/radau.py,sha256=vONDUVm27wGERxDqP4ui7fg3W4q0uX6IioRmBqq3nis,19948
scipy/integrate/_ivp/rk.py,sha256=ISibroLgGkZNmXK6u1ZzKr1NDiGFDHi71hlofmKsqhk,22831
scipy/integrate/_ivp/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/_ivp/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_ivp.cpython-38.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_rk.cpython-38.pyc,,
scipy/integrate/_ivp/tests/test_ivp.py,sha256=xf5-h9tamSjSNrPNuw7NEcC0AsKNmwU90oCcU7jtbZw,35802
scipy/integrate/_ivp/tests/test_rk.py,sha256=_8nQKtotVvR218br-0mNSQSEZ-ST4L2Tgaz5xKXHgJw,1363
scipy/integrate/_lsoda.cp38-win_amd64.dll.a,sha256=fqMPgKvDCaPrCoaWT1hEbZz21WXxK_Me6xC6McpHRGQ,1524
scipy/integrate/_lsoda.cp38-win_amd64.pyd,sha256=zDZCqhFuJlviTLcsLR3Whb-H4tScA_4vii98Nrl3huQ,421376
scipy/integrate/_ode.py,sha256=CbU2HsxUfuBqtIhdTM9uaVLh11W91iCq74-fm-HHGvM,49317
scipy/integrate/_odepack.cp38-win_amd64.dll.a,sha256=MB5uXmNKxIdcjs8PNaDYpXkhSqW0dv5mczIoVy2uQi4,1548
scipy/integrate/_odepack.cp38-win_amd64.pyd,sha256=hpSfYxuw_Zwa93Q0y17tXeqZNs_93uhWto06W9jnNS0,404480
scipy/integrate/_odepack_py.py,sha256=-lOaDEyg4OrG2b__C3JrnGi5jdAJAFVFzxvXCQjXBFc,11029
scipy/integrate/_quad_vec.py,sha256=syJqRY8XEbhXvP54R3hDG3buF4XJzz0SxiJMC4QLmx4,21847
scipy/integrate/_quadpack.cp38-win_amd64.dll.a,sha256=e6KZjHYSz0qJ5GE1bodkI6A8IqUADnegbDGT0N6D3OU,1564
scipy/integrate/_quadpack.cp38-win_amd64.pyd,sha256=8HWHtr0wpfCaSH0ZLYEF1GVE15Qmadt3d0AeJ0fpVZs,433152
scipy/integrate/_quadpack_py.py,sha256=NCjq1cHgdgtmB80qz3pfhaHR-h0o3ks5dbOHT_k9LP8,53590
scipy/integrate/_quadrature.py,sha256=cJYoV9RFRFA9jCdRKeDSyRzZhiBmrXLc7F_2ykI1XYY,47273
scipy/integrate/_test_multivariate.cp38-win_amd64.dll.a,sha256=FvghlgiBqHzAAPwegKd9Cy8Ck0SBH-piUa_moEh27lQ,1668
scipy/integrate/_test_multivariate.cp38-win_amd64.pyd,sha256=RQFfwbiZJC3hDQFict3R-mvCs0agKNUUoRhQXP2a-ZY,17920
scipy/integrate/_test_odeint_banded.cp38-win_amd64.dll.a,sha256=jJ0W_Y06hhsYhV1uEx-8WImNAFk96OAxxS2gcLPjzs4,1684
scipy/integrate/_test_odeint_banded.cp38-win_amd64.pyd,sha256=HyqCBDrYgqS53sF-lOqud4a6paAKUKJnjL9oZ0mQ7ro,423424
scipy/integrate/_vode.cp38-win_amd64.dll.a,sha256=_QNLvcTF5ZbTgGeGBsyPl2f9lDuibSgjLhIa_d98D4Y,1516
scipy/integrate/_vode.cp38-win_amd64.pyd,sha256=a67BcvJ6Fv7VOwXdODTiPc_y-OQ7A5UcE-ytD27oLz4,475136
scipy/integrate/dop.py,sha256=LuJEuLD2P0QiAKhLFVyKEjmKOOxov-etcRcjUKoGlj0,650
scipy/integrate/lsoda.py,sha256=wUvgBh8lRrVThfzSMXww325Rc-OZXIObG5rM77QwiJY,635
scipy/integrate/odepack.py,sha256=oItkHltyej5LYB0ZfOHK-zrK5bhIzkLYw2C89vz1flk,796
scipy/integrate/quadpack.py,sha256=M53X9b3kdhysl3GFKBf0lha7XYmP_u5Vy3Ndg326xLQ,877
scipy/integrate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/integrate/tests/__pycache__/test__quad_vec.cpython-38.pyc,,
scipy/integrate/tests/__pycache__/test_banded_ode_solvers.cpython-38.pyc,,
scipy/integrate/tests/__pycache__/test_bvp.cpython-38.pyc,,
scipy/integrate/tests/__pycache__/test_integrate.cpython-38.pyc,,
scipy/integrate/tests/__pycache__/test_odeint_jac.cpython-38.pyc,,
scipy/integrate/tests/__pycache__/test_quadpack.cpython-38.pyc,,
scipy/integrate/tests/__pycache__/test_quadrature.cpython-38.pyc,,
scipy/integrate/tests/test__quad_vec.py,sha256=9iYd5_TJvzPt8KkZItsS6FK9E4Z0GdtwP5RqyyMtcdE,6423
scipy/integrate/tests/test_banded_ode_solvers.py,sha256=erjGeCJNAVCaiulMn698i6ZjMqUFFr0SCZcmo2jJZsM,6905
scipy/integrate/tests/test_bvp.py,sha256=uvupRGbwnqDGGI21qQuMx217PmY6BcdQP_05gC-aX9Y,20868
scipy/integrate/tests/test_integrate.py,sha256=3M2HI_-6cSnQKdg6SQ9H_Le2KvkH4fI1ZyCIJaZs_98,25165
scipy/integrate/tests/test_odeint_jac.py,sha256=z-L5lqY_JTekUMnqwyucaaWA_8JUtqrvO9X_80oE4bE,1895
scipy/integrate/tests/test_quadpack.py,sha256=wH55Yck_9o_9m-CeSl3XJK7A7bEcaPmrGXm-OAE04iM,28621
scipy/integrate/tests/test_quadrature.py,sha256=i9Nh7EPF4Pa8uywPv6IM5LfMXegbgFLmcIhB0kkzEj4,15714
scipy/integrate/vode.py,sha256=l9KU9SI-Csxkcb0OuLsGO_He6KKVfgvIVVDKkJGZbWI,653
scipy/interpolate/__init__.py,sha256=atoDp2yw2xwAXVAK1uMlWrcjzAlHjYH51_lz6SqmDko,3682
scipy/interpolate/__pycache__/__init__.cpython-38.pyc,,
scipy/interpolate/__pycache__/_bsplines.cpython-38.pyc,,
scipy/interpolate/__pycache__/_cubic.cpython-38.pyc,,
scipy/interpolate/__pycache__/_fitpack2.cpython-38.pyc,,
scipy/interpolate/__pycache__/_fitpack_impl.cpython-38.pyc,,
scipy/interpolate/__pycache__/_fitpack_py.cpython-38.pyc,,
scipy/interpolate/__pycache__/_interpnd_info.cpython-38.pyc,,
scipy/interpolate/__pycache__/_interpolate.cpython-38.pyc,,
scipy/interpolate/__pycache__/_ndgriddata.cpython-38.pyc,,
scipy/interpolate/__pycache__/_pade.cpython-38.pyc,,
scipy/interpolate/__pycache__/_polyint.cpython-38.pyc,,
scipy/interpolate/__pycache__/_rbf.cpython-38.pyc,,
scipy/interpolate/__pycache__/_rbfinterp.cpython-38.pyc,,
scipy/interpolate/__pycache__/_rgi.cpython-38.pyc,,
scipy/interpolate/__pycache__/fitpack.cpython-38.pyc,,
scipy/interpolate/__pycache__/fitpack2.cpython-38.pyc,,
scipy/interpolate/__pycache__/interpolate.cpython-38.pyc,,
scipy/interpolate/__pycache__/ndgriddata.cpython-38.pyc,,
scipy/interpolate/__pycache__/polyint.cpython-38.pyc,,
scipy/interpolate/__pycache__/rbf.cpython-38.pyc,,
scipy/interpolate/_bspl.cp38-win_amd64.dll.a,sha256=jaymoEIY51zbvJ-mQAu4lH7ees56LXXI5buKOtXyKH0,1516
scipy/interpolate/_bspl.cp38-win_amd64.pyd,sha256=yPUTgM0p3c_CzyunP7no3lzmzXngc_eojJLvsjL1MBk,274944
scipy/interpolate/_bsplines.py,sha256=lQDKrcpd0hdYBk5Yd9vlwNK2qO3d47gZN6EntarXEkg,71195
scipy/interpolate/_cubic.py,sha256=K7zShrxXZ_HS6kMz-1Avy9OS26IGPV0ASuMPwgcBw1U,34635
scipy/interpolate/_fitpack.cp38-win_amd64.dll.a,sha256=zh0Sp65rt9pzrDJ0PPrv4dIK1e-hXGq_nreaImNvbvo,1548
scipy/interpolate/_fitpack.cp38-win_amd64.pyd,sha256=0wMWCWQTKU-bndwLg3yt2Gq94Ubs4YMSdmHrwgmEXTk,450560
scipy/interpolate/_fitpack2.py,sha256=tTl4563J2o6FwHA8_4e7SSNhn5sXucKtv8hvBpPE0TI,83746
scipy/interpolate/_fitpack_impl.py,sha256=NyrKWWP2W9CTvJl01hgNg1U2fXQjb30b3g2MqFn8pu0,48122
scipy/interpolate/_fitpack_py.py,sha256=UEIzHRVh_NmgMIsAwurGZxEkKc83GGt4LrmzI2Bs-RA,28328
scipy/interpolate/_interpnd_info.py,sha256=GYonUhHHH1g1O9TdINaSgOIv1bSzcWDeLCskackRJMM,906
scipy/interpolate/_interpolate.py,sha256=8M_d2y35AXIJRGDLBMjmLM75M39oWpEUCT6_bg3F7Uc,90214
scipy/interpolate/_ndgriddata.py,sha256=1KFuLj6_nFmrmJsthXKkcRKxW_g5bnsBd9We-oqGSw4,9360
scipy/interpolate/_pade.py,sha256=Phydd4f5oVM_-B3_iwe7H3t66FoCjbfsK_TagWLz9Jo,1894
scipy/interpolate/_polyint.py,sha256=lr3ayQ0QZltNHTpXPJ5ycKBI8GGQRrehOqqJHz2MwyQ,26735
scipy/interpolate/_ppoly.cp38-win_amd64.dll.a,sha256=IeyHcqZeXX9HwR1cKs6TPZWNRpYIGzNkdPOIzJZsnAY,1524
scipy/interpolate/_ppoly.cp38-win_amd64.pyd,sha256=ZqqS4Kg_Yc0eYJKTBs65BI6vAtwp9HFCT7JAR--6Vhw,326144
scipy/interpolate/_rbf.py,sha256=gtMVCHiyHbmlxsqmUXifqInlvjxTxOqiHeRP5lkxxfs,11952
scipy/interpolate/_rbfinterp.py,sha256=ZCzm4oDJ-TePWSSVJ7IhDZFCtHEqhC1BmxrQ44fBfWQ,19926
scipy/interpolate/_rbfinterp_pythran.cp38-win_amd64.dll.a,sha256=6Z52gFE3ogjlLrIG6ycXOF-YXvVpSXD5tTpnCGAAiDQ,1668
scipy/interpolate/_rbfinterp_pythran.cp38-win_amd64.pyd,sha256=PUjpEVgL-vZB9oyVV0w_x-TL-P-UndFoRcKZcNo_Na0,1146880
scipy/interpolate/_rgi.py,sha256=b75iDSYTbS0yMV9RV0pXvEBVvJAacyUK7iNvlhIBbis,27600
scipy/interpolate/_rgi_cython.cp38-win_amd64.dll.a,sha256=5R-wjzZw0qr_oZJFhRw7WnJvzzhuepve5Z1pCzvImMc,1588
scipy/interpolate/_rgi_cython.cp38-win_amd64.pyd,sha256=pJK5-CnluPAvKMNZ0lbIA2C2fTs9iitxPFkL_OCnJ20,203264
scipy/interpolate/dfitpack.cp38-win_amd64.dll.a,sha256=_XNiG59bWgu6VPBVm67xwNL3gagXtIDHsNN3c9TFB68,1548
scipy/interpolate/dfitpack.cp38-win_amd64.pyd,sha256=N-hfMKCt-UdjZVEUZdVHdaU3ZzNoVO-8IZV6LQfPLlc,647680
scipy/interpolate/fitpack.py,sha256=EZLYybeRB4VuqcAUL3WefJJKBt41LfKwteMNJSJUhlE,988
scipy/interpolate/fitpack2.py,sha256=RQZO0omUVY8_5tIZNuGS0xYnaITMg5gne7b5LvO_QWE,1241
scipy/interpolate/interpnd.cp38-win_amd64.dll.a,sha256=i90qtybqup-7c_Ydhyt6vttR_OlL4Aql87VvsftFV2I,1548
scipy/interpolate/interpnd.cp38-win_amd64.pyd,sha256=qh12ePwCHaBrrxTUYzqPMz7e5AGFh_auLtieYTw0TSs,332288
scipy/interpolate/interpolate.py,sha256=YoEgqQsjxTZxYjlHFXEBHd7f4VpFfzNM0g7Wmh16yao,1232
scipy/interpolate/ndgriddata.py,sha256=68eqSfvBC0To4kWCV0aUUdzXUgWnB2Wi4J5aDufqXOU,945
scipy/interpolate/polyint.py,sha256=_SRqRCIhOcik1iLhLGxJb5RtFna42jDfnJByJ-JGlEI,975
scipy/interpolate/rbf.py,sha256=CD57kmE86z1FxoW7084fL06zYF9tsvVgoAs3dPnqyQI,851
scipy/interpolate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/interpolate/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/interpolate/tests/__pycache__/test_bsplines.cpython-38.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack.cpython-38.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack2.cpython-38.pyc,,
scipy/interpolate/tests/__pycache__/test_gil.cpython-38.pyc,,
scipy/interpolate/tests/__pycache__/test_interpnd.cpython-38.pyc,,
scipy/interpolate/tests/__pycache__/test_interpolate.cpython-38.pyc,,
scipy/interpolate/tests/__pycache__/test_ndgriddata.cpython-38.pyc,,
scipy/interpolate/tests/__pycache__/test_pade.cpython-38.pyc,,
scipy/interpolate/tests/__pycache__/test_polyint.cpython-38.pyc,,
scipy/interpolate/tests/__pycache__/test_rbf.cpython-38.pyc,,
scipy/interpolate/tests/__pycache__/test_rbfinterp.cpython-38.pyc,,
scipy/interpolate/tests/__pycache__/test_rgi.cpython-38.pyc,,
scipy/interpolate/tests/data/bug-1310.npz,sha256=jWgDwLOY8nBMI28dG56OXt4GvRZaCrsPIoKBq71FWuk,2648
scipy/interpolate/tests/data/estimate_gradients_hang.npy,sha256=QGwQhXQX_16pjYzSiUXJ0OT1wk-SpIrQ6Pq5Vb8kd_E,35680
scipy/interpolate/tests/data/gcvspl.npz,sha256=A86BVabLoMG_CiRBoQwigZH5Ft7DbLggcjQpgRKWu6g,3138
scipy/interpolate/tests/test_bsplines.py,sha256=aTFry9bMbGp2osdMHofHv9Hx3N7srxs3p01WaNmk6Lw,61290
scipy/interpolate/tests/test_fitpack.py,sha256=LnoN4KeNKJoM8NJhxFLYJojR-r-7DTGkROW8kE_iLek,14995
scipy/interpolate/tests/test_fitpack2.py,sha256=mpXV2O5WzWqF2UxIQH4lrtfZKgzTAoCehyKDL3bHBuI,59824
scipy/interpolate/tests/test_gil.py,sha256=U7P1Jo7ztDrrkjDRv_2AZb7I2f6hv-c6WyP8UeeNVVE,1939
scipy/interpolate/tests/test_interpnd.py,sha256=r2PBsiLCA5u2Nms1IEftk3iq4NAJOX_NOZNFGS3ZarI,14013
scipy/interpolate/tests/test_interpolate.py,sha256=MwgD0SUTES4J72iWv-YRSAGX7mnmYNK5nlNcfjGyIJg,98279
scipy/interpolate/tests/test_ndgriddata.py,sha256=uYgSi4F10q44J6fALyPurx063zJOLAhYtU23xW2KPfQ,9691
scipy/interpolate/tests/test_pade.py,sha256=BIQhGVfZRFMz9dbAFfmLJFxQTUpRtnf_QshNqcOs4eo,3887
scipy/interpolate/tests/test_polyint.py,sha256=1sQpV01X-8DRVJRXSwCZ3Dr9Py4hJduR6Mgw2YOXdRo,31100
scipy/interpolate/tests/test_rbf.py,sha256=6VG5TgneHa_L6vPczjLgQpvJOnd2KxbBgKwj57FYrT4,6768
scipy/interpolate/tests/test_rbfinterp.py,sha256=x0Eh1Zlq35104Q8xlxLwvRieqHaPCYR6PLi8-4nRCI0,18634
scipy/interpolate/tests/test_rgi.py,sha256=6D7IXXDb66DoXGkBzT-NuSA2NYgmLir3GOA6RjeCrGY,42253
scipy/io/__init__.py,sha256=vGwvo_gf2CiVCKpX1wl3LiMkGhSah7NoGSVjKpohsxU,2863
scipy/io/__pycache__/__init__.cpython-38.pyc,,
scipy/io/__pycache__/_fortran.cpython-38.pyc,,
scipy/io/__pycache__/_idl.cpython-38.pyc,,
scipy/io/__pycache__/_mmio.cpython-38.pyc,,
scipy/io/__pycache__/_netcdf.cpython-38.pyc,,
scipy/io/__pycache__/harwell_boeing.cpython-38.pyc,,
scipy/io/__pycache__/idl.cpython-38.pyc,,
scipy/io/__pycache__/mmio.cpython-38.pyc,,
scipy/io/__pycache__/netcdf.cpython-38.pyc,,
scipy/io/__pycache__/wavfile.cpython-38.pyc,,
scipy/io/_fortran.py,sha256=Eya-R0yvAZmGYH3t13LG1ZL-35PM6h_0Ws1zVCAe5xU,11257
scipy/io/_harwell_boeing/__init__.py,sha256=YnsHlxg7HQ-WxR5OOELtfv1JrwYrSkojji4_8hpppBs,591
scipy/io/_harwell_boeing/__pycache__/__init__.cpython-38.pyc,,
scipy/io/_harwell_boeing/__pycache__/_fortran_format_parser.cpython-38.pyc,,
scipy/io/_harwell_boeing/__pycache__/hb.cpython-38.pyc,,
scipy/io/_harwell_boeing/_fortran_format_parser.py,sha256=R6rf63oX01AQO-z1ux_tjzT4HBL0tVy2IWPZWhnoxcw,9231
scipy/io/_harwell_boeing/hb.py,sha256=8MwHXivPVCbIkDLAEQLkJwxAKxPcuvs6B4kaWu_QOgs,19737
scipy/io/_harwell_boeing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/_harwell_boeing/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/io/_harwell_boeing/tests/__pycache__/test_fortran_format.cpython-38.pyc,,
scipy/io/_harwell_boeing/tests/__pycache__/test_hb.cpython-38.pyc,,
scipy/io/_harwell_boeing/tests/test_fortran_format.py,sha256=oBT5mfOa0sthUlb4qo_lJnP5BYFj3h8a6HqUZPMTXrk,2434
scipy/io/_harwell_boeing/tests/test_hb.py,sha256=BCfVyKAE3O0ABcfeGfxtGquwhehBGMPKGJl95oC3EIo,2349
scipy/io/_idl.py,sha256=F9DrMAkVxb4PdRPwytNC-ZqsqVL_tqimG74z9bqbT-g,27844
scipy/io/_mmio.py,sha256=BAORvyW52xBMrieL2l2ieOENsFQ9c9eyhREjJhSOr3o,34161
scipy/io/_netcdf.py,sha256=sxAnYe8R5wWkzVm2BdMqTelgmkubPX13K6GLHjyqWmk,40173
scipy/io/_test_fortran.cp38-win_amd64.dll.a,sha256=vuGybZC7yKaueDrVjWt6Yu1Vm7qwVBKAwy9SrR6BRWY,1612
scipy/io/_test_fortran.cp38-win_amd64.pyd,sha256=2ziChpFt6iKTcC3oa3jgUJQudNTnuN2ypUksHydbLhA,378368
scipy/io/arff/__init__.py,sha256=3F0yvqizJT7Hq5gygpmZw2WMuy0r9aCmb6G2yNNn8-I,833
scipy/io/arff/__pycache__/__init__.cpython-38.pyc,,
scipy/io/arff/__pycache__/_arffread.cpython-38.pyc,,
scipy/io/arff/__pycache__/arffread.cpython-38.pyc,,
scipy/io/arff/_arffread.py,sha256=LV0TEBKyQk4GR0PVudK8tiWsGNT9wl_buogryd-Ibec,27278
scipy/io/arff/arffread.py,sha256=F2uc1fWu_w4GrgZmiO1sUnUd16N_4PeTRIFLuW0v5ws,1400
scipy/io/arff/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/arff/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/io/arff/tests/__pycache__/test_arffread.cpython-38.pyc,,
scipy/io/arff/tests/data/iris.arff,sha256=gzevjIJmmJLocLjOEnQMkZVZuIpfPJLocJdIG4RYHIk,7711
scipy/io/arff/tests/data/missing.arff,sha256=m53l1Himyx0TtVgNLucoz1go8-9BmSE70Ta9bm6dt_k,128
scipy/io/arff/tests/data/nodata.arff,sha256=6oAHQ7CUQEG8h5glXKpCaXH0UABOCEoxs5j50WYPAKU,240
scipy/io/arff/tests/data/quoted_nominal.arff,sha256=rc8IS9thME1NM9tAR-C4IUixLrSpw64YNeHkIRMS6Ng,299
scipy/io/arff/tests/data/quoted_nominal_spaces.arff,sha256=NbE6wwE0HkJDomErLPHgPGO6iF2HA1bLpmcamzx3F58,305
scipy/io/arff/tests/data/test1.arff,sha256=NV86lzIKWCwq3UNZmMGt1XK0yurcYmcp0KS9c5rKLkE,201
scipy/io/arff/tests/data/test10.arff,sha256=GbDWnG6LzjsbjYxiarYG4Ft3WlK1rvuM6w3proNFD58,199016
scipy/io/arff/tests/data/test11.arff,sha256=RFWNoWrx2LHJ0izc1bJdmGA2M2L8NT58MwKQN2B-3AI,252
scipy/io/arff/tests/data/test2.arff,sha256=zk5hNFTEcphFXkon2At_FD9ia_lAyzOcSFj4xnJTVPs,315
scipy/io/arff/tests/data/test3.arff,sha256=R2zwCzapweGqylmLD-LXx7GAS957NNJ8emM48hLTwEY,78
scipy/io/arff/tests/data/test4.arff,sha256=KtyokEnupbYZVi-fR7GJtr-hHefP54xgV2l26Nz3YZU,249
scipy/io/arff/tests/data/test5.arff,sha256=_1-QhpmflJKp1polaKwjkeJZsHeho7RzM3_ic2JJykA,391
scipy/io/arff/tests/data/test6.arff,sha256=WOCPN8QRdxCUSfzs-Y-OlJKiUmgLKuDrfy6wsWIFCJU,207
scipy/io/arff/tests/data/test7.arff,sha256=AQdPXYEKEhb10Odd_SDrPcv_gHNR0ymFKnD_91zjFc8,573
scipy/io/arff/tests/data/test8.arff,sha256=YlCbztVn-7x4w41HLIP852BqkKvWBrO9N8Nb0LSR0ZM,440
scipy/io/arff/tests/data/test9.arff,sha256=X0fH-_mz5ScbEvPJV7LJH-7Kbr2RTOlamf2_x18ydY0,324
scipy/io/arff/tests/test_arffread.py,sha256=t2SwgccQ7DgXqpQQPRy3-EFuF8DB1Sy3aCBX1XFEzVc,13516
scipy/io/harwell_boeing.py,sha256=JGv5F3Li4s8RQbdyDRsYQXh0coa_SOgdZhF8apsvMIU,927
scipy/io/idl.py,sha256=ZAJt7fqvOIGrQUFqI5mdaMq9dG0O-OLHDp62SkEfx6A,823
scipy/io/matlab/__init__.py,sha256=Q2pLqHXC6sl2UM6xbBo5ouvuQYwi7Ky_vf8aQTEBbBA,2085
scipy/io/matlab/__pycache__/__init__.cpython-38.pyc,,
scipy/io/matlab/__pycache__/_byteordercodes.cpython-38.pyc,,
scipy/io/matlab/__pycache__/_mio.cpython-38.pyc,,
scipy/io/matlab/__pycache__/_mio4.cpython-38.pyc,,
scipy/io/matlab/__pycache__/_mio5.cpython-38.pyc,,
scipy/io/matlab/__pycache__/_mio5_params.cpython-38.pyc,,
scipy/io/matlab/__pycache__/_miobase.cpython-38.pyc,,
scipy/io/matlab/__pycache__/byteordercodes.cpython-38.pyc,,
scipy/io/matlab/__pycache__/mio.cpython-38.pyc,,
scipy/io/matlab/__pycache__/mio4.cpython-38.pyc,,
scipy/io/matlab/__pycache__/mio5.cpython-38.pyc,,
scipy/io/matlab/__pycache__/mio5_params.cpython-38.pyc,,
scipy/io/matlab/__pycache__/mio5_utils.cpython-38.pyc,,
scipy/io/matlab/__pycache__/mio_utils.cpython-38.pyc,,
scipy/io/matlab/__pycache__/miobase.cpython-38.pyc,,
scipy/io/matlab/__pycache__/streams.cpython-38.pyc,,
scipy/io/matlab/_byteordercodes.py,sha256=Bzx5cwfuEKYvZmRA8cZSop_whh7tnH8098yZpXzuyM4,1975
scipy/io/matlab/_mio.py,sha256=iE9AkfS5nqEEZor7XjTzHDvG5431fVjw6wtaTyBAIaw,13157
scipy/io/matlab/_mio4.py,sha256=TNwDJPvw5bRg3oIz98Al1VkmQ5TlNn-dBU7YXfco-10,21235
scipy/io/matlab/_mio5.py,sha256=1MHVRXPURh8RLXcYaXaaIKFoWA48N0TyhnoOFo5kNow,34318
scipy/io/matlab/_mio5_params.py,sha256=b3tn-kJVtf-aYDG_da0nrrAnHkVrP17pfh95SLeZnOU,8479
scipy/io/matlab/_mio5_utils.cp38-win_amd64.dll.a,sha256=dU0d50PEWUqPCdvBCY7i4ec4XcWAGfEJYf8WP6Zf4cU,1588
scipy/io/matlab/_mio5_utils.cp38-win_amd64.pyd,sha256=_LCn2kM9ozZIYIItLLFphdpMvclhUkuYx2DbeWHiWFA,178688
scipy/io/matlab/_mio_utils.cp38-win_amd64.dll.a,sha256=LCifj2NTCQL-iLWA6jYqEDjRpVyp7axIs2UJShZw0AM,1572
scipy/io/matlab/_mio_utils.cp38-win_amd64.pyd,sha256=ZnnYXPlu28zcvU03Xl3zYeA0lqu8ZvnMfxlQx0GbNAA,42496
scipy/io/matlab/_miobase.py,sha256=N_0clQJpb-_7M1rs_BmRc-7kZ6A-0hcaG_4rzQPETOg,13337
scipy/io/matlab/_streams.cp38-win_amd64.dll.a,sha256=O9kEa5s0iJLVnjyaQNg0WLw8av0D46nw128zrJ1QuA0,1548
scipy/io/matlab/_streams.cp38-win_amd64.pyd,sha256=dOKXTf1xfvFqi8AgnU6zlRdWfv34G1lchdpfXORtiEs,104960
scipy/io/matlab/byteordercodes.py,sha256=QGGZ1YElbvWQV3zVz_d-GwlzoCca0hXizqNGTlg93oo,878
scipy/io/matlab/mio.py,sha256=avFPrzEr5G3tofr2ah65kUEariVqkTKavYI23CGXBAk,923
scipy/io/matlab/mio4.py,sha256=KbcOevZ1wx2mNaZenhbBImXrroOeo2F5NZ0Sd3O-8zo,1234
scipy/io/matlab/mio5.py,sha256=PNRaqOyDlooRhxzEcXFhlMcMVg4DJgb6htqQJguHB9I,1472
scipy/io/matlab/mio5_params.py,sha256=Dt10KjCvDtFxKNkM7eqV6fxHy1sQg4rnSqmHxGOsV7A,1563
scipy/io/matlab/mio5_utils.py,sha256=yqyy-8dxdEJxzakOkfqEi5HrE5T5tqD9YazSYSLuSXM,927
scipy/io/matlab/mio_utils.py,sha256=171bvCROOYtLX89rgjW4owHD4JjQ315U86m-07KPoYY,812
scipy/io/matlab/miobase.py,sha256=YmfpvPIhgDx2VzeDrPopeFzL5tqRWYsqnU6kii0CJqg,1019
scipy/io/matlab/streams.py,sha256=0yaJf3hsNv2_i85_MGJ7Kx22f9K-6SBB71f34iLtY8g,836
scipy/io/matlab/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/matlab/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/io/matlab/tests/__pycache__/test_byteordercodes.cpython-38.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio.cpython-38.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio5_utils.cpython-38.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_funcs.cpython-38.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_utils.cpython-38.pyc,,
scipy/io/matlab/tests/__pycache__/test_miobase.cpython-38.pyc,,
scipy/io/matlab/tests/__pycache__/test_pathological.cpython-38.pyc,,
scipy/io/matlab/tests/__pycache__/test_streams.cpython-38.pyc,,
scipy/io/matlab/tests/data/bad_miuint32.mat,sha256=CVkYHp_U4jxYKRRHSuZ5fREop4tJjnZcQ02DKfObkRA,272
scipy/io/matlab/tests/data/bad_miutf8_array_name.mat,sha256=V-jfVMkYyy8qRGcOIsNGcoO0GCgTxchrsQUBGBnfWHE,208
scipy/io/matlab/tests/data/big_endian.mat,sha256=2ttpiaH2B6nmHnq-gsFeMvZ2ZSLOlpzt0IJiqBTcc8M,273
scipy/io/matlab/tests/data/broken_utf8.mat,sha256=nm8aotRl6NIxlM3IgPegKR3EeevYZoJCrYpV4Sa1T5I,216
scipy/io/matlab/tests/data/corrupted_zlib_checksum.mat,sha256=X4dvE7K9DmGEF3D6I-48hC86W41jB54H7bD8KTXjtYA,276
scipy/io/matlab/tests/data/corrupted_zlib_data.mat,sha256=DfE1YBH-pYw-dAaEeKA6wZcyKeo9GlEfrzZtql-fO_w,3451
scipy/io/matlab/tests/data/japanese_utf8.txt,sha256=rgxiBH7xmEKF91ZkB3oMLrqABBXINEMHPXDKdZXNBEY,270
scipy/io/matlab/tests/data/little_endian.mat,sha256=FQP_2MNod-FFF-JefN7ZxovQ6QLCdHQ0DPL_qBCP44Y,265
scipy/io/matlab/tests/data/logical_sparse.mat,sha256=qujUUpYewaNsFKAwGpYS05z7kdUv9TQZTHV5_lWhRrs,208
scipy/io/matlab/tests/data/malformed1.mat,sha256=DTuTr1-IzpLMBf8u5DPb3HXmw9xJo1aWfayA5S_3zUI,2208
scipy/io/matlab/tests/data/miuint32_for_miint32.mat,sha256=romrBP_BS46Sl2-pKWsUnxYDad2wehyjq4wwLaVqums,272
scipy/io/matlab/tests/data/miutf8_array_name.mat,sha256=Vo8JptFr-Kg2f2cEoDg8LtELSjVNyccdJY74WP_kqtc,208
scipy/io/matlab/tests/data/nasty_duplicate_fieldnames.mat,sha256=bvdmj6zDDUIpOfIP8J4Klo107RYCDd5VK5gtOYx3GsU,8168
scipy/io/matlab/tests/data/one_by_zero_char.mat,sha256=Z3QdZjTlOojjUpS0cfBP4XfNQI3GTjqU0n_pnAzgQhU,184
scipy/io/matlab/tests/data/parabola.mat,sha256=ENWuWX_uwo4Av16dIGOwnbMReAMrShDhalkq8QUI8Rg,729
scipy/io/matlab/tests/data/single_empty_string.mat,sha256=4uTmX0oydTjmtnhxqi9SyPWCG2I24gj_5LarS80bPik,171
scipy/io/matlab/tests/data/some_functions.mat,sha256=JA736oG3s8PPdKhdsYK-BndLUsGrJCJAIRBseSIEZtM,1397
scipy/io/matlab/tests/data/sqr.mat,sha256=3DtGl_V4wABKCDQ0P3He5qfOzpUTC-mINdK73MKS7AM,679
scipy/io/matlab/tests/data/test3dmatrix_6.1_SOL2.mat,sha256=-odiBIQAbOLERg0Vg682QHGfs7C8MaA_gY77OWR8x78,232
scipy/io/matlab/tests/data/test3dmatrix_6.5.1_GLNX86.mat,sha256=G5siwvZ-7Uv5KJ6h7AA3OHL6eiFsd8Lnjx4IcoByzCU,232
scipy/io/matlab/tests/data/test3dmatrix_7.1_GLNX86.mat,sha256=EVj1wPnoyWGIdTpkSj3YAwqzTAm27eqZNxCaJAs3pwU,213
scipy/io/matlab/tests/data/test3dmatrix_7.4_GLNX86.mat,sha256=S_Sd3sxorDd8tZ5CxD5_J8vXbfcksLWzhUQY5b82L9g,213
scipy/io/matlab/tests/data/test_empty_struct.mat,sha256=WoC7g7TyXqNr2T0d5xE3IUq5PRzatE0mxXjqoHX5Xec,173
scipy/io/matlab/tests/data/test_mat4_le_floats.mat,sha256=2xvn3Cg4039shJl62T-bH-VeVP_bKtwdqvGfIxv8FJ4,38
scipy/io/matlab/tests/data/test_skip_variable.mat,sha256=pJLVpdrdEb-9SMZxaDu-uryShlIi90l5LfXhvpVipJ0,20225
scipy/io/matlab/tests/data/testbool_8_WIN64.mat,sha256=_xBw_2oZA7u9Xs6GJItUpSIEV4jVdfdcwzmLNFWM6ow,185
scipy/io/matlab/tests/data/testcell_6.1_SOL2.mat,sha256=OWOBzNpWTyAHIcZABRytVMcABiRYgEoMyF9gDaIkFe4,536
scipy/io/matlab/tests/data/testcell_6.5.1_GLNX86.mat,sha256=7111TN_sh1uMHmYx-bjd_v9uaAnWhJMhrQFAtAw6Nvk,536
scipy/io/matlab/tests/data/testcell_7.1_GLNX86.mat,sha256=62p6LRW6PbM-Y16aUeGVhclTVqS5IxPUtsohe7MjrYo,283
scipy/io/matlab/tests/data/testcell_7.4_GLNX86.mat,sha256=NkTA8UW98hIQ0t5hGx_leG-MzNroDelYwqx8MPnO63Q,283
scipy/io/matlab/tests/data/testcellnest_6.1_SOL2.mat,sha256=AeNaog8HUDCVrIuGICAXYu9SGDsvV6qeGjgvWHrVQho,568
scipy/io/matlab/tests/data/testcellnest_6.5.1_GLNX86.mat,sha256=Gl4QA0yYwGxjiajjgWS939WVAM-W2ahNIm9wwMaT5oc,568
scipy/io/matlab/tests/data/testcellnest_7.1_GLNX86.mat,sha256=CUGtkwIU9CBa0Slx13mbaM67_ec0p-unZdu8Z4YYM3c,228
scipy/io/matlab/tests/data/testcellnest_7.4_GLNX86.mat,sha256=TeTk5yjl5j_bcnmIkpzuYHxGGQXNu-rK6xOsN4t6lX8,228
scipy/io/matlab/tests/data/testcomplex_4.2c_SOL2.mat,sha256=WOwauWInSVUFBuOJ1Bo3spmUQ3UWUIlsIe4tYGlrU7o,176
scipy/io/matlab/tests/data/testcomplex_6.1_SOL2.mat,sha256=GpAEccizI8WvlrBPdvlKUv6uKbZOo_cjUK3WVVb2lo4,352
scipy/io/matlab/tests/data/testcomplex_6.5.1_GLNX86.mat,sha256=3MEbf0zJdQGAO7x-pzFCup2QptfYJHQG59z0vVOdxl4,352
scipy/io/matlab/tests/data/testcomplex_7.1_GLNX86.mat,sha256=VNHV2AIEkvPuhae1kKIqt5t8AMgUyr0L_CAp-ykLxt4,247
scipy/io/matlab/tests/data/testcomplex_7.4_GLNX86.mat,sha256=8rWGf5bqY7_2mcd5w5gTYgMkXVePlLL8qT7lh8kApn0,247
scipy/io/matlab/tests/data/testdouble_4.2c_SOL2.mat,sha256=MzT7OYPEUXHYNPBrVkyKEaG5Cas2aOA0xvrO7l4YTrQ,103
scipy/io/matlab/tests/data/testdouble_6.1_SOL2.mat,sha256=DpB-mVKx1gsjl-3IbxfxHNuzU5dnuku-MDQCA8kALVI,272
scipy/io/matlab/tests/data/testdouble_6.5.1_GLNX86.mat,sha256=4hY5VEubavNEv5KvcqQnd7MWWvFUzHXXpYIqUuUt-50,272
scipy/io/matlab/tests/data/testdouble_7.1_GLNX86.mat,sha256=N2QOOIXPyy0zPZZ_qY7xIDaodMGrTq3oXNBEHZEscw0,232
scipy/io/matlab/tests/data/testdouble_7.4_GLNX86.mat,sha256=TrkJ4Xx_dC9YrPdewlsOvYs_xag7gT3cN4HkDsJmT8I,232
scipy/io/matlab/tests/data/testemptycell_5.3_SOL2.mat,sha256=g96Vh9FpNhkiWKsRm4U6KqeKd1hNAEyYSD7IVzdzwsU,472
scipy/io/matlab/tests/data/testemptycell_6.5.1_GLNX86.mat,sha256=2Zw-cMv-Mjbs2HkSl0ubmh_htFUEpkn7XVHG8iM32o0,472
scipy/io/matlab/tests/data/testemptycell_7.1_GLNX86.mat,sha256=t5Ar8EgjZ7fkTUHIVpdXg-yYWo_MBaigMDJUGWEIrmU,218
scipy/io/matlab/tests/data/testemptycell_7.4_GLNX86.mat,sha256=5PPvfOoL-_Q5ou_2nIzIrHgeaOZGFXGxAFdYzCQuwEQ,218
scipy/io/matlab/tests/data/testfunc_7.4_GLNX86.mat,sha256=ScTKftENe78imbMc0I5ouBlIMcEEmZgu8HVKWAMNr58,381
scipy/io/matlab/tests/data/testhdf5_7.4_GLNX86.mat,sha256=ZoVbGk38_MCppZ0LRr6OE07HL8ZB4rHXgMj9LwUBgGg,4168
scipy/io/matlab/tests/data/testmatrix_4.2c_SOL2.mat,sha256=14YMiKAN9JCPTqSDXxa58BK6Un7EM4hEoSGAUuwKWGQ,151
scipy/io/matlab/tests/data/testmatrix_6.1_SOL2.mat,sha256=ZdjNbcIE75V5Aht5EVBvJX26aabvNqbUH0Q9VBnxBS4,216
scipy/io/matlab/tests/data/testmatrix_6.5.1_GLNX86.mat,sha256=OB82QgB6SwtsxT4t453OVSj-B777XrHGEGOMgMD1XGc,216
scipy/io/matlab/tests/data/testmatrix_7.1_GLNX86.mat,sha256=-TYB0kREY7i7gt5x15fOYjXi410pXuDWUFxPYuMwywI,193
scipy/io/matlab/tests/data/testmatrix_7.4_GLNX86.mat,sha256=l9psDc5K1bpxNeuFlyYIYauswLnOB6dTX6-jvelW0kU,193
scipy/io/matlab/tests/data/testminus_4.2c_SOL2.mat,sha256=2914WYQajPc9-Guy3jDOLU3YkuE4OXC_63FUSDzJzX0,38
scipy/io/matlab/tests/data/testminus_6.1_SOL2.mat,sha256=2X2fZKomz0ktBvibj7jvHbEvt2HRA8D6hN9qA1IDicw,200
scipy/io/matlab/tests/data/testminus_6.5.1_GLNX86.mat,sha256=i364SgUCLSYRjQsyygvY1ArjEaO5uLip3HyU-R7zaLo,200
scipy/io/matlab/tests/data/testminus_7.1_GLNX86.mat,sha256=gtYNC9_TciYdq8X9IwyGEjiw2f1uCVTGgiOPFOiQbJc,184
scipy/io/matlab/tests/data/testminus_7.4_GLNX86.mat,sha256=eXcoTM8vKuh4tQnl92lwdDaqssGB6G9boSHh3FOCkng,184
scipy/io/matlab/tests/data/testmulti_4.2c_SOL2.mat,sha256=Zhyu2KCsseSJ5NARdS00uwddCs4wmjcWNP2LJFns2-Q,240
scipy/io/matlab/tests/data/testmulti_7.1_GLNX86.mat,sha256=KI3H58BVj6k6MFsj8icSbjy_0Z-jOesWN5cafStLPG8,276
scipy/io/matlab/tests/data/testmulti_7.4_GLNX86.mat,sha256=Yr4YKCP27yMWlK5UOK3BAEOAyMr-m0yYGcj8v1tCx-I,276
scipy/io/matlab/tests/data/testobject_6.1_SOL2.mat,sha256=kzLxy_1o1HclPXWyA-SX5gl6LsG1ioHuN4eS6x5iZio,800
scipy/io/matlab/tests/data/testobject_6.5.1_GLNX86.mat,sha256=dq_6_n0v7cUz9YziXn-gZFNc9xYtNxZ8exTsziWIM7s,672
scipy/io/matlab/tests/data/testobject_7.1_GLNX86.mat,sha256=3z-boFw0SC5142YPOLo2JqdusPItVzjCFMhXAQNaQUQ,306
scipy/io/matlab/tests/data/testobject_7.4_GLNX86.mat,sha256=5OwLTMgCBlxsDfiEUzlVjqcSbVQG-X5mIw5JfW3wQXA,306
scipy/io/matlab/tests/data/testonechar_4.2c_SOL2.mat,sha256=BCvppGhO19-j-vxAvbdsORIiyuJqzCuQog9Ao8V1lvA,40
scipy/io/matlab/tests/data/testonechar_6.1_SOL2.mat,sha256=ThppTHGJFrUfal5tewS70DL00dSwk1otazuVdJrTioE,200
scipy/io/matlab/tests/data/testonechar_6.5.1_GLNX86.mat,sha256=SBfN6e7Vz1rAdi8HLguYXcHUHk1viaXTYccdEyhhob4,200
scipy/io/matlab/tests/data/testonechar_7.1_GLNX86.mat,sha256=m8W9GqvflfAsizkhgAfT0lLcxuegZIWCLNuHVX69Jac,184
scipy/io/matlab/tests/data/testonechar_7.4_GLNX86.mat,sha256=t9ObKZOLy3vufnER8TlvQcUkd_wmXbJSdQoG4f3rVKY,184
scipy/io/matlab/tests/data/testscalarcell_7.4_GLNX86.mat,sha256=5LX9sLH7Y6h_N_a1XRN2GuMgp_P7ECpPsXGDOypAJg0,194
scipy/io/matlab/tests/data/testsimplecell.mat,sha256=Aoeh0PX2yiLDTwkxMEyZ_CNX2mJHZvyfuFJl817pA1c,220
scipy/io/matlab/tests/data/testsparse_4.2c_SOL2.mat,sha256=dFUcB1gunfWqexgR4YDZ_Ec0w0HffM1DUE1C5PVfDDc,223
scipy/io/matlab/tests/data/testsparse_6.1_SOL2.mat,sha256=9Sgd_SPkGNim7ZL0xgD71qml3DK0yDHYC7VSNLNQEXA,280
scipy/io/matlab/tests/data/testsparse_6.5.1_GLNX86.mat,sha256=jp1ILNxLyV6XmCCGxAz529XoZ9dhCqGEO-ExPH70_Pg,328
scipy/io/matlab/tests/data/testsparse_7.1_GLNX86.mat,sha256=k8QuQ_4Zu7FWTzHjRnHCVZ9Yu5vwNP0WyNzu6TuiY-4,229
scipy/io/matlab/tests/data/testsparse_7.4_GLNX86.mat,sha256=QbZOCqIvnaK0XOH3kaSXBe-m_1_Rb33psq8E-WMSBTU,229
scipy/io/matlab/tests/data/testsparsecomplex_4.2c_SOL2.mat,sha256=QMVoBXVyl9RBGvAjLoiW85kAXYJ-hHprUMegEG69A5w,294
scipy/io/matlab/tests/data/testsparsecomplex_6.1_SOL2.mat,sha256=WfEroAT5YF4HGAKq3jTJxlFrKaTCh3rwlSlKu__VjwA,304
scipy/io/matlab/tests/data/testsparsecomplex_6.5.1_GLNX86.mat,sha256=e0s6cyoKJeYMArdceHpnKDvtCVcw7XuB44OBDHpoa6U,400
scipy/io/matlab/tests/data/testsparsecomplex_7.1_GLNX86.mat,sha256=kgHcuq-deI2y8hfkGwlMOkW7lntexdPHfuz0ar6b3jo,241
scipy/io/matlab/tests/data/testsparsecomplex_7.4_GLNX86.mat,sha256=rYCaWNLXK7f_jjMc6_UvZz6ZDuMCuVRmJV5RyeXiDm8,241
scipy/io/matlab/tests/data/testsparsefloat_7.4_GLNX86.mat,sha256=hnNV6GZazEeqTXuA9vcOUo4xam_UnKRYGYH9PUGTLv8,219
scipy/io/matlab/tests/data/teststring_4.2c_SOL2.mat,sha256=cAhec51DlqIYfDXXGaumOE3Hqb3cFWM1UsUK3K_lDP8,375
scipy/io/matlab/tests/data/teststring_6.1_SOL2.mat,sha256=ciFzNGMO7gjYecony-E8vtOwBY4vXIUhyug6Euaz3Kg,288
scipy/io/matlab/tests/data/teststring_6.5.1_GLNX86.mat,sha256=yrJrpLiwLvU_LI1D6rw1Pk1qJK1YlC7Cmw7lwyJVLtw,288
scipy/io/matlab/tests/data/teststring_7.1_GLNX86.mat,sha256=zo7sh-8dMpGqhoNxLEnfz3Oc7RonxiY5j0B3lxk0e8o,224
scipy/io/matlab/tests/data/teststring_7.4_GLNX86.mat,sha256=igL_CvtAcNEa1nxunDjQZY5wS0rJOlzsUkBiDreJssk,224
scipy/io/matlab/tests/data/teststringarray_4.2c_SOL2.mat,sha256=pRldk-R0ig1k3ouvaR9oVtBwZsQcDW_b4RBEDYu1-Vk,156
scipy/io/matlab/tests/data/teststringarray_6.1_SOL2.mat,sha256=B9IdaSsyb0wxjyYyHOj_GDO0laAeWDEJhoEhC9xdm1E,232
scipy/io/matlab/tests/data/teststringarray_6.5.1_GLNX86.mat,sha256=t4tKGJg2NEg_Ar5MkOjCoQb2hVL8Q_Jdh9FF4TPL_4g,232
scipy/io/matlab/tests/data/teststringarray_7.1_GLNX86.mat,sha256=lpYkBZX8K-c4FO5z0P9DMfYc7Y-yzyg11J6m-19uYTU,203
scipy/io/matlab/tests/data/teststringarray_7.4_GLNX86.mat,sha256=lG-c7U-5Bo8j8xZLpd0JAsMYwewT6cAw4eJCZH5xf6E,203
scipy/io/matlab/tests/data/teststruct_6.1_SOL2.mat,sha256=3GJbA4O7LP57J6IYzmJqTPeSJrEaiNSk-rg7h0ANR1w,608
scipy/io/matlab/tests/data/teststruct_6.5.1_GLNX86.mat,sha256=fRbqAnzTeOU3dTQx7O24MfMVFr6pM5u594FRrPPkYJE,552
scipy/io/matlab/tests/data/teststruct_7.1_GLNX86.mat,sha256=mCtI_Yot08NazvWHvehOZbTV4bW_I4-D5jBgJ6T9EbI,314
scipy/io/matlab/tests/data/teststruct_7.4_GLNX86.mat,sha256=52qaF4HRCtPl1jE6ljbkEl2mofZVAPpmBxrm-J5OTTI,314
scipy/io/matlab/tests/data/teststructarr_6.1_SOL2.mat,sha256=vneCpWBwApBGfeKzdZcybyajxjR-ZYf64j0l08_hU84,528
scipy/io/matlab/tests/data/teststructarr_6.5.1_GLNX86.mat,sha256=gqhRpSfNNB5SR9sCp-wWrvokr5VV_heGnvco6dmfOvY,472
scipy/io/matlab/tests/data/teststructarr_7.1_GLNX86.mat,sha256=6VDU0mtTBEG0bBHqKP1p8xq846eMhSZ_WvBZv8MzE7M,246
scipy/io/matlab/tests/data/teststructarr_7.4_GLNX86.mat,sha256=ejtyxeeX_W1a2rNrEUUiG9txPW8_UtSgt8IaDOxE2pg,246
scipy/io/matlab/tests/data/teststructnest_6.1_SOL2.mat,sha256=sbi0wUwOrbU-gBq3lyDwhAbvchdtOJkflOR_MU7uGKA,496
scipy/io/matlab/tests/data/teststructnest_6.5.1_GLNX86.mat,sha256=uTkKtrYBTuz4kICVisEaG7V5C2nJDKjy92mPDswTLPE,416
scipy/io/matlab/tests/data/teststructnest_7.1_GLNX86.mat,sha256=o4F2jOhYyNpJCo-BMg6v_ITZQvjenXfXHLq94e7iwRo,252
scipy/io/matlab/tests/data/teststructnest_7.4_GLNX86.mat,sha256=CNXO12O6tedEuMG0jNma4qfbTgCswAbHwh49a3uE3Yk,252
scipy/io/matlab/tests/data/testunicode_7.1_GLNX86.mat,sha256=KV97FCW-1XZiXrwXJoZPbgyAht79oIFHa917W1KFLwE,357
scipy/io/matlab/tests/data/testunicode_7.4_GLNX86.mat,sha256=9-8xzACZleBkMjZnbr8t4Ncs9B6mbzrONDblPnteBPU,357
scipy/io/matlab/tests/data/testvec_4_GLNX86.mat,sha256=GQzR3mBVS266_NBfrRC9X0dLgmeu8Jl4r4ZYMOrn1V0,93
scipy/io/matlab/tests/test_byteordercodes.py,sha256=YKPAsE36MRPDi3Jsh2QrinvLOEvDPmsBylkwUzqoDTg,967
scipy/io/matlab/tests/test_mio.py,sha256=5xPpg_tk0_OJ-HnaAS3bcryb8sUE1lO5wn9xxXWr6k8,44628
scipy/io/matlab/tests/test_mio5_utils.py,sha256=QsiolpbVo9kU8fbwd3qXv1bidUFBsjdQBa8mMoG_-1U,5599
scipy/io/matlab/tests/test_mio_funcs.py,sha256=JoLN42aZLd-f-kju-kTgRFQjQmEy4VtJlKgJ5EgnVFI,1443
scipy/io/matlab/tests/test_mio_utils.py,sha256=I4_z8My_JDy6jverX4bm-GeDNnnC6jX71hprsGSnCF8,1639
scipy/io/matlab/tests/test_miobase.py,sha256=G3Xm9Q14vWR37Wt7uxxgIBnLvmSkJ86GVABDhlJMflU,1496
scipy/io/matlab/tests/test_pathological.py,sha256=t0oCcPAaZ9XbWFXWu7HWjDY4tDniuLqQOyr6rwieCVM,1093
scipy/io/matlab/tests/test_streams.py,sha256=UtRkoFIJSTRJZZ-gCflcDej0iqFD76rFsOzB4QFbqeQ,7548
scipy/io/mmio.py,sha256=B55fPeX7f85gRK0OhQSyl5jXcexjLJMhmGrCDb9TcrA,807
scipy/io/netcdf.py,sha256=b4qPiQuQAE01Nb0M1YeX63os6MiWIETIf_1B3IbOhvM,1113
scipy/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/io/tests/__pycache__/test_fortran.cpython-38.pyc,,
scipy/io/tests/__pycache__/test_idl.cpython-38.pyc,,
scipy/io/tests/__pycache__/test_mmio.cpython-38.pyc,,
scipy/io/tests/__pycache__/test_netcdf.cpython-38.pyc,,
scipy/io/tests/__pycache__/test_paths.cpython-38.pyc,,
scipy/io/tests/__pycache__/test_wavfile.cpython-38.pyc,,
scipy/io/tests/data/Transparent Busy.ani,sha256=vwoK3ysYo87-TwzvjerHjFjSPIGpw83jjiMDXcHPWjA,4362
scipy/io/tests/data/array_float32_1d.sav,sha256=A_xXWkfS1sQCxP4ONezeEZvlKEXwZ1TPG2rCCFdmBNM,2628
scipy/io/tests/data/array_float32_2d.sav,sha256=qJmN94pywXznXMHzt-L6DJgaIq_FfruVKJl_LMaI8UU,3192
scipy/io/tests/data/array_float32_3d.sav,sha256=U7P6As7Nw6LdBY1pTOaW9C-O_NlXLXZwSgbT3H8Z8uk,13752
scipy/io/tests/data/array_float32_4d.sav,sha256=Tl6erEw_Zq3dwVbVyPXRWqB83u_o4wkIVFOe3wQrSro,6616
scipy/io/tests/data/array_float32_5d.sav,sha256=VmaBgCD854swYyLouDMHJf4LL6iUNgajEOQf0pUjHjg,7896
scipy/io/tests/data/array_float32_6d.sav,sha256=lb7modI0OQDweJWbDxEV2OddffKgMgq1tvCy5EK6sOU,19416
scipy/io/tests/data/array_float32_7d.sav,sha256=pqLWIoxev9sLCs9LLwxFlM4RCFwxHC4Q0dEEz578mpI,3288
scipy/io/tests/data/array_float32_8d.sav,sha256=R8A004f9XLWvF6eKMNEqIrC6PGP1vLZr9sFqawqM8ZA,13656
scipy/io/tests/data/array_float32_pointer_1d.sav,sha256=sV7qFNwHK-prG5vODa7m5HYK7HlH_lqdfsI5Y1RWDyg,2692
scipy/io/tests/data/array_float32_pointer_2d.sav,sha256=b0brvK6xQeezoRuujmEcJNw2v6bfASLM3FSY9u5dMSg,3256
scipy/io/tests/data/array_float32_pointer_3d.sav,sha256=a_Iyg1YjPBRh6B-N_n_BGIVjFje4K-EPibKV-bPbF7E,13816
scipy/io/tests/data/array_float32_pointer_4d.sav,sha256=cXrkHHlPyoYstDL_OJ15-55sZOOeDNW2OJ3KWhBv-Kk,6680
scipy/io/tests/data/array_float32_pointer_5d.sav,sha256=gRVAZ6jeqFZyIQI9JVBHed9Y0sjS-W4bLseb01rIcGs,7960
scipy/io/tests/data/array_float32_pointer_6d.sav,sha256=9yic-CQiS0YR_ow2yUA2Nix0Nb_YCKMUsIgPhgcJT1c,19480
scipy/io/tests/data/array_float32_pointer_7d.sav,sha256=Rp1s8RbW8eoEIRTqxba4opAyY0uhTuyy3YkwRlNspQU,3352
scipy/io/tests/data/array_float32_pointer_8d.sav,sha256=Wk3Dd2ClAwWprXLKZon3blY7aMvMrJqz_NXzK0J5MFY,13720
scipy/io/tests/data/example_1.nc,sha256=EkfC57dWXeljgXy5sidrJHJG12D1gmQUyPDK18WzlT4,1736
scipy/io/tests/data/example_2.nc,sha256=wywMDspJ2QT431_sJUr_5DHqG3pt9VTvDJzfR9jeWCk,272
scipy/io/tests/data/example_3_maskedvals.nc,sha256=P9N92jCJgKJo9VmNd7FeeJSvl4yUUFwBy6JpR4MeuME,1424
scipy/io/tests/data/fortran-3x3d-2i.dat,sha256=oYCXgtY6qqIqLAhoh_46ob_RVQRcV4uu333pOiLKgRM,451
scipy/io/tests/data/fortran-mixed.dat,sha256=zTi7RLEnyAat_DdC3iSEcSbyDtAu0aTKwUT-tExjasw,40
scipy/io/tests/data/fortran-sf8-11x1x10.dat,sha256=KwaOrZOAe-wRhuxvmHIK-Wr59us40MmiA9QyWtIAUaA,888
scipy/io/tests/data/fortran-sf8-15x10x22.dat,sha256=5ohvjjOUcIsGimSqDhpUUKwflyhVsfwKL5ElQe_SU0I,26408
scipy/io/tests/data/fortran-sf8-1x1x1.dat,sha256=Djmoip8zn-UcxWGUPKV5wzKOYOf7pbU5L7HaR3BYlec,16
scipy/io/tests/data/fortran-sf8-1x1x5.dat,sha256=Btgavm3w3c9md_5yFfq6Veo_5IK9KtlLF1JEPeHhZoU,48
scipy/io/tests/data/fortran-sf8-1x1x7.dat,sha256=L0r9yAEMbfMwYQytzYsS45COqaVk-o_hi6zRY3yIiO4,64
scipy/io/tests/data/fortran-sf8-1x3x5.dat,sha256=c2LTocHclwTIeaR1Pm3mVMyf5Pl_imfjIFwi4Lpv0Xs,128
scipy/io/tests/data/fortran-si4-11x1x10.dat,sha256=OesvSIGsZjpKZlZsV74PNwy0Co0KH8-3gxL9-DWoa08,448
scipy/io/tests/data/fortran-si4-15x10x22.dat,sha256=OJcKyw-GZmhHb8REXMsHDn7W5VP5bhmxgVPIAYG-Fj4,13208
scipy/io/tests/data/fortran-si4-1x1x1.dat,sha256=1Lbx01wZPCOJHwg99MBDuc6QZKdMnccxNgICt4omfFM,12
scipy/io/tests/data/fortran-si4-1x1x5.dat,sha256=L1St4yiHTA3v91JjnndYfUrdKfT1bWxckwnnrscEZXc,28
scipy/io/tests/data/fortran-si4-1x1x7.dat,sha256=Dmqt-tD1v2DiPZkghGGZ9Ss-nJGfei-3yFXPO5Acpk4,36
scipy/io/tests/data/fortran-si4-1x3x5.dat,sha256=3vl6q93m25jEcZVKD0CuKNHmhZwZKp-rv0tfHoPVP88,68
scipy/io/tests/data/invalid_pointer.sav,sha256=JmgoISXC4r5fSmI5FqyapvmzQ4qpYLf-9N7_Et1p1HQ,1280
scipy/io/tests/data/null_pointer.sav,sha256=P_3a_sU614F3InwM82jSMtWycSZkvqRn1apwd8XxbtE,2180
scipy/io/tests/data/scalar_byte.sav,sha256=dNJbcE5OVDY_wHwN_UBUtfIRd13Oqu-RBEO74g5SsBA,2076
scipy/io/tests/data/scalar_byte_descr.sav,sha256=DNTmDgDWOuzlQnrceER6YJ0NutUUwZ9tozVMBWQmuuY,2124
scipy/io/tests/data/scalar_complex32.sav,sha256=NGd-EvmFZgt8Ko5MP3T_TLwyby6yS0BXM_OW8197hpU,2076
scipy/io/tests/data/scalar_complex64.sav,sha256=gFBWtxuAajazupGFSbvlWUPDYK-JdWgZcEWih2-7IYU,2084
scipy/io/tests/data/scalar_float32.sav,sha256=EwWQw2JTwq99CHVpDAh4R20R0jWaynXABaE2aTRmXrs,2072
scipy/io/tests/data/scalar_float64.sav,sha256=iPcDlgF1t0HoabvNLWCbSiTPIa9rvVEbOGGmE_3Ilsk,2076
scipy/io/tests/data/scalar_heap_pointer.sav,sha256=JXZbPmntXILsNOuLIKL8qdu8gDJekYrlN9DQxAWve0E,2204
scipy/io/tests/data/scalar_int16.sav,sha256=kDBLbPYGo2pzmZDhyl8rlDv0l6TMEWLIoLtmgJXDMkk,2072
scipy/io/tests/data/scalar_int32.sav,sha256=IzJwLvEoqWLO5JRaHp8qChfptlauU-ll3rb0TfDDM8Y,2072
scipy/io/tests/data/scalar_int64.sav,sha256=-aSHQRiaE3wjAxINwuLX33_8qmWl4GUkTH45elTkA-8,2076
scipy/io/tests/data/scalar_string.sav,sha256=AQ7iZ8dKk9QfnLdP9idKv1ojz0M_SwpL7XAUmbHodDQ,2124
scipy/io/tests/data/scalar_uint16.sav,sha256=928fmxLsQM83ue4eUS3IEnsLSEzmHBklDA59JAUvGK8,2072
scipy/io/tests/data/scalar_uint32.sav,sha256=X3RbPhS6_e-u-1S1gMyF7s9ys7oV6ZNwPrJqJ6zIJsk,2072
scipy/io/tests/data/scalar_uint64.sav,sha256=ffVyS2oKn9PDtWjJdOjSRT2KZzy6Mscgd4u540MPHC4,2076
scipy/io/tests/data/struct_arrays.sav,sha256=TzH-Gf0JgbP_OgeKYbV8ZbJXvWt1VetdUr6C_ziUlzg,2580
scipy/io/tests/data/struct_arrays_byte_idl80.sav,sha256=oOmhTnmKlE60-JMJRRMv_zfFs4zqioMN8QA0ldlgQZo,1388
scipy/io/tests/data/struct_arrays_replicated.sav,sha256=kXU8j9QI2Q8D22DVboH9fwwDQSLVvuWMJl3iIOhUAH8,2936
scipy/io/tests/data/struct_arrays_replicated_3d.sav,sha256=s3ZUwhT6TfiVfk4AGBSyxYR4FRzo4sZQkTxFCJbIQMI,4608
scipy/io/tests/data/struct_inherit.sav,sha256=4YajBZcIjqMQ4CI0lRUjXpYDY3rI5vzJJzOYpjWqOJk,2404
scipy/io/tests/data/struct_pointer_arrays.sav,sha256=fkldO6-RO2uAN_AI9hM6SEaBPrBf8TfiodFGJpViaqg,2408
scipy/io/tests/data/struct_pointer_arrays_replicated.sav,sha256=eKVerR0LoD9CuNlpwoBcn7BIdj3-8x56VNg--Qn7Hgc,2492
scipy/io/tests/data/struct_pointer_arrays_replicated_3d.sav,sha256=vsqhGpn3YkZEYjQuI-GoX8Jg5Dv8A2uRtP0kzQkq4lg,2872
scipy/io/tests/data/struct_pointers.sav,sha256=Zq6d5V9ZijpocxJpimrdFTQG827GADBkMB_-6AweDYI,2268
scipy/io/tests/data/struct_pointers_replicated.sav,sha256=aIXPBIXTfPmd4IaLpYD5W_HUoIOdL5Y3Hj7WOeRM2sA,2304
scipy/io/tests/data/struct_pointers_replicated_3d.sav,sha256=t1jhVXmhW6VotQMNZ0fv0sDO2pkN4EutGsx5No4VJQs,2456
scipy/io/tests/data/struct_scalars.sav,sha256=LYICjERzGJ_VvYgtwJ_Up2svQTv8wBzNcVD3nsd_OPg,2316
scipy/io/tests/data/struct_scalars_replicated.sav,sha256=lw3fC4kppi6BUWAd4n81h8_KgoUdiJl5UIt3CvJIuBs,2480
scipy/io/tests/data/struct_scalars_replicated_3d.sav,sha256=xVAup6f1dSV_IsSwBQC3KVs0eLEZ6-o5EaZT9yUoDZI,3240
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-be.wav,sha256=gjv__ng9xH_sm34hyxCbCgO4AP--PZAfDOArH5omkjM,3586
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-le.wav,sha256=H0LLyv2lc2guzYGnx4DWXU6vB57JrRX-G9Dd4qGh0hM,3586
scipy/io/tests/data/test-44100Hz-be-1ch-4bytes.wav,sha256=KKz9SXv_R3gX_AVeED2vyhYnj4BvD1uyDiKpCT3ulZ0,17720
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof-no-data.wav,sha256=YX1g8qdCOAG16vX9G6q4SsfCj2ZVk199jzDQ8S0zWYI,72
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof.wav,sha256=bFrsRqw0QXmsaDtjD6TFP8hZ5jEYMyaCmt-ka_C6GNk,1024
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-incomplete-chunk.wav,sha256=zMnhvZvrP4kyOWKVKfbBneyv03xvzgqXYhHNxsAxDJ4,13
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes.wav,sha256=9qTCvpgdz3raecVN1ViggHPnQjBf47xmXod9iCDsEik,17720
scipy/io/tests/data/test-48000Hz-2ch-64bit-float-le-wavex.wav,sha256=EqYBnEgTxTKvaTAtdA5HIl47CCFIje93y4hawR6Pyu0,7792
scipy/io/tests/data/test-8000Hz-be-3ch-5S-24bit.wav,sha256=hGYchxQFjrtvZCBo0ULi-xdZ8krqXcKdTl3NSUfqe8k,90
scipy/io/tests/data/test-8000Hz-le-1ch-10S-20bit-extra.wav,sha256=h8CXsW5_ShKR197t_d-TUTlgDqOZ-7wK_EcVGucR-aY,74
scipy/io/tests/data/test-8000Hz-le-1ch-1byte-ulaw.wav,sha256=BoUCDct3GiY_JJV_HoghF3mzAebT18j02c-MOn19KxU,70
scipy/io/tests/data/test-8000Hz-le-2ch-1byteu.wav,sha256=R6EJshvQp5YVR4GB9u4Khn5HM1VMfJUj082i8tkBIJ8,1644
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit-inconsistent.wav,sha256=t2Mgri3h6JLQDekrwIhDBOaG46OUzHynUz0pKbvOpNU,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit.wav,sha256=yCv0uh-ux_skJsxeOjzog0YBk3ZQO_kw5HJHMqtVyI0,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-36bit.wav,sha256=oiMVsQV9-qGBz_ZwsfAkgA9BZXNjXbH4zxCGvvdT0RY,120
scipy/io/tests/data/test-8000Hz-le-3ch-5S-45bit.wav,sha256=e97XoPrPGJDIh8nO6mii__ViY5yVlmt4OnPQoDN1djs,134
scipy/io/tests/data/test-8000Hz-le-3ch-5S-53bit.wav,sha256=wbonKlzvzQ_bQYyBsj-GwnihZOhn0uxfKhL_nENCGNc,150
scipy/io/tests/data/test-8000Hz-le-3ch-5S-64bit.wav,sha256=Uu5QPQcbtnFlnxOd4zFGxpiTC4wgdp6JOoYJ2VMZIU0,164
scipy/io/tests/data/test-8000Hz-le-4ch-9S-12bit.wav,sha256=1F67h8tr2xz0C5K21T9y9gspcGA0qnSOzsl2vjArAMs,116
scipy/io/tests/data/test-8000Hz-le-5ch-9S-5bit.wav,sha256=TJvGU7GpgXdCrdrjzMlDtpieDMnDK-lWMMqlWjT23BY,89
scipy/io/tests/data/various_compressed.sav,sha256=H-7pc-RCQx5y6_IbHk1hB6OfnhvuPyW6EJq4EwI9iMc,1015
scipy/io/tests/test_fortran.py,sha256=S68hJoxMZgGmyEvQZewuii-9txrUMZR8zP0Q6iMFv_8,7808
scipy/io/tests/test_idl.py,sha256=fwn2fcUPUBViBTbk7hvxA6dPfZ9ju6g1MRT-fLZTJ-o,20324
scipy/io/tests/test_mmio.py,sha256=7Kn9K_VbUcrG5pf1hT7CChVgb7Sli2S4un_FMuA8I3o,27344
scipy/io/tests/test_netcdf.py,sha256=F2zfYYREKjLZsjmqQn_-_VGKAi9YiZe2Zj14ZHyOr4c,19860
scipy/io/tests/test_paths.py,sha256=yfqQtS6YQkg2SFa4_YbGBqZlIhd-brGut6jSRDtv4yc,3271
scipy/io/tests/test_wavfile.py,sha256=TnsS-_8CoY0ZRFjR7N6E9UYK5A2lCHD5SQH1LOc8d10,15719
scipy/io/wavfile.py,sha256=z782OeGNC4ysiYqqfLSDUkgqHDlNDUIMDC48Q_LK_P0,27482
scipy/linalg.pxd,sha256=KI69T_3C0Qavl9-kv8POc1NyDRzueB6yEF1KhDfIrsA,49
scipy/linalg/__init__.py,sha256=hG961xOGgcInmT7NlE5JE4Qgm8uj6QiSGtnlNFYMqmw,7834
scipy/linalg/__pycache__/__init__.cpython-38.pyc,,
scipy/linalg/__pycache__/_basic.cpython-38.pyc,,
scipy/linalg/__pycache__/_decomp.cpython-38.pyc,,
scipy/linalg/__pycache__/_decomp_cholesky.cpython-38.pyc,,
scipy/linalg/__pycache__/_decomp_cossin.cpython-38.pyc,,
scipy/linalg/__pycache__/_decomp_ldl.cpython-38.pyc,,
scipy/linalg/__pycache__/_decomp_lu.cpython-38.pyc,,
scipy/linalg/__pycache__/_decomp_polar.cpython-38.pyc,,
scipy/linalg/__pycache__/_decomp_qr.cpython-38.pyc,,
scipy/linalg/__pycache__/_decomp_qz.cpython-38.pyc,,
scipy/linalg/__pycache__/_decomp_schur.cpython-38.pyc,,
scipy/linalg/__pycache__/_decomp_svd.cpython-38.pyc,,
scipy/linalg/__pycache__/_expm_frechet.cpython-38.pyc,,
scipy/linalg/__pycache__/_flinalg_py.cpython-38.pyc,,
scipy/linalg/__pycache__/_interpolative_backend.cpython-38.pyc,,
scipy/linalg/__pycache__/_matfuncs.cpython-38.pyc,,
scipy/linalg/__pycache__/_matfuncs_inv_ssq.cpython-38.pyc,,
scipy/linalg/__pycache__/_matfuncs_sqrtm.cpython-38.pyc,,
scipy/linalg/__pycache__/_misc.cpython-38.pyc,,
scipy/linalg/__pycache__/_procrustes.cpython-38.pyc,,
scipy/linalg/__pycache__/_sketches.cpython-38.pyc,,
scipy/linalg/__pycache__/_solvers.cpython-38.pyc,,
scipy/linalg/__pycache__/_special_matrices.cpython-38.pyc,,
scipy/linalg/__pycache__/_testutils.cpython-38.pyc,,
scipy/linalg/__pycache__/basic.cpython-38.pyc,,
scipy/linalg/__pycache__/blas.cpython-38.pyc,,
scipy/linalg/__pycache__/decomp.cpython-38.pyc,,
scipy/linalg/__pycache__/decomp_cholesky.cpython-38.pyc,,
scipy/linalg/__pycache__/decomp_lu.cpython-38.pyc,,
scipy/linalg/__pycache__/decomp_qr.cpython-38.pyc,,
scipy/linalg/__pycache__/decomp_schur.cpython-38.pyc,,
scipy/linalg/__pycache__/decomp_svd.cpython-38.pyc,,
scipy/linalg/__pycache__/flinalg.cpython-38.pyc,,
scipy/linalg/__pycache__/interpolative.cpython-38.pyc,,
scipy/linalg/__pycache__/lapack.cpython-38.pyc,,
scipy/linalg/__pycache__/matfuncs.cpython-38.pyc,,
scipy/linalg/__pycache__/misc.cpython-38.pyc,,
scipy/linalg/__pycache__/special_matrices.cpython-38.pyc,,
scipy/linalg/_basic.py,sha256=F8Z7ezEJ4jAWzH0sV7xNzreGilR_GYK2nbtUX6drDcI,66196
scipy/linalg/_blas_subroutine_wrappers.f,sha256=xCDifo7qRixvt11wrQR6BvQf3-l6rtH1H4cl1Snhz3U,8219
scipy/linalg/_blas_subroutines.h,sha256=z_4wvAsR2qygLTOQiqbv7PEMc8a1dpVnULvFKLP_VoY,19234
scipy/linalg/_cythonized_array_utils.cp38-win_amd64.dll.a,sha256=iz0_k8BVfd0tctnRfXG9SaYNXDb_xKUWKcVcDxu4kkU,1732
scipy/linalg/_cythonized_array_utils.cp38-win_amd64.pyd,sha256=YjvZFLpZnaG9c9zC_aAOuPpj3Oh1dXXoolhnyF206nc,397824
scipy/linalg/_cythonized_array_utils.pxd,sha256=tP35YEdRD-cRwsilWlfD1TuJnnrQJzB_y80Fg5DVKRE,901
scipy/linalg/_cythonized_array_utils.pyi,sha256=hch_W5DDPWNFsJPuDbQoQVHZrWMJfvTmt0UyeW-H8eU,361
scipy/linalg/_decomp.py,sha256=l5Cnztef-wBITaGPxOYydS9se7Cgh7a68neh3XPetSU,63008
scipy/linalg/_decomp_cholesky.py,sha256=qj5SCRMOp6Zrs08DcoP7Nhrs3cD4XKbms_OWvRBWok4,12261
scipy/linalg/_decomp_cossin.py,sha256=-xRojqDIcaEW1vaS6SXbkd6khS_xG_N0TtxLUm6iGGo,9360
scipy/linalg/_decomp_ldl.py,sha256=VVA_IY7ol92EisVNUDDjl7KMYjSC8LgfuoPUIkR_sbg,12868
scipy/linalg/_decomp_lu.py,sha256=b3Y54LYmbLddIDVXD3XMliKZYHYNfa0AMjPE9WJ6ioo,7148
scipy/linalg/_decomp_polar.py,sha256=4KAJQAgKoN6W_onEM-m3oGqfwCK3__ASC7UFMFdKcac,3689
scipy/linalg/_decomp_qr.py,sha256=WioKKWcHa753N_JBtSH3BdrppWZi1Dk-gZi7XkpYQ8k,14156
scipy/linalg/_decomp_qz.py,sha256=VDjMbx68uEfRi1vqqkzFIksoY-Ke8v3pZ9OGMlo4aN0,16781
scipy/linalg/_decomp_schur.py,sha256=s66IIG8kT0lCE9-lEefnTCd2jBU5koDSmmKUhpXYJzA,10566
scipy/linalg/_decomp_svd.py,sha256=PPND6h0dDrTPKzhERr0UOn52tWq8WB_dz_RieOYCY9I,15410
scipy/linalg/_decomp_update.cp38-win_amd64.dll.a,sha256=N62AqCB0z4X8de0I-eSf_WeyiZLiJSNn4iKTaeuDdN4,1620
scipy/linalg/_decomp_update.cp38-win_amd64.pyd,sha256=xppJ7uNOIRXlLehHkppmZjFSbhcBXu9eQa3sL0BAecg,314880
scipy/linalg/_expm_frechet.py,sha256=M42pBJ5Czz9A3-IVAWguiq2BBk9-9Bsrft6GUhu5e-M,12739
scipy/linalg/_fblas.cp38-win_amd64.dll.a,sha256=7BEUqlS2Qwi8bEvEQegzvy33dWqhvXH4hxeUXkE0bFA,1524
scipy/linalg/_fblas.cp38-win_amd64.pyd,sha256=Nw8qWLnxma0M6blEVVz7n6CTrGd4kIennjMQkfQEtU4,633344
scipy/linalg/_flapack.cp38-win_amd64.dll.a,sha256=3xap7P5fszScRq_kb01JBiqC8LJI9l2leg8V_Iqq16c,1548
scipy/linalg/_flapack.cp38-win_amd64.pyd,sha256=ESE6K2CzUKpFCsbOnlmhUZfv5_DefWzwM8AKAd-TZus,2138624
scipy/linalg/_flinalg.cp38-win_amd64.dll.a,sha256=gByOrmsLfuRUroJOKljm7Qd6GK2NfE5IMU247fy7iRg,1548
scipy/linalg/_flinalg.cp38-win_amd64.pyd,sha256=GjN-df_eib3w9oIqS1k4wXwE4C9FzrLEAc0RlmD3ZNM,94208
scipy/linalg/_flinalg_py.py,sha256=dJnARQShtiYnyqzVXi6KAUm4wQ4KcBekksfCgit2058,1714
scipy/linalg/_interpolative.cp38-win_amd64.dll.a,sha256=xUIfSzkeYYTsZ93ofuI4n3N7t6uTzYdegXAALICaEwQ,1620
scipy/linalg/_interpolative.cp38-win_amd64.pyd,sha256=RH0bA9P_glxwD43WFpR4PGfTjPjt_VUth4zh1r-OEVc,734208
scipy/linalg/_interpolative_backend.py,sha256=mwdx_kKIlNkWDBeLqx7WnD2ce4UEo2nXqCSyKHny-9s,46873
scipy/linalg/_lapack_subroutine_wrappers.f,sha256=p9h3y4oiQiOBihKeoF08B9R45dV5-MBeUsUFeREkyOg,36415
scipy/linalg/_lapack_subroutines.h,sha256=jzZsEPEbMC3r7z1TqeBDyWxaI1YSk4OXkc_Qt1l4yeM,248359
scipy/linalg/_matfuncs.py,sha256=NmUNLuh1swePdYB9du8DPBAAl0qEs-A7qHSLVK4ZmtU,25943
scipy/linalg/_matfuncs_expm.cp38-win_amd64.dll.a,sha256=vMBAjlWzH3tZbC6jo8IqbDUG1b87FhgqnQv_8fBtbJ0,1620
scipy/linalg/_matfuncs_expm.cp38-win_amd64.pyd,sha256=nzGGEIEKx26a10QOWnWGbxBUaV2OdItGQ-w23QeTDXk,362496
scipy/linalg/_matfuncs_expm.pyi,sha256=Z_PVEY8ggzT5DxIArk-FvvLdi8eloPZM5qiAMMPl6_4,200
scipy/linalg/_matfuncs_inv_ssq.py,sha256=rS8WTKaVwoiwW9oF_B1f_Q1QEy8GiILKYJafcrlgvco,28924
scipy/linalg/_matfuncs_sqrtm.py,sha256=BknNKzJrWR-Rw5mgbqU6ji_BHxtV_Euxyi75ABBSEBw,6847
scipy/linalg/_matfuncs_sqrtm_triu.cp38-win_amd64.dll.a,sha256=MnkMkh5vuk7E7fpujOqqwjQMofki-f9CAGcK4nQhbho,1692
scipy/linalg/_matfuncs_sqrtm_triu.cp38-win_amd64.pyd,sha256=YlcAOTLSEBlMUec64y2MNRSeuJweaDRjnzoRxB6fWDc,187904
scipy/linalg/_misc.py,sha256=GuAl0DgvKf35a2oyvYgcud84O0e3DCQLSUdR2AzXE3k,6474
scipy/linalg/_procrustes.py,sha256=PKpozLb6EnZmUKPr4guUukDFIVsRnoENh0wSzdmlfdo,2835
scipy/linalg/_sketches.py,sha256=nVwWE2o7wZw69rFbEDoosoZoNm31jW1iM9fxhdz5BnU,6324
scipy/linalg/_solve_toeplitz.cp38-win_amd64.dll.a,sha256=XJYNAFpDVExdvFlilT_XCJb_HZuG2TftV-GNMklOyvQ,1636
scipy/linalg/_solve_toeplitz.cp38-win_amd64.pyd,sha256=c-lPQoS5_CBFVqrL04T6V8xLHBnGlfpwJr8e-yZ6-ho,208896
scipy/linalg/_solvers.py,sha256=GvWjc5i1WAdXI-fkSuREA4uvi0KBlZtmG5lRkb55Sik,29226
scipy/linalg/_special_matrices.py,sha256=Dlg307dAGn1MJObtisMAFGhstQsqNB_INJTFG0K38AM,41431
scipy/linalg/_testutils.py,sha256=HdXoW_CG1uLEbU8bF3evtij_4TNQEqPBtBdrqvozMjk,1795
scipy/linalg/basic.py,sha256=4u-n43f4Kd7InroewxBajK6vNOvrYI643v0EGl-g_2Q,1057
scipy/linalg/blas.py,sha256=0zu_naR0GC_Ib2imOfJncSi5SoJsyLd7ned7R1mL280,12167
scipy/linalg/cython_blas.cp38-win_amd64.dll.a,sha256=wsrxRX3w_1zr2B8gqgvhc36LQrP1a4azX8YHapZ8GQc,1588
scipy/linalg/cython_blas.cp38-win_amd64.pyd,sha256=odbd-TaxMJd8PMTUx3yiexJO3FYzDMSK9XxRVHedj0g,211456
scipy/linalg/cython_blas.pxd,sha256=KrK1unX8rOb2eA8VMCVcCgK1IAfHqGsmaa_uCxc-Fk8,14717
scipy/linalg/cython_blas.pyx,sha256=NnbogwNJlM2l0cc4kBDoPmC_y6LWxHS1CUdYPuIwV_w,64424
scipy/linalg/cython_lapack.cp38-win_amd64.dll.a,sha256=3QabIr7V6C0tcOkvIe-8zp5dTiX7ItGKsbf7-HtYnfA,1612
scipy/linalg/cython_lapack.cp38-win_amd64.pyd,sha256=UkOeaVf_7wm9ClhVy_UOMiQnt4DfNQTcfc42tdb689U,510464
scipy/linalg/cython_lapack.pxd,sha256=SWzpIFM1nF1LTfw2fLEKjGxGJ6M9-S9rO_k9RlgLBqc,195600
scipy/linalg/cython_lapack.pyx,sha256=OeH9w6epiQEIsWfZVzDn9XdVQCleuv7BFvm6U4dQP8g,697453
scipy/linalg/decomp.py,sha256=s3_tcXlzoQphoOv0ufw79or0q3FIrmLC9spAQLVIStU,1089
scipy/linalg/decomp_cholesky.py,sha256=n---n4edzo4hyF7vhqNHDQVcLFgDUM9G5c-NSB0mNM4,946
scipy/linalg/decomp_lu.py,sha256=5Kst2Svl_NFEWOTO5qTwgo-0E_WboyK_UzR8p_Yxi2k,886
scipy/linalg/decomp_qr.py,sha256=ADq7RNc1RJ7H0-CP5bc7v8PnV1_zVRX7Mh_vphpx8R4,823
scipy/linalg/decomp_schur.py,sha256=LdwZVICTCzbhg6pqpFBJVlCUcyRq3Y8M3C9WknpDFDI,910
scipy/linalg/decomp_svd.py,sha256=_Fiu3kLKkJJkS0HiIzNtm2Ew3icRz2kwZye9R2cayJc,878
scipy/linalg/flinalg.py,sha256=3he_hg2IyL7iPo084U7ed_PtSgF8nnaNuf4M6vhvOaI,700
scipy/linalg/interpolative.py,sha256=iOOVRCpY6hm4f4UecxqXQHOCUknyxdgNLBO3aqZPW2k,33095
scipy/linalg/lapack.py,sha256=IdhupdY73PhU_ZpR78VtBW5UopMhZXocK1QileIYAxU,16662
scipy/linalg/matfuncs.py,sha256=GFLrA47eIBKkBlU6i7cJLAR-oWG_O7mKvSA6CM770kQ,1130
scipy/linalg/misc.py,sha256=D4sUpr3GSPGub5WuJF4YZeP2XjAGYLqC2Yk0u-WDmuY,827
scipy/linalg/special_matrices.py,sha256=Vojq6AIpQVJZvsaaEDzIimjK3BZeNGHfybpa0_7knyU,1056
scipy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/linalg/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_basic.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_blas.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_cython_blas.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_cython_lapack.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_cythonized_array_utils.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_decomp.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cholesky.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cossin.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_ldl.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_polar.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_update.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_fblas.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_interpolative.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_lapack.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_matfuncs.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_matmul_toeplitz.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_misc.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_procrustes.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_sketches.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_solve_toeplitz.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_solvers.cpython-38.pyc,,
scipy/linalg/tests/__pycache__/test_special_matrices.cpython-38.pyc,,
scipy/linalg/tests/data/carex_15_data.npz,sha256=E_PhSRqHa79Z1-oQrSnB-bWZaiq5khbzHVv81lkBLB4,34462
scipy/linalg/tests/data/carex_18_data.npz,sha256=Wfg5Rn8nUrffb7bUCUOW7dMqWSm3ZPf_oeZmZDHmysY,161487
scipy/linalg/tests/data/carex_19_data.npz,sha256=OOj8ewQd8LI9flyhXq0aBl5kZ2Ee-ahIzH25P4Ct_Yc,34050
scipy/linalg/tests/data/carex_20_data.npz,sha256=FOIi00pxGMcoShZ1xv7O7ne4TflRpca6Kl7p_zBU-h0,31231
scipy/linalg/tests/data/carex_6_data.npz,sha256=GyoHNrVB6_XEubTADW2rKB5zyfuZE8biWBp4Gze2Avk,15878
scipy/linalg/tests/data/gendare_20170120_data.npz,sha256=o9-rRR2dXCAkPg7YXNi2yWV2afuaD4O1vhZVhXg9VbU,2164
scipy/linalg/tests/test_basic.py,sha256=ksg13oujD5DKpMQjO-zN9tg1N485n2e4vPPSbuHIS2M,66511
scipy/linalg/tests/test_blas.py,sha256=RkJSFRsLoW1ZfZZ5h_XM89RwgVn49sE7CqzaMVTvI4s,41302
scipy/linalg/tests/test_cython_blas.py,sha256=xQfOXafxyJ67WjhdSDbRIknQLoVhqSd4GfIU1DzBiAE,4337
scipy/linalg/tests/test_cython_lapack.py,sha256=L92_DkwZQyPDplzVwrdbMDLCHaXu5WPACwG-dwACGC0,591
scipy/linalg/tests/test_cythonized_array_utils.py,sha256=uGVyBm288GVIieUXpc0Iu81z9u8Vc6Q6neIN2aCnIvE,3961
scipy/linalg/tests/test_decomp.py,sha256=mpKux478ygJfNCHVWI8DtagMjiJXgWXpwimdRY3eO8Q,111068
scipy/linalg/tests/test_decomp_cholesky.py,sha256=FtAiYQKLuej7DyViryuIMOTfmlnYoW3sT0zqSfgTP4I,7467
scipy/linalg/tests/test_decomp_cossin.py,sha256=2hK8yN25BPcsjX6sI_Sb9Fiie203iRBdmKGAFWETO78,5927
scipy/linalg/tests/test_decomp_ldl.py,sha256=X_z4g1GkUdAkemE0ctCHKPEqx9DJKggCXkvkFyDRn8o,5115
scipy/linalg/tests/test_decomp_polar.py,sha256=dx9ubrGOCBZW2IVPBveUt6v6wWldGUOrh0MzFlB6h7w,2736
scipy/linalg/tests/test_decomp_update.py,sha256=FMBxtw8KdQ8s3Cj0XMc3nxRQkbsMn6JLsKGQHwGiN14,70186
scipy/linalg/tests/test_fblas.py,sha256=oJVVXNpZHFtLOrjaqFsI-m-Eyaq32F7tZlWeZiqc9xA,19292
scipy/linalg/tests/test_interpolative.py,sha256=n3KneEG0Yo1UYgr54PDlEjNx9GkBq1wfgA5pbU63mgA,9197
scipy/linalg/tests/test_lapack.py,sha256=05_X_C_WqbjcPLtS8RHCGXI1xiREijbg8PH5E58G824,128334
scipy/linalg/tests/test_matfuncs.py,sha256=sSEWxP-YONsq4oDIWuw1wAlIz9yMCecDjy1dXrj-r4o,39670
scipy/linalg/tests/test_matmul_toeplitz.py,sha256=jrzob-NZ16k_1cYe-7xqI5L8ic7VMYxVlqG7IyiMTy4,3995
scipy/linalg/tests/test_misc.py,sha256=k0264gAdjSMDmE4KRZcMztRKkkwWuzblNOrAD4K17Ec,81
scipy/linalg/tests/test_procrustes.py,sha256=JalCsDisOMEY2lD9RSHfSU6X_W4wVmev_9BZpn8VAko,6949
scipy/linalg/tests/test_sketches.py,sha256=RMUKNRQTj1o08CGHGf68bsES-jzLuN0AswGG5aNXsk0,4078
scipy/linalg/tests/test_solve_toeplitz.py,sha256=1y0Vec8ZOK1tmjclVMXVxCJJ8ZgO_s4TguYRFawrKcU,4131
scipy/linalg/tests/test_solvers.py,sha256=1iE8UEusOqQui_TzSmmowD4xyOQQaW5rc1THlOHhSXY,31840
scipy/linalg/tests/test_special_matrices.py,sha256=o-HtSqtn-8d6p3RPsKcwXfP1aUpIgQIN52zOBbvXQmU,27400
scipy/misc/__init__.py,sha256=yGB5XvVr2KLtK1e40nBG1TTne-7oxFrwSYufByVeUDE,1793
scipy/misc/__pycache__/__init__.cpython-38.pyc,,
scipy/misc/__pycache__/_common.cpython-38.pyc,,
scipy/misc/__pycache__/common.cpython-38.pyc,,
scipy/misc/__pycache__/doccer.cpython-38.pyc,,
scipy/misc/_common.py,sha256=ZXc4BohxuQbN0NZVAcsnYAn58rJ4mchMkIR1c_-6Yds,11462
scipy/misc/ascent.dat,sha256=6KhJOUhEY6uAUa7cW0CqJiqzOpHWRYps0TxqHK1aAj0,527630
scipy/misc/common.py,sha256=emcNIDg_5jhB5ext7Y8sPbtMfbSTtPRrzAQCDgzKDBg,898
scipy/misc/doccer.py,sha256=muEPqmmwNGq3eYxz0fVBbqO2TY9djYJ5iEKZlzXAPq8,795
scipy/misc/ecg.dat,sha256=8grTNl-5t_hF0OXEi2_mcIE3fuRmw6Igt_afNciVi68,119035
scipy/misc/face.dat,sha256=nYsLTQgTE-K0hXSMdwRy5ale0XOBRog9hMcDBJPoKIY,1581821
scipy/misc/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/misc/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/misc/tests/__pycache__/test_common.cpython-38.pyc,,
scipy/misc/tests/__pycache__/test_config.cpython-38.pyc,,
scipy/misc/tests/__pycache__/test_doccer.cpython-38.pyc,,
scipy/misc/tests/test_common.py,sha256=rfcxQnBUc22G95MDYoYRmIciDjL8Pgmrf0fbMp-pqGs,859
scipy/misc/tests/test_config.py,sha256=Ma6jMr-KuIuDdFFz88ZHmFcawmOugzfJnspZv9Aqda4,1288
scipy/misc/tests/test_doccer.py,sha256=CWV_zZfJCz88jyPJ-pcIk8hMXxmXh_f58LgJjjBbJzg,3872
scipy/ndimage/__init__.py,sha256=d3GLXQWYFAp4l1jLR4uTHe8CTuIWxLQPrV1kDyaciLc,5324
scipy/ndimage/__pycache__/__init__.cpython-38.pyc,,
scipy/ndimage/__pycache__/_filters.cpython-38.pyc,,
scipy/ndimage/__pycache__/_fourier.cpython-38.pyc,,
scipy/ndimage/__pycache__/_interpolation.cpython-38.pyc,,
scipy/ndimage/__pycache__/_measurements.cpython-38.pyc,,
scipy/ndimage/__pycache__/_morphology.cpython-38.pyc,,
scipy/ndimage/__pycache__/_ni_docstrings.cpython-38.pyc,,
scipy/ndimage/__pycache__/_ni_support.cpython-38.pyc,,
scipy/ndimage/__pycache__/filters.cpython-38.pyc,,
scipy/ndimage/__pycache__/fourier.cpython-38.pyc,,
scipy/ndimage/__pycache__/interpolation.cpython-38.pyc,,
scipy/ndimage/__pycache__/measurements.cpython-38.pyc,,
scipy/ndimage/__pycache__/morphology.cpython-38.pyc,,
scipy/ndimage/_ctest.cp38-win_amd64.dll.a,sha256=8ZVlcC2PliMyCxKz4HKOSDBbCdqJQWNQS4s5xkdQ3Mo,1524
scipy/ndimage/_ctest.cp38-win_amd64.pyd,sha256=_MQndRW_1RCeKOWrkk3TveNzT3taqKeh1Z8HeBroLbo,16896
scipy/ndimage/_cytest.cp38-win_amd64.dll.a,sha256=o_A8auRSbQNhgad-n6XcKhEl7aXqpD4aUcKj85qowXg,1540
scipy/ndimage/_cytest.cp38-win_amd64.pyd,sha256=3RfBHS0vvWwzitrF7LENYN9txRvSmrz3rCH17VAz9gQ,51200
scipy/ndimage/_filters.py,sha256=yRBGlVahYcK0lVl8am-9vSPHV7KX3xiCBBRgy41Py7M,59077
scipy/ndimage/_fourier.py,sha256=qECDrOx4UOXmnfMU7PEmP0bWlRBnK_YkLgxAcvdGMGc,11697
scipy/ndimage/_interpolation.py,sha256=pFsue5VMXBNuQt2VysIzDbVXvG-tXIJrVr06VFr-f3c,36397
scipy/ndimage/_measurements.py,sha256=frrH0mD16ouGgKQ7mODQX2Q65BDsUuT4GQ7xMCrL4lg,57609
scipy/ndimage/_morphology.py,sha256=YEyOJfR1MDxOCLj7hd_hgFdMO-oVIlt-OzwjQs9y8-I,89861
scipy/ndimage/_nd_image.cp38-win_amd64.dll.a,sha256=4VwLTSmp9-OvI8gsdGhi98d11M8Hn9EL0c5kmiWji6s,1564
scipy/ndimage/_nd_image.cp38-win_amd64.pyd,sha256=xte3cxeg20BPW8rIG91qkRdx3jPVjY5U-AbHVqgE184,153600
scipy/ndimage/_ni_docstrings.py,sha256=yq3jX_2G9UdXeWtMWv8cbr5EA3GBPN0yGHArqInqzT8,8724
scipy/ndimage/_ni_label.cp38-win_amd64.dll.a,sha256=YzWaxK4kR5Ood1LXesiTs3kCanM0sJSPVHAQK8PjVao,1564
scipy/ndimage/_ni_label.cp38-win_amd64.pyd,sha256=V1ezcAZTCOXYyQ2xHMYjIbzgG-_oXVtPlgG5yNZJHgQ,309248
scipy/ndimage/_ni_support.py,sha256=euuzLZGgRh4yRlSF8kxSDEQof0l836wFT4QzcydK5B0,3924
scipy/ndimage/filters.py,sha256=yFvle_r_zDEQDDzQUCBVvAIPz04nhTRN9r0wfT4bxzQ,1252
scipy/ndimage/fourier.py,sha256=XJ6JMT-mn5xTcJwSttqA8trKxsU2JgHzPiNXVFeBffM,869
scipy/ndimage/interpolation.py,sha256=u6pLDi-EdABzAd6ZiRRm1cOONGoyTw1q1OOKRk47Noo,964
scipy/ndimage/measurements.py,sha256=TZeNw84wyRAnye-fAADygOQVbS8U-1vqP1grgkFOHbA,1047
scipy/ndimage/morphology.py,sha256=xzmNiSWirVonCOsfXEjvFv8pSL_2tU8iGFH8gLxXpiI,1223
scipy/ndimage/tests/__init__.py,sha256=2Ycbfz-ItQlC3Vc7bSSD3M1uUW1Mf7YmIudErTAfHyE,442
scipy/ndimage/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/ndimage/tests/__pycache__/test_c_api.cpython-38.pyc,,
scipy/ndimage/tests/__pycache__/test_datatypes.cpython-38.pyc,,
scipy/ndimage/tests/__pycache__/test_filters.cpython-38.pyc,,
scipy/ndimage/tests/__pycache__/test_fourier.cpython-38.pyc,,
scipy/ndimage/tests/__pycache__/test_interpolation.cpython-38.pyc,,
scipy/ndimage/tests/__pycache__/test_measurements.cpython-38.pyc,,
scipy/ndimage/tests/__pycache__/test_morphology.cpython-38.pyc,,
scipy/ndimage/tests/__pycache__/test_splines.cpython-38.pyc,,
scipy/ndimage/tests/data/label_inputs.txt,sha256=oBKOjlyOqkDERFefuyjlsPeVfGRyfmDc-uiawuMey4A,315
scipy/ndimage/tests/data/label_results.txt,sha256=hNPE8YOPm5MZ169I4c5JnG2cWxleOlKddG1VdA-Fn3Y,4603
scipy/ndimage/tests/data/label_strels.txt,sha256=fLjLCCb72QdX55kKssZdwg262SaV5NgdIG_Bn_gPHSg,294
scipy/ndimage/tests/dots.png,sha256=sgtW-tx0ccBpTT6BSNniioPXlnusFr-IUglK_qOVBBQ,2114
scipy/ndimage/tests/test_c_api.py,sha256=XrjxHrLOhfwWVHtpl18s4NzZhOYvgsMmpGeySeN4bh8,3570
scipy/ndimage/tests/test_datatypes.py,sha256=V_so7DMOR0qvkIEfhy_c3kcn1VXs8tDdp86PpbRSyQE,2808
scipy/ndimage/tests/test_filters.py,sha256=7eRDc6Teoiv5qRY5WaypfPag8nmmWx1dy0x2QdsdVuM,86080
scipy/ndimage/tests/test_fourier.py,sha256=-0PA7eAZYeI9bAg8n56t3qUET2vkN_Nsy3umgmA4BNI,6819
scipy/ndimage/tests/test_interpolation.py,sha256=DcLAxEn6brE6v593XljYhxwThifsrmQ7Ki-xs8PFf14,56126
scipy/ndimage/tests/test_measurements.py,sha256=j-KVHfS3nOAbSnRZlfIg5lfT0E4LY8Ays2RiCT1Q-gE,49198
scipy/ndimage/tests/test_morphology.py,sha256=kX6wwat_Okohiq6dg1ZUdSVH2K_2J5NAgZWUmxniHvU,107962
scipy/ndimage/tests/test_splines.py,sha256=4VLm6Rdxb2nZTv2xb75cnTLSgKKNsh46qIlOtYsIwqY,2272
scipy/odr/__init__.py,sha256=gHT9GMAwFz2ewCC0B1kTenNLG6g8CHNm__5z6vtF_zc,4456
scipy/odr/__odrpack.cp38-win_amd64.dll.a,sha256=b91prlblc_7CUNkBdEcC8zaJxXsFTcfRPclZ74pyI68,1564
scipy/odr/__odrpack.cp38-win_amd64.pyd,sha256=aoRJPTg6zlwlt_fRWiXVA3MadBeLOvCIul9BhAn_chs,551424
scipy/odr/__pycache__/__init__.cpython-38.pyc,,
scipy/odr/__pycache__/_add_newdocs.cpython-38.pyc,,
scipy/odr/__pycache__/_models.cpython-38.pyc,,
scipy/odr/__pycache__/_odrpack.cpython-38.pyc,,
scipy/odr/__pycache__/models.cpython-38.pyc,,
scipy/odr/__pycache__/odrpack.cpython-38.pyc,,
scipy/odr/_add_newdocs.py,sha256=r5m9cNR3ZxZ9Wxcpff4l4yyhow_iUTxRd4ituHbLAHk,1120
scipy/odr/_models.py,sha256=Mi8SuWoWXIFqGi3BfgMSc_eu1GQfGRtXSvkeI9fblWQ,8115
scipy/odr/_odrpack.py,sha256=WYvfAFaeDJ_IsSMOF0FKrZpiE-h0eB09wh9oJ0uF2rI,43213
scipy/odr/models.py,sha256=rTsyLTTPj-tdnAFa3SaeEslT0bCDbWj1pD_CXEXPwD4,821
scipy/odr/odrpack.py,sha256=YBv1y2mGkw1p5Nl1yCLrdBu6aCAcYSZ2fX_ULJVe0h8,866
scipy/odr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/odr/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/odr/tests/__pycache__/test_odr.cpython-38.pyc,,
scipy/odr/tests/test_odr.py,sha256=nNvgrLS4WWrHvoyGlwOxm1lj_eHJi_Bkko_kHriYlJA,20312
scipy/optimize.pxd,sha256=Go47l_Tk8YvXQxnuJNN6UmDEApEKgBUrplitjO4tsUM,40
scipy/optimize/README,sha256=xkkjdG-1gDRGLV1D5cnLkV8yEyDvO3wiwSS4zHa4mhI,3503
scipy/optimize/__init__.py,sha256=bpomy5LWKr8t0HCX4H4JJW0p32n0F0y5owQGSvu-CaE,13235
scipy/optimize/__nnls.cp38-win_amd64.dll.a,sha256=9T_j1Ho9I0NHUuOM-lkRiPo28LZwlrCRg9EnevkTr1o,1524
scipy/optimize/__nnls.cp38-win_amd64.pyd,sha256=C8Y27IFTqNALpYtjPOQ0Q4bZCSSDPbGTnYH684-wUww,378368
scipy/optimize/__nnls.pyi,sha256=yiK7hRYJrHzfF-tFtPLvB1QE-2_IVy39nqPEcHHBWqw,469
scipy/optimize/__pycache__/__init__.cpython-38.pyc,,
scipy/optimize/__pycache__/_basinhopping.cpython-38.pyc,,
scipy/optimize/__pycache__/_cobyla_py.cpython-38.pyc,,
scipy/optimize/__pycache__/_constraints.cpython-38.pyc,,
scipy/optimize/__pycache__/_differentiable_functions.cpython-38.pyc,,
scipy/optimize/__pycache__/_differentialevolution.cpython-38.pyc,,
scipy/optimize/__pycache__/_direct_py.cpython-38.pyc,,
scipy/optimize/__pycache__/_dual_annealing.cpython-38.pyc,,
scipy/optimize/__pycache__/_hessian_update_strategy.cpython-38.pyc,,
scipy/optimize/__pycache__/_lbfgsb_py.cpython-38.pyc,,
scipy/optimize/__pycache__/_linesearch.cpython-38.pyc,,
scipy/optimize/__pycache__/_linprog.cpython-38.pyc,,
scipy/optimize/__pycache__/_linprog_doc.cpython-38.pyc,,
scipy/optimize/__pycache__/_linprog_highs.cpython-38.pyc,,
scipy/optimize/__pycache__/_linprog_ip.cpython-38.pyc,,
scipy/optimize/__pycache__/_linprog_rs.cpython-38.pyc,,
scipy/optimize/__pycache__/_linprog_simplex.cpython-38.pyc,,
scipy/optimize/__pycache__/_linprog_util.cpython-38.pyc,,
scipy/optimize/__pycache__/_milp.cpython-38.pyc,,
scipy/optimize/__pycache__/_minimize.cpython-38.pyc,,
scipy/optimize/__pycache__/_minpack_py.cpython-38.pyc,,
scipy/optimize/__pycache__/_nnls.cpython-38.pyc,,
scipy/optimize/__pycache__/_nonlin.cpython-38.pyc,,
scipy/optimize/__pycache__/_numdiff.cpython-38.pyc,,
scipy/optimize/__pycache__/_optimize.cpython-38.pyc,,
scipy/optimize/__pycache__/_qap.cpython-38.pyc,,
scipy/optimize/__pycache__/_remove_redundancy.cpython-38.pyc,,
scipy/optimize/__pycache__/_root.cpython-38.pyc,,
scipy/optimize/__pycache__/_root_scalar.cpython-38.pyc,,
scipy/optimize/__pycache__/_shgo.cpython-38.pyc,,
scipy/optimize/__pycache__/_slsqp_py.cpython-38.pyc,,
scipy/optimize/__pycache__/_spectral.cpython-38.pyc,,
scipy/optimize/__pycache__/_tnc.cpython-38.pyc,,
scipy/optimize/__pycache__/_trustregion.cpython-38.pyc,,
scipy/optimize/__pycache__/_trustregion_dogleg.cpython-38.pyc,,
scipy/optimize/__pycache__/_trustregion_exact.cpython-38.pyc,,
scipy/optimize/__pycache__/_trustregion_krylov.cpython-38.pyc,,
scipy/optimize/__pycache__/_trustregion_ncg.cpython-38.pyc,,
scipy/optimize/__pycache__/_tstutils.cpython-38.pyc,,
scipy/optimize/__pycache__/_zeros_py.cpython-38.pyc,,
scipy/optimize/__pycache__/cobyla.cpython-38.pyc,,
scipy/optimize/__pycache__/lbfgsb.cpython-38.pyc,,
scipy/optimize/__pycache__/linesearch.cpython-38.pyc,,
scipy/optimize/__pycache__/minpack.cpython-38.pyc,,
scipy/optimize/__pycache__/minpack2.cpython-38.pyc,,
scipy/optimize/__pycache__/moduleTNC.cpython-38.pyc,,
scipy/optimize/__pycache__/nonlin.cpython-38.pyc,,
scipy/optimize/__pycache__/optimize.cpython-38.pyc,,
scipy/optimize/__pycache__/slsqp.cpython-38.pyc,,
scipy/optimize/__pycache__/tnc.cpython-38.pyc,,
scipy/optimize/__pycache__/zeros.cpython-38.pyc,,
scipy/optimize/_basinhopping.py,sha256=vNyjhYCOfGz_WKoZxrVk3jlWfEW8xqhUH702yX80FV4,30652
scipy/optimize/_bglu_dense.cp38-win_amd64.dll.a,sha256=sjmckNDvIGdCytqLbnbcinFDGahAJL7ns79E37UEcBg,1588
scipy/optimize/_bglu_dense.cp38-win_amd64.pyd,sha256=duJ43B4WZyYWs4owR__UK779DOmwLSKCvO5uFtuAY3A,258560
scipy/optimize/_cobyla.cp38-win_amd64.dll.a,sha256=Ch6DFQAj9YA8c3UIBeXMylWkJy0pbaScomsqccPNpHs,1540
scipy/optimize/_cobyla.cp38-win_amd64.pyd,sha256=rK-RE2pOCMT2bVnVgXxcckPFkVC1C_8U6gX9lD7HPpo,409088
scipy/optimize/_cobyla_py.py,sha256=IPQKuWdl177YFhDJNhdar1FWJ0dlRTaSPgFkbKzJi0E,10477
scipy/optimize/_constraints.py,sha256=M02tYQMeSZ8xuxE_Yie20zzbc1EjNj1rTUJaSHk2ORU,22587
scipy/optimize/_differentiable_functions.py,sha256=g-hDHp6rL3hTIPlcc2vrWOmXchCEoj-XmxybBSnJpZ4,23335
scipy/optimize/_differentialevolution.py,sha256=Tlh7j-KiyJ4pOp1gOxbu4KsNlHgljnEDvB7FUz5LM90,75162
scipy/optimize/_direct.cp38-win_amd64.dll.a,sha256=anI2j_a6uwNh0toUZHk8nZGYuK1VwJDighUQgv_5O1Y,1540
scipy/optimize/_direct.cp38-win_amd64.pyd,sha256=16kfhkQHMgeuYQ9eo5WvYhjxXKjjFfrtiER9p3qB0EY,64512
scipy/optimize/_direct_py.py,sha256=vwWDqQ9yWoyhQX8S0NRVOTzbqzoazZjh8Ed404CI4Rs,12139
scipy/optimize/_dual_annealing.py,sha256=ssjpyqYXrXrbu9UriCI0dN36fU7wgVFV_QAdSYZwgqA,30860
scipy/optimize/_group_columns.cp38-win_amd64.dll.a,sha256=Hjum706c26JZpT_j7wrxnSEFwh97FqiaOJMpf19T9Ew,1620
scipy/optimize/_group_columns.cp38-win_amd64.pyd,sha256=Jeve-szkAhfTwcr-7PWSRylnb1acOfASZsYWDKL_qVM,1029632
scipy/optimize/_hessian_update_strategy.py,sha256=2ZwhDFIwD83WgaLW6CaDfCu3F5r4WvkG5SqsY2QrK8o,16259
scipy/optimize/_highs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_highs/__pycache__/__init__.cpython-38.pyc,,
scipy/optimize/_highs/_highs_constants.cp38-win_amd64.dll.a,sha256=smqGEO6bbJCvcJPmZdFn0KS3D1wdnK6HnD2GnWbr9Jw,1644
scipy/optimize/_highs/_highs_constants.cp38-win_amd64.pyd,sha256=fnEHEUxa49NWSl-fSBBBcArk52zx1Tm5Lmof_XORTuk,946176
scipy/optimize/_highs/_highs_wrapper.cp38-win_amd64.dll.a,sha256=LEOh-V4cJ8XkdBEnizww5vH5qzTBm57GghGz57PmXI4,1620
scipy/optimize/_highs/_highs_wrapper.cp38-win_amd64.pyd,sha256=QLkMnNFm0kqHdRSBtD9kRntu_4l55JwmFPJZlndD6Uc,4118528
scipy/optimize/_highs/src/cython/HConst.pxd,sha256=cHJGLWN3PFKwA4TPcf_Ysy33-q50GE7x7E4cm5WPaj0,5644
scipy/optimize/_highs/src/cython/Highs.pxd,sha256=iESi_17ZTmA94oRDsQPLN02wWwSvHy3nhLIi_we3qTs,2178
scipy/optimize/_highs/src/cython/HighsIO.pxd,sha256=yfm3BoLQBanrW6yqNqX1jtbTlqM_z4GVm9FgR3HqR78,752
scipy/optimize/_highs/src/cython/HighsInfo.pxd,sha256=kX_fiVNr5R-PNRNa3DEWdW_TSBemRaN9P-3n4TkMV04,784
scipy/optimize/_highs/src/cython/HighsLp.pxd,sha256=72mPP_GTwA1u9IIAJYVbm5p6-llcV2VAkm6mmcAAeJo,1179
scipy/optimize/_highs/src/cython/HighsLpUtils.pxd,sha256=9_ZVs291VCA792pOg4wlufiThs5Ko5tpMi4wKTERy8w,325
scipy/optimize/_highs/src/cython/HighsModelUtils.pxd,sha256=Zc1Kw5QACiT7F_NRdf65jjgm4BZDU_QPnjJd7785aO0,372
scipy/optimize/_highs/src/cython/HighsOptions.pxd,sha256=pWrLtCo_XElAPNgRy_rORfulWUdLKRX0_QrobBQlFfk,3297
scipy/optimize/_highs/src/cython/HighsRuntimeOptions.pxd,sha256=jQGo5ge0zLpsTOwHUSD4U-AXaSp6v3qRoWBAwdhq558,297
scipy/optimize/_highs/src/cython/HighsStatus.pxd,sha256=mmeURT3hjZvNPqMS5scMlvoz0OolBOh7TIaIOsb1Hu8,378
scipy/optimize/_highs/src/cython/SimplexConst.pxd,sha256=8UBis1MSfyd6yABST2hcm-j3A2MDPvWMS80r_8W5DR8,5140
scipy/optimize/_highs/src/cython/highs_c_api.pxd,sha256=UqxNL_2JCWCmwNJ9W22NMrpzl6fU6GPfaUxdWzpV6CA,366
scipy/optimize/_lbfgsb.cp38-win_amd64.dll.a,sha256=xznW-J6nde9ZOTbft39tyfInG6fa-c077H9nkvJJI_A,1540
scipy/optimize/_lbfgsb.cp38-win_amd64.pyd,sha256=W7NYCibw9TTl5Q3BX_FU_X5npXE3BvBeqwolTYiNe30,431104
scipy/optimize/_lbfgsb_py.py,sha256=oINP1dQntE_6JmP46RuudSmtxwlLvBoZJJKVvc1yWJI,19137
scipy/optimize/_linesearch.py,sha256=c4Gm4bEqB3syhoGprRMykVq3aoa6wNa4HceEU42Xj34,27463
scipy/optimize/_linprog.py,sha256=ZpynGWjJx6KbsDC9b06wvbf63rHzM1UAogtUJAhTmKM,30151
scipy/optimize/_linprog_doc.py,sha256=YLY2LeopkSlLm47eJWAL7BxidS0afQoDquHBK5cHcXA,63402
scipy/optimize/_linprog_highs.py,sha256=KEMe2dqfHx92RRwUnS-5SzO_H4MaOgSfBJsmuGHZQ7M,17904
scipy/optimize/_linprog_ip.py,sha256=gq_cgybwmp-stLg_Syxbf5JQvfkGH6o6yzEOmUcxhmo,47041
scipy/optimize/_linprog_rs.py,sha256=i98FNQoswOPg7iVvfQBGtg206CPPeqbBlgcHb6AC3tI,23721
scipy/optimize/_linprog_simplex.py,sha256=24fziumCHXUjm1V64bbN-ke5h8pKQtNWx2mMdok5kdw,25388
scipy/optimize/_linprog_util.py,sha256=idWERTd0Oimo9_FI9KC8MCwuNqJLIEwAkYJ-SbyF65A,64043
scipy/optimize/_lsap.cp38-win_amd64.dll.a,sha256=C9Lics8mIU4DNJbBjM82THiSGI3IXBvM5uZRPtx1x7c,1516
scipy/optimize/_lsap.cp38-win_amd64.pyd,sha256=Ram1b1L8wgOwbnXS0L6B5BuRm_GCPVb3gpDaUNSmwHA,177152
scipy/optimize/_lsq/__init__.py,sha256=c4V-tnMyi2dXNNXkjFQ8SdvGANtgbrqXEM-OpdBdZpQ,177
scipy/optimize/_lsq/__pycache__/__init__.cpython-38.pyc,,
scipy/optimize/_lsq/__pycache__/bvls.cpython-38.pyc,,
scipy/optimize/_lsq/__pycache__/common.cpython-38.pyc,,
scipy/optimize/_lsq/__pycache__/dogbox.cpython-38.pyc,,
scipy/optimize/_lsq/__pycache__/least_squares.cpython-38.pyc,,
scipy/optimize/_lsq/__pycache__/lsq_linear.cpython-38.pyc,,
scipy/optimize/_lsq/__pycache__/trf.cpython-38.pyc,,
scipy/optimize/_lsq/__pycache__/trf_linear.cpython-38.pyc,,
scipy/optimize/_lsq/bvls.py,sha256=h9bYh3astIRvMtMGghtEGpG5fDsh74SMRbmgF3HN8YM,5378
scipy/optimize/_lsq/common.py,sha256=RpaKaEwRTFZ4fmQqi0fKwXEs8HUAuTWH3qFe_nQ9tYU,21340
scipy/optimize/_lsq/dogbox.py,sha256=KuyTxgJPRumBkrk8KlgZZVhm8tpdmsGNBtPAsj2U1Xs,12013
scipy/optimize/_lsq/givens_elimination.cp38-win_amd64.dll.a,sha256=sG907Jjc7OBOyQuSPMzfUIXWlLM4CLN3ZwJDQm_aH2Y,1668
scipy/optimize/_lsq/givens_elimination.cp38-win_amd64.pyd,sha256=nhWOC7K8NvsPLN3SULUA_fDzhkpZ3rUUmppSZLXePdg,153600
scipy/optimize/_lsq/least_squares.py,sha256=AT0W4Hr26x_70CUv6QQv5lUhddTX73UPN6HRY9Artoc,40494
scipy/optimize/_lsq/lsq_linear.py,sha256=TF0jNgUwHxgAvMIVEYk-CE2cVwyBDF2OKCUjDRiI5c8,15198
scipy/optimize/_lsq/trf.py,sha256=Eav2HZf0n7W7PMIPlAr1829mb9mXrKHlCBjrDMdPuyk,20039
scipy/optimize/_lsq/trf_linear.py,sha256=GEMGyMqWwaVLUa7sCo9iwDmUkuT746oci1iK6KjnJQA,7891
scipy/optimize/_milp.py,sha256=FM_hdxqZgDFxRTyCb9z3KFmR4Qxnxr3FZOvxh362l_E,15310
scipy/optimize/_minimize.py,sha256=xmRmTv4J--ciyV2keBlXv45v47dCfXY-hRdWk788Sro,46998
scipy/optimize/_minpack.cp38-win_amd64.dll.a,sha256=fVknZFAoedVleb4MlKkxYcHDiDyuIH3iZGzfgriLVs8,1548
scipy/optimize/_minpack.cp38-win_amd64.pyd,sha256=6-XSiJpdGY5KIPzd5rrGc3xJL_e78yC9Lhn9zom133c,97280
scipy/optimize/_minpack2.cp38-win_amd64.dll.a,sha256=fVF7oEyv61ozz5mrjARsCsJ_TYG6uYgHGVlJcutevWU,1564
scipy/optimize/_minpack2.cp38-win_amd64.pyd,sha256=3yA9402IUl3w2qgwvuti3p-il97J7yEutgKIR-XO1Vo,70144
scipy/optimize/_minpack_py.py,sha256=hqBaG63mbsOlSJMCmpB6GLqDjkHNLPZSManHSbK3f3s,39356
scipy/optimize/_moduleTNC.cp38-win_amd64.dll.a,sha256=e6AA39QBK4OT0KIdN-p0faCz3Ovwa2hqcYHpAHkZwvc,1572
scipy/optimize/_moduleTNC.cp38-win_amd64.pyd,sha256=B2dEvOusYn82lUxSBoFz73mn8PB92-J1OISl-xvX4zk,129024
scipy/optimize/_nnls.py,sha256=5sxY9YRmbwoAl-wUeK-pVKdIp1BBU3FMCr447gkWJps,2385
scipy/optimize/_nonlin.py,sha256=yextFwExWUmy2ZWV4oVLsTLCHQyyqqfgKp4IOFovpoA,50596
scipy/optimize/_numdiff.py,sha256=B11y5uTu0RIWcUP97NGnj4RazEr-sfzC3QuQZ370DVo,29042
scipy/optimize/_optimize.py,sha256=9Lb1PtYFRTCLo6u2oe6LzbSSjhoKzP1xHtrq262KtSc,143677
scipy/optimize/_qap.py,sha256=lGe7ogeqUVEs-dUTAr2tumGUCjiNOJFcCQNch1k6OGA,28382
scipy/optimize/_remove_redundancy.py,sha256=kIpdqqpum9ZGyBNbf6cHhNRvxaYmSJI-RvD_qGA8AKw,19289
scipy/optimize/_root.py,sha256=R9cz8UJC7ak8GLLj7HRw9PYkErp4bVMPSKZR7HhBo_I,28993
scipy/optimize/_root_scalar.py,sha256=xeA-8gtG27mUhHMsi4m32fveCrJKXtgFwJWvpflAASE,18967
scipy/optimize/_shgo.py,sha256=khlZ-ds8auI0qH2AlwhOTj3ZpqDAQajY5pTs0-BjJcg,62491
scipy/optimize/_shgo_lib/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_shgo_lib/__pycache__/__init__.cpython-38.pyc,,
scipy/optimize/_shgo_lib/__pycache__/triangulation.cpython-38.pyc,,
scipy/optimize/_shgo_lib/triangulation.py,sha256=KZnD7g088IpKK_uOA9VC1KIKRHlWHsNBSWqNMD5RbuM,22100
scipy/optimize/_slsqp.cp38-win_amd64.dll.a,sha256=ECUeQtyAMYrD8OO99Ihn1XkIiBzUOSgBzYtiQ50ksAk,1524
scipy/optimize/_slsqp.cp38-win_amd64.pyd,sha256=_Ulg3hp2Sgz8T1J_r2Hs5320GfmMBu66YjBKdVLxkxY,96768
scipy/optimize/_slsqp_py.py,sha256=MYjj-U2eeqLw1WPHPbp37veTCs-VtsZAh1kzpTFJbEE,19271
scipy/optimize/_spectral.py,sha256=jJFx0UcNjpH2e3KybxP2lzM75h7NTrzVnxyGWeGAF3g,8180
scipy/optimize/_tnc.py,sha256=6WRG-Gp4qsqXT8MMdhtJYtNpoHmb9VrL9YBD3N4Y-ew,17745
scipy/optimize/_trlib/__init__.py,sha256=5TbV8UJSSEwW_qONgzozoHDgvhhugzqykJzybDCrNj0,536
scipy/optimize/_trlib/__pycache__/__init__.cpython-38.pyc,,
scipy/optimize/_trlib/_trlib.cp38-win_amd64.dll.a,sha256=8VVXlSNpj4kjEzNqM29mtvfQpa357XQn9rQOsP78ze8,1524
scipy/optimize/_trlib/_trlib.cp38-win_amd64.pyd,sha256=4kfdxTM34tFU_wO8ofNGPk0wn_ATjP8WY9rLPmLto5c,309760
scipy/optimize/_trustregion.py,sha256=aTbOIhXBveJUFa-0ovhZo9gvdv3YnuwTAV2IWbxviLM,10952
scipy/optimize/_trustregion_constr/__init__.py,sha256=Y2OLn2HBQ5rxpAfI-UjWZc4j_sDqjOBGmgdS2zAC_24,186
scipy/optimize/_trustregion_constr/__pycache__/__init__.cpython-38.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/canonical_constraint.cpython-38.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/equality_constrained_sqp.cpython-38.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/minimize_trustregion_constr.cpython-38.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/projections.cpython-38.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/qp_subproblem.cpython-38.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/report.cpython-38.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/tr_interior_point.cpython-38.pyc,,
scipy/optimize/_trustregion_constr/canonical_constraint.py,sha256=pYMm2qLrCrvpD7pQZAZpz1H7Zfag3uiwezGBCDx6syk,12928
scipy/optimize/_trustregion_constr/equality_constrained_sqp.py,sha256=uuZ-Jtgl6G6sew7J2aeA4pERgAQ6tPtvvebGLBdNFUQ,8809
scipy/optimize/_trustregion_constr/minimize_trustregion_constr.py,sha256=Zu1YaXF69KXMCaUa5a1q9ugsmg_JoawnO0QSS31LpJs,25435
scipy/optimize/_trustregion_constr/projections.py,sha256=YOPzrg8CMqhn1cXq-ykqqlYSC1OYkwyB-YTCGhVsaoo,13510
scipy/optimize/_trustregion_constr/qp_subproblem.py,sha256=kzhhoadtUgeFmSoBKerKMnSgMSSpzHc_m43hkgqjTlg,23229
scipy/optimize/_trustregion_constr/report.py,sha256=HguMcfjFSybgSIPyA5_vBNc271ZR9-22OiPjhg6B8Dw,1910
scipy/optimize/_trustregion_constr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_trustregion_constr/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_canonical_constraint.cpython-38.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_projections.cpython-38.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_qp_subproblem.cpython-38.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_report.cpython-38.pyc,,
scipy/optimize/_trustregion_constr/tests/test_canonical_constraint.py,sha256=z8MKN6NbaxsH7pj2rbCGdOQ4jXE-eM-SPu_IcNMtIaI,10165
scipy/optimize/_trustregion_constr/tests/test_projections.py,sha256=iDpvZi2SiwZwuyWE4wgRDzYlyuQKpGiAkxGRXX9a6Ts,9034
scipy/optimize/_trustregion_constr/tests/test_qp_subproblem.py,sha256=a9VYYB-PIUgNZht7COhFkVseeSMo3rdK8jO2QI4VTYg,28364
scipy/optimize/_trustregion_constr/tests/test_report.py,sha256=xeCq--GOFVM_FFu9CkRXQsKGkVmIkgMbUU0bK8Fw8_Q,1120
scipy/optimize/_trustregion_constr/tr_interior_point.py,sha256=2EAitOYVUPycT9CfGSrugYh0zOz0rhr9cV1kwKM6C2U,14148
scipy/optimize/_trustregion_dogleg.py,sha256=XTvNQtebZTPzl6xGtzHBLDcFD2qe5sSOFOTbmtDWswo,4511
scipy/optimize/_trustregion_exact.py,sha256=HKkPTZ_7D2YAR6Huz7TPWnxjfYOyKgS_QMJDLVRKJWE,15837
scipy/optimize/_trustregion_krylov.py,sha256=OE6ABN1_oeRUUBXWGBA2QFlzxO4GJVBseoNOMB2b4Sw,3095
scipy/optimize/_trustregion_ncg.py,sha256=O2_8p92l_faUHLI26GHK2wV0RTdAR1scli0XhXTTsSo,4706
scipy/optimize/_tstutils.py,sha256=R85c3mMQd_3ZWK3-0E1hdp817PlqdKZ5LN34_eU9o0Q,30151
scipy/optimize/_zeros.cp38-win_amd64.dll.a,sha256=GXcfEoZxn4dt099h_i_mKsDHMFBfgFOo5qJN4Eex1UA,1524
scipy/optimize/_zeros.cp38-win_amd64.pyd,sha256=unQ-Zrp4DNfNLqECd1VaNPvJt6vdIYxlNPQVNTdcitk,23552
scipy/optimize/_zeros_py.py,sha256=cXo0bid6b0MzrFdinA7I5CVA2he9AeQWzLPzqEfl-pk,52605
scipy/optimize/cobyla.py,sha256=-MoSAgYJMPGorcRS231A5-rYFHkkwo_1A4jyY4xz2-o,871
scipy/optimize/cython_optimize.pxd,sha256=RBaGDYU1CbbEqMMhX4RZd0Pzt4a3E9ap1lgYk1VxM-w,439
scipy/optimize/cython_optimize/__init__.py,sha256=thKe604HuVDgMGl7yNAHymyjECDHsy8gwyR_ZOJ_fFI,4963
scipy/optimize/cython_optimize/__pycache__/__init__.cpython-38.pyc,,
scipy/optimize/cython_optimize/_zeros.cp38-win_amd64.dll.a,sha256=RMvpWt9jQRNvknBKpJwTpdno5ZQisHy6M-g39hVVN44,1524
scipy/optimize/cython_optimize/_zeros.cp38-win_amd64.pyd,sha256=Pk23MNWSsqkFkAOeIxcp8byEAqGHkfHwItm-8ZDWTnI,75264
scipy/optimize/cython_optimize/_zeros.pxd,sha256=zRbpv0__RdBCE6Qp_QMkxr-vT8QowRC2ndgDoRX8EGE,1227
scipy/optimize/cython_optimize/c_zeros.pxd,sha256=bNywaoCR0gGHwSsuxjc7phFPaFXbUVaUOu2oR5x0KuE,1135
scipy/optimize/lbfgsb.py,sha256=wcWdyuACE7bFtRkS_Rc7c0q3uRwjzPmoxtYYK_LKlhs,966
scipy/optimize/linesearch.py,sha256=ddHjwUqHFsEH0_NSCni0HphW7UtPrp2TXccmH-cPP2c,1045
scipy/optimize/minpack.py,sha256=Jl-uGpUjoaod9D4pLNZEBSl0aCuDUaKmppD3C64VD70,1337
scipy/optimize/minpack2.py,sha256=n-m19LmjnYy2UNj3EvwVYKJUdtPAZbJh22-2DwybjhQ,798
scipy/optimize/moduleTNC.py,sha256=W1Htn_FZFIGOV8B6kczl0Q6OQmq8Tc8u6hbENArOrZA,774
scipy/optimize/nonlin.py,sha256=xlJBsGhM57y9ItIueD9_FvCkDo1oShOez2MCBFGtDDw,1483
scipy/optimize/optimize.py,sha256=E_EEWvY_rFA09x-q1p1k0pi4RHUYML-FvKyNRoEyAJc,1596
scipy/optimize/slsqp.py,sha256=6ttTaN06slpaI7fA8rBKeLBf3dtMLoZTJkdYBvao4kA,1090
scipy/optimize/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test__basinhopping.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test__differential_evolution.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test__dual_annealing.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test__linprog_clean_inputs.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test__numdiff.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test__remove_redundancy.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test__root.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test__shgo.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test__spectral.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_cobyla.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_constraint_conversion.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_constraints.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_cython_optimize.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_differentiable_functions.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_direct.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_hessian_update_strategy.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_hessinv.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_setulb.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_least_squares.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_linear_assignment.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_linesearch.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_linprog.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_common.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_linear.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_milp.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_minimize_constrained.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_minpack.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_nnls.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_nonlin.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_optimize.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_quadratic_assignment.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_regression.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_slsqp.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_tnc.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_exact.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_krylov.cpython-38.pyc,,
scipy/optimize/tests/__pycache__/test_zeros.cpython-38.pyc,,
scipy/optimize/tests/test__basinhopping.py,sha256=fPvvG17I0DbR93AC8pAjhp_U-cu0gTJLOCD7BcS-rRE,17188
scipy/optimize/tests/test__differential_evolution.py,sha256=CWNztS2dmP05Jjh9jAgHw5g0hA8Kn5_NJdz7-N_wNtM,62898
scipy/optimize/tests/test__dual_annealing.py,sha256=7Ck-b-155kenDr95K26nw3bzRxH7dmmKrNw4UyZd8ko,14675
scipy/optimize/tests/test__linprog_clean_inputs.py,sha256=x9qv4IWNHWMX94l3Gf4FgGSadHakt0cJfa5qUYHCsmQ,11403
scipy/optimize/tests/test__numdiff.py,sha256=2tCIiToIrxAg4uApfbJwWqwMS3yTmrSz690rq114JA8,32151
scipy/optimize/tests/test__remove_redundancy.py,sha256=SjHX9-Q8wK0hEO8Yh4_I_cGVTV0PpR1f1ynm0Ft8L8k,7788
scipy/optimize/tests/test__root.py,sha256=fFUr3BL-MmlNUHUt_eNMHQoLwH9akXTzL2_6PyPKU1s,2698
scipy/optimize/tests/test__shgo.py,sha256=2urFTBWTF5N0dWpubIx19twskREMJQ01TUzvFXxbhwA,29846
scipy/optimize/tests/test__spectral.py,sha256=UoMNbr_AOw5btdZAvLKLzU_PGkHkQutu-jMkL-ihEYo,6727
scipy/optimize/tests/test_cobyla.py,sha256=lGLHdkAejWPzr598_vXBv6ILNHTb4dc0rfMXAaItXeM,4310
scipy/optimize/tests/test_constraint_conversion.py,sha256=OFsbFhU62aHbDtPzE37b37yRHzFZ7OpAW1xPFgHRHbA,12013
scipy/optimize/tests/test_constraints.py,sha256=0JmEmz3sitdN0UawqV5Au5kBRoKsRdISjfKl9LaUuFI,8624
scipy/optimize/tests/test_cython_optimize.py,sha256=XmUrnJrxWKWfH5337FiCAfCV6djffjpQWVg1olG1vg0,2730
scipy/optimize/tests/test_differentiable_functions.py,sha256=LNNcUE6Zz0FNlprx9U8AA1xuJXM666cOsi3N6jtHtHk,26885
scipy/optimize/tests/test_direct.py,sha256=_JopiGDv5bJeZuALeYyE5a9umkHw21Z-r7yfWbFtdtQ,13470
scipy/optimize/tests/test_hessian_update_strategy.py,sha256=sapTTn1g1XVzth5l0b_jOp7y_nbRbD2vnr2rNNYwfBg,10320
scipy/optimize/tests/test_lbfgsb_hessinv.py,sha256=8_Bvj0kpB1mvK3fmh3v_vsYCrH8iFTnmaVjqe1EnSGA,1180
scipy/optimize/tests/test_lbfgsb_setulb.py,sha256=IJaE_d8DrJnReGmMkajhY5HBHGnLz6nrGIlbOKZuYbI,3288
scipy/optimize/tests/test_least_squares.py,sha256=OkRvRwdgmM5vL9e7kbC1b_czQH9tb7D-goGINwilgTc,32584
scipy/optimize/tests/test_linear_assignment.py,sha256=51pamAv4Kf0zrqIUkxk-8iFVF4-iZS6F8CxVH1h0J1A,4201
scipy/optimize/tests/test_linesearch.py,sha256=1MoqWSGVNedtkIroHZ2TNmrQXnDcyHTlMVpZRWCMFjo,11103
scipy/optimize/tests/test_linprog.py,sha256=ggepgEAjGrZ8AQ-2RMYmYandYlyVewbRdxtP-4gF5LQ,98003
scipy/optimize/tests/test_lsq_common.py,sha256=AXdfzIlCO5gQa3gy7Ou9mp1SAHD8-qMP1yCyfK1kaI4,9797
scipy/optimize/tests/test_lsq_linear.py,sha256=N_BonD8sFCspLPrhHMXmTNA7N8fSSk6fJ4tBc8tK_rU,10611
scipy/optimize/tests/test_milp.py,sha256=Esd-gfSFRzT4TNvS-L3FA_I9iQg2ljxRHEQqHwSUm1w,14236
scipy/optimize/tests/test_minimize_constrained.py,sha256=l-NWPl7frChHOFxPPKLXSzrorEtrTCpiJ_ainWAlVbQ,26401
scipy/optimize/tests/test_minpack.py,sha256=YQLW_U2cVjd6CEiJyEMe-lr5pW44H3skTGgDj3ntPdU,37471
scipy/optimize/tests/test_nnls.py,sha256=mCllSFFe9J2NtiGs6xxa49tjPmvP0tRoT0BKVgEIxaw,948
scipy/optimize/tests/test_nonlin.py,sha256=a6-40CY7TM9DwveBN_EP08s31hcObyEe1T_IzqajoSI,17439
scipy/optimize/tests/test_optimize.py,sha256=qnV11vSiZrFaQUgRIer0KEcCu38F3ZHFxGnrkypTeq8,114035
scipy/optimize/tests/test_quadratic_assignment.py,sha256=jdtSWYpLQ79EXoGBR_IrxtUX7VPWQH1QOFyivhrnufU,16740
scipy/optimize/tests/test_regression.py,sha256=jc-uV89QavAhf34j8J2riNxYY9gIIgOddrRMV5ltC0Y,1117
scipy/optimize/tests/test_slsqp.py,sha256=T08XJNru8vxNk58-wNujpe7L82wMoe49X9pDloAU8YE,23798
scipy/optimize/tests/test_tnc.py,sha256=i5Z7VISOykjYfDmw6vibaWGUzPtWyG-4YhP2ykjuO4s,13523
scipy/optimize/tests/test_trustregion.py,sha256=S57y-AFuek-24XWgUozVMLaoTGySNufU84-nZ8bo-6o,4813
scipy/optimize/tests/test_trustregion_exact.py,sha256=qaFjV_iWisvbDPrfVEl_SXqVLuSbTiqb1P4_JVRmQXo,13306
scipy/optimize/tests/test_trustregion_krylov.py,sha256=0FXn35I_-hBQS8rKb1BhujlfyAEJ1VDEofTXJ9X_UWQ,6757
scipy/optimize/tests/test_zeros.py,sha256=M2F6WUFlMFznob8H29ksuaVUFMI4198mq3wfgxX1Ah0,29209
scipy/optimize/tnc.py,sha256=HLK6bVEx_LlAeM2PUoDo1TRmEqWC0QHMtqrdhaAGImo,1201
scipy/optimize/zeros.py,sha256=PGXty8DoZhzj6HK4T1KEyczVx_2GRVq8xxNckihMiNA,1052
scipy/signal/__init__.py,sha256=ebttLAkhpOK6-9gNHsyAD7ESBAVrfCKqIclIRlMQl1I,15896
scipy/signal/__pycache__/__init__.cpython-38.pyc,,
scipy/signal/__pycache__/_arraytools.cpython-38.pyc,,
scipy/signal/__pycache__/_bsplines.cpython-38.pyc,,
scipy/signal/__pycache__/_czt.cpython-38.pyc,,
scipy/signal/__pycache__/_filter_design.cpython-38.pyc,,
scipy/signal/__pycache__/_fir_filter_design.cpython-38.pyc,,
scipy/signal/__pycache__/_lti_conversion.cpython-38.pyc,,
scipy/signal/__pycache__/_ltisys.cpython-38.pyc,,
scipy/signal/__pycache__/_max_len_seq.cpython-38.pyc,,
scipy/signal/__pycache__/_peak_finding.cpython-38.pyc,,
scipy/signal/__pycache__/_savitzky_golay.cpython-38.pyc,,
scipy/signal/__pycache__/_signaltools.cpython-38.pyc,,
scipy/signal/__pycache__/_spectral.cpython-38.pyc,,
scipy/signal/__pycache__/_spectral_py.cpython-38.pyc,,
scipy/signal/__pycache__/_upfirdn.cpython-38.pyc,,
scipy/signal/__pycache__/_waveforms.cpython-38.pyc,,
scipy/signal/__pycache__/_wavelets.cpython-38.pyc,,
scipy/signal/__pycache__/bsplines.cpython-38.pyc,,
scipy/signal/__pycache__/filter_design.cpython-38.pyc,,
scipy/signal/__pycache__/fir_filter_design.cpython-38.pyc,,
scipy/signal/__pycache__/lti_conversion.cpython-38.pyc,,
scipy/signal/__pycache__/ltisys.cpython-38.pyc,,
scipy/signal/__pycache__/signaltools.cpython-38.pyc,,
scipy/signal/__pycache__/spectral.cpython-38.pyc,,
scipy/signal/__pycache__/spline.cpython-38.pyc,,
scipy/signal/__pycache__/waveforms.cpython-38.pyc,,
scipy/signal/__pycache__/wavelets.cpython-38.pyc,,
scipy/signal/_arraytools.py,sha256=Gys8GyLUFE_oDeczc1BWjY59i4mrLzcveciGb1WD49Y,7730
scipy/signal/_bsplines.py,sha256=BlkW9L082-NXaolpCzGxj_quSJws2O6rxy8sdTSu19c,20436
scipy/signal/_czt.py,sha256=dhVmn1ScMOn8rzcvaou8n5s4ik8mlPH7BPsViaSA_so,20020
scipy/signal/_filter_design.py,sha256=dQ6a-8v8ojeQoFqOR50tp_c4hRaxLUj72G2x7UrQjXY,190799
scipy/signal/_fir_filter_design.py,sha256=4gZ0tvKkYmOp-wSk0uVYpFT5QF0h1UzfoVGHsFWqFcg,50373
scipy/signal/_lti_conversion.py,sha256=arhvsBjgFkIqOFUtNnBri0Po1CdXVjo9Pj6JlajTJZU,16663
scipy/signal/_ltisys.py,sha256=_F29cPVMdLOAymRUQu7bNt3ow4pXdcR2gb8f1nONY20,132900
scipy/signal/_max_len_seq.py,sha256=HgaUzZZEOTy89zh8K5rEf7jDFALxRDEArsIUn1woWFM,5201
scipy/signal/_max_len_seq_inner.cp38-win_amd64.dll.a,sha256=cdSBlGzGxeM7D2YoY1_weMmiTzTRowhf0mUza_C2fFw,1668
scipy/signal/_max_len_seq_inner.cp38-win_amd64.pyd,sha256=mOZUSYOh0svgCsPt___y7m9yIP-1jDGiOO2Bj_YuMTM,1007104
scipy/signal/_peak_finding.py,sha256=YGOqY1MJTYuj8k_G9ZjMaIAprGOsx3cpqtUKn4SlDVs,50118
scipy/signal/_peak_finding_utils.cp38-win_amd64.dll.a,sha256=hgvtnt47vzOrbBqU8bDyZCdx69foK0Mtffzc5HTCFMQ,1684
scipy/signal/_peak_finding_utils.cp38-win_amd64.pyd,sha256=aJ-OVkP54NdLLg6vG-rypPu0Yt70qWG6cPfQYFPLRpI,209920
scipy/signal/_savitzky_golay.py,sha256=TA5zbmZeExTMJS2ORPe4BJ23PfS7NHKkoOTjRj552z8,13774
scipy/signal/_signaltools.py,sha256=wRKoliNoVwlposxq7Ah32ujw2lSuVVTXdt27_tgWZaQ,160251
scipy/signal/_sigtools.cp38-win_amd64.dll.a,sha256=TgEM75mVzGvZrKcVrud3H7wswyrOBjfj9IHQ45dKenI,1564
scipy/signal/_sigtools.cp38-win_amd64.pyd,sha256=gsMvFqHZ_22N8qYZ9z12_j9yBGHfr4pCnT355-_gxrg,100352
scipy/signal/_sosfilt.cp38-win_amd64.dll.a,sha256=zL6AsMOqg92PlZVwIW_g9AshlOSxGLj9TD1GIrcmgpM,1548
scipy/signal/_sosfilt.cp38-win_amd64.pyd,sha256=GZMfZ66yy1-51CxFpoSBBSJw2fx1DHGoJ60h4kQso_U,220672
scipy/signal/_spectral.cp38-win_amd64.dll.a,sha256=cFahdzbW7AIiVUBpM1ni_oZlWS5Jv5I0JRPbanJRxVU,1564
scipy/signal/_spectral.cp38-win_amd64.pyd,sha256=cbHFWUnHobvE6jPNlD6jCWkT95o0rqusNPxt675DGAA,1011200
scipy/signal/_spectral.py,sha256=b6WMIuLcBE4WuMBvQj8avTGmPX4wkW11mpoMmpD6tKE,2023
scipy/signal/_spectral_py.py,sha256=BerG-kKr0a8X6OxaAFa2Ts4pId-vcEHdf4-sD5Jl1kc,78629
scipy/signal/_spline.cp38-win_amd64.dll.a,sha256=BFe-_3oFisH7hNnnFGQCovp1A_HE_Nn_sN8bhyGKlm8,1540
scipy/signal/_spline.cp38-win_amd64.pyd,sha256=G8QUg87Bz9GBdqGTalOm6IGDrV0S22bzFCDZ9A_j1wo,60416
scipy/signal/_upfirdn.py,sha256=OeP4CSPwZ0QD2Bu9UIqQzPfmWLQuW56wQQfeKrax2X4,8100
scipy/signal/_upfirdn_apply.cp38-win_amd64.dll.a,sha256=PvaKsmRT75PUNGdLzRTzZwTcoB-sHYwPIJSIV85mWJ0,1620
scipy/signal/_upfirdn_apply.cp38-win_amd64.pyd,sha256=2LiX4WYS2cVEEUPVyKXwx-u80S1ITvOExqMKpEj86OQ,281088
scipy/signal/_waveforms.py,sha256=SjUjL0zu-cOy1V-xsWxzz3tkwT7ycG4LXlJdQbiuKiY,21195
scipy/signal/_wavelets.py,sha256=4pX0I9h4Fx8dyRStjmM5ETBDxaijbRiydXIzDCm3whA,14539
scipy/signal/bsplines.py,sha256=If7aqljUcfb_KMkNmcPP_Z43wFlYjF1VZEtcxHBtrto,1117
scipy/signal/filter_design.py,sha256=4Ma-9mrsHtdVjkqCOhPkPs4qle4ZPAEfVxup-r4hfq4,1761
scipy/signal/fir_filter_design.py,sha256=uyer_Ekos6bsjQXBv_uofH_l4iBNmk3r_TP-F2ExAf8,1036
scipy/signal/lti_conversion.py,sha256=VLeuuxDFDXAni73MjwG0lsDPnAVnB9yA2IOSOZhtQCU,966
scipy/signal/ltisys.py,sha256=o3lWSZRHAEqDxQA3Uo8fFqZ_yHCgDepdzwj9kbF6WP8,1508
scipy/signal/signaltools.py,sha256=H6fVq2a-lEG1xJGhI2la1aU0Ve3C76wdkfTLRce04pU,1438
scipy/signal/spectral.py,sha256=sIEVoqzANYHLVo3sLDWdyR9kCaZ9YRxocXsZXBAMelM,976
scipy/signal/spline.py,sha256=4xylmC4JyNUioC_v_jsn4fdi2aFMVOy23Id14X8Q9wM,836
scipy/signal/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/signal/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/signal/tests/__pycache__/mpsig.cpython-38.pyc,,
scipy/signal/tests/__pycache__/test_array_tools.cpython-38.pyc,,
scipy/signal/tests/__pycache__/test_bsplines.cpython-38.pyc,,
scipy/signal/tests/__pycache__/test_cont2discrete.cpython-38.pyc,,
scipy/signal/tests/__pycache__/test_czt.cpython-38.pyc,,
scipy/signal/tests/__pycache__/test_dltisys.cpython-38.pyc,,
scipy/signal/tests/__pycache__/test_filter_design.cpython-38.pyc,,
scipy/signal/tests/__pycache__/test_fir_filter_design.cpython-38.pyc,,
scipy/signal/tests/__pycache__/test_ltisys.cpython-38.pyc,,
scipy/signal/tests/__pycache__/test_max_len_seq.cpython-38.pyc,,
scipy/signal/tests/__pycache__/test_peak_finding.cpython-38.pyc,,
scipy/signal/tests/__pycache__/test_result_type.cpython-38.pyc,,
scipy/signal/tests/__pycache__/test_savitzky_golay.cpython-38.pyc,,
scipy/signal/tests/__pycache__/test_signaltools.cpython-38.pyc,,
scipy/signal/tests/__pycache__/test_spectral.cpython-38.pyc,,
scipy/signal/tests/__pycache__/test_upfirdn.cpython-38.pyc,,
scipy/signal/tests/__pycache__/test_waveforms.cpython-38.pyc,,
scipy/signal/tests/__pycache__/test_wavelets.cpython-38.pyc,,
scipy/signal/tests/__pycache__/test_windows.cpython-38.pyc,,
scipy/signal/tests/mpsig.py,sha256=3cd_WPgz9dkjalruhXxxAPYCV8Kzs0L8CH4h-0W8jX4,3430
scipy/signal/tests/test_array_tools.py,sha256=GHjEyc4Q-O3PN9C9R1CCQ2Izm-LKGZhg5mX5gZ_FxUk,3743
scipy/signal/tests/test_bsplines.py,sha256=bL1vG87hu5oFtaXh0c-SKKiampzJZqQgJkHfvjMEZyA,13487
scipy/signal/tests/test_cont2discrete.py,sha256=P43ppwW61ejgISyMFhzsx7zeUBhG3YSgGnueZ-4Be8A,15241
scipy/signal/tests/test_czt.py,sha256=gSrHRP4imVuSnNrYkNrxvb4sdYnAFp6rRUMFr-4C6Qs,7212
scipy/signal/tests/test_dltisys.py,sha256=2ETM-Wwlgc-p6-Vbpu7-Mz1Vp6UK51M8qR2yvb70gOg,22156
scipy/signal/tests/test_filter_design.py,sha256=x3OyPWyJlyGmFz6k8FDKxzBMxwGnAU5s5z19D3c4uuM,189477
scipy/signal/tests/test_fir_filter_design.py,sha256=vn7jm9fDFqEIqZw2gGm8CO7U-mZZ2NnnfzuBwBMNiq4,29606
scipy/signal/tests/test_ltisys.py,sha256=C-mRNcJ84zRkt3L-h8DxhTFPg5lel_tUprmCCgzqAME,48615
scipy/signal/tests/test_max_len_seq.py,sha256=8caIaIvvSgV9zsQ8t2MnNBeOSBLqJajPISerank05Qo,3171
scipy/signal/tests/test_peak_finding.py,sha256=2j5nEzhgFkvpudQqHOM5EZ6IqMU5cyqgpsgfVxvC0mQ,34554
scipy/signal/tests/test_result_type.py,sha256=zWsEnxBFDAKmi9IhebffJbvjY9R2aO0kFtUm7skwAm8,1679
scipy/signal/tests/test_savitzky_golay.py,sha256=e7NiioSNnsP2SXYW6xc_ygBPLske5eDIjVf6GjUCIQo,12782
scipy/signal/tests/test_signaltools.py,sha256=eyutfCtufrZNefsFWXNY7AjgSGgm5lyucoPmfTH0jZM,140680
scipy/signal/tests/test_spectral.py,sha256=QE_nZKHj8N-jT9SBt60I9S4YfD7UloTxGK7vNExCzy4,60878
scipy/signal/tests/test_upfirdn.py,sha256=qpJJpoo_Hl0yk7pkuDIAQd6Zgs7CIX5_jE6qhHNICJk,11527
scipy/signal/tests/test_waveforms.py,sha256=j80USvnR7ddMZZeqQ5PeiHbJ5m4E7qHG7QbVD5TxKA4,12326
scipy/signal/tests/test_wavelets.py,sha256=AZ1FwME5mycUifxbOlqOrZhRtPgariXIu6NoKpoFIWg,6098
scipy/signal/tests/test_windows.py,sha256=1MX_a1UYsqI_s_JQ5SalVSGqKbOOEcAisuJ9LR-w73Y,41871
scipy/signal/waveforms.py,sha256=ORwlr1q1ZjgEz-S-SmtF6tqt3UfBz8EEeFq6wx12Mrk,919
scipy/signal/wavelets.py,sha256=jJPbg5suonF-Ii9wjC6AHQAmZqcEz4F6cpw6mPs3bMw,856
scipy/signal/windows/__init__.py,sha256=G97xwCUcxrUmZO1pb3K_VfzlyUrzcJYpGjMN9UWWC2k,2171
scipy/signal/windows/__pycache__/__init__.cpython-38.pyc,,
scipy/signal/windows/__pycache__/_windows.cpython-38.pyc,,
scipy/signal/windows/__pycache__/windows.cpython-38.pyc,,
scipy/signal/windows/_windows.py,sha256=TMhGKNJOyGk_nkqDHJHqnN7LZMaj0monX-oWfVyWAyk,85991
scipy/signal/windows/windows.py,sha256=CqOWSr35U0WGthpoIA4EQxomL4JfmWjGAEExNbdpOxM,1149
scipy/sparse/__init__.py,sha256=l2E5LZeQgmZauulmWfyiXSMu-Q6ma7Yd0P-v75iuTTs,8934
scipy/sparse/__pycache__/__init__.cpython-38.pyc,,
scipy/sparse/__pycache__/_arrays.cpython-38.pyc,,
scipy/sparse/__pycache__/_base.cpython-38.pyc,,
scipy/sparse/__pycache__/_bsr.cpython-38.pyc,,
scipy/sparse/__pycache__/_compressed.cpython-38.pyc,,
scipy/sparse/__pycache__/_construct.cpython-38.pyc,,
scipy/sparse/__pycache__/_coo.cpython-38.pyc,,
scipy/sparse/__pycache__/_csc.cpython-38.pyc,,
scipy/sparse/__pycache__/_csr.cpython-38.pyc,,
scipy/sparse/__pycache__/_data.cpython-38.pyc,,
scipy/sparse/__pycache__/_dia.cpython-38.pyc,,
scipy/sparse/__pycache__/_dok.cpython-38.pyc,,
scipy/sparse/__pycache__/_extract.cpython-38.pyc,,
scipy/sparse/__pycache__/_index.cpython-38.pyc,,
scipy/sparse/__pycache__/_lil.cpython-38.pyc,,
scipy/sparse/__pycache__/_matrix_io.cpython-38.pyc,,
scipy/sparse/__pycache__/_spfuncs.cpython-38.pyc,,
scipy/sparse/__pycache__/_sputils.cpython-38.pyc,,
scipy/sparse/__pycache__/base.cpython-38.pyc,,
scipy/sparse/__pycache__/bsr.cpython-38.pyc,,
scipy/sparse/__pycache__/compressed.cpython-38.pyc,,
scipy/sparse/__pycache__/construct.cpython-38.pyc,,
scipy/sparse/__pycache__/coo.cpython-38.pyc,,
scipy/sparse/__pycache__/csc.cpython-38.pyc,,
scipy/sparse/__pycache__/csr.cpython-38.pyc,,
scipy/sparse/__pycache__/data.cpython-38.pyc,,
scipy/sparse/__pycache__/dia.cpython-38.pyc,,
scipy/sparse/__pycache__/dok.cpython-38.pyc,,
scipy/sparse/__pycache__/extract.cpython-38.pyc,,
scipy/sparse/__pycache__/lil.cpython-38.pyc,,
scipy/sparse/__pycache__/sparsetools.cpython-38.pyc,,
scipy/sparse/__pycache__/spfuncs.cpython-38.pyc,,
scipy/sparse/__pycache__/sputils.cpython-38.pyc,,
scipy/sparse/_arrays.py,sha256=VB8Wt6foIBJ5GCNcNGU46wD8XUJVlqHIs60QODUT8OE,2294
scipy/sparse/_base.py,sha256=UFPIpm78qg04wVkVO5quP9hdLmeDhTX14KNa_CrRG_I,46115
scipy/sparse/_bsr.py,sha256=avhIRfEPfdzjd-_7622zo6Gr12epfJ8GCN9YE_-ZCCI,25970
scipy/sparse/_compressed.py,sha256=PTWNpiVdIZ8WYrsEWTDXQRh8PsUjXdb6TZqI4kUwRWE,52438
scipy/sparse/_construct.py,sha256=8nOJofBG4kXLD4s6K226VuSxhtdo43Ygx6Xc9NKPGP4,31255
scipy/sparse/_coo.py,sha256=PONlO2gTcqr7CULqvjmpz4INDMfxHUi3dRRyjTcZ2ao,22788
scipy/sparse/_csc.py,sha256=sdz3KMpHstu9-KlnWxJQhHJyUPxRiFDMuA09bwyOrKY,8185
scipy/sparse/_csparsetools.cp38-win_amd64.dll.a,sha256=RM2UO3zSUbGT0cfAV3xwKWJ9CjPZXPt859YvkmFTVpg,1612
scipy/sparse/_csparsetools.cp38-win_amd64.pyd,sha256=GFT1C4zqOCbpx5BUPYBPY8_cXGw8_ywiXn3bF7F3O00,526336
scipy/sparse/_csr.py,sha256=eDFQZMoHYPZiCZQkyAuUTdfeiq4tcGS4bL4jJAJ220Q,12040
scipy/sparse/_data.py,sha256=_5yco5xed73EC073YN_f7vgS62XBTpE6E1dImLdBL0w,13293
scipy/sparse/_dia.py,sha256=QD5M7YrBl9w2l2MarNNOy8BqSEBSZmWh02qZCktDP-g,16517
scipy/sparse/_dok.py,sha256=RxEqlW587DPpWP2mJhf7KBSIxJ-Nvt80RZJWl3EsO3A,16359
scipy/sparse/_extract.py,sha256=WnyNwj3g_xJG4vp5AWd0gTzIDZOeRIU0-FpiBU3HiKk,4817
scipy/sparse/_index.py,sha256=x4p27l06QsvKuBt_NE8g31Vbg6itH9MVFolX3h_V3VA,13317
scipy/sparse/_lil.py,sha256=evPgnJ3GoHVG000gQAnTjud7syhqGuucp_juNltSyBU,18843
scipy/sparse/_matrix_io.py,sha256=YMUXVACEhG-Y35aVHgilBFRDE6Qs4KUmYv6GqyVQn1U,5530
scipy/sparse/_sparsetools.cp38-win_amd64.dll.a,sha256=QuHohf6LT3QbqfKhdlhML1MaXrRTqE1HCOq0wMc_lvA,1596
scipy/sparse/_sparsetools.cp38-win_amd64.pyd,sha256=fxLdtTNgMPdKU5YkvisnU5gCehUrEAsS2cEjw9Lx9jY,3253248
scipy/sparse/_spfuncs.py,sha256=TR6CyepiPLur-ki0rBRRqLeOmwm-m8PcXhjUY2sisFU,2057
scipy/sparse/_sputils.py,sha256=6zYuLFqRzGg3WTMSsOVgAnFYTxDf-dvq1eTEIpBteFs,13549
scipy/sparse/base.py,sha256=pSqkd4j0eVR7-LLCYaB1QqtpHaM0Prbijxepue14Z-U,1058
scipy/sparse/bsr.py,sha256=4yre3ig776iff_Z-Uh73waIhdBFOu-Xz6hFWRa9DUSU,1104
scipy/sparse/compressed.py,sha256=s7ctEABLvUH_bpwYge_TrH13UaHAxSsmID000A-Hep8,1340
scipy/sparse/construct.py,sha256=qfM6te-w3En3QBTF4g7-3u5VTwWCv13WPD7TiQTYdHA,1211
scipy/sparse/coo.py,sha256=xylo2KaQz1BlK6UG0NpOTgJkdtRcofZElc7cjZrCS8s,1138
scipy/sparse/csc.py,sha256=WRiDAxCAr0frWNtxg5oCJd_tVUCQQPgXHXpC43POkYw,872
scipy/sparse/csgraph/__init__.py,sha256=wI_NApSS88_2jDQvrrvmPw-hCoBLmVo89Emb_jZT-Bw,7947
scipy/sparse/csgraph/__pycache__/__init__.cpython-38.pyc,,
scipy/sparse/csgraph/__pycache__/_laplacian.cpython-38.pyc,,
scipy/sparse/csgraph/__pycache__/_validation.cpython-38.pyc,,
scipy/sparse/csgraph/__pycache__/setup.cpython-38.pyc,,
scipy/sparse/csgraph/_flow.cp38-win_amd64.dll.a,sha256=2rrwZWhBsbkmphG97yeeiwabGSI6WKdWYJPTm2Tb8MU,1516
scipy/sparse/csgraph/_flow.cp38-win_amd64.pyd,sha256=IYdUBYLKtlf3Yx_ywJFmJiJCnF2sq3Ye0KQATz8LZIQ,252928
scipy/sparse/csgraph/_laplacian.py,sha256=Wr7a4tvUGtBgZWfdy04X_aFfAAHyOBNVRK_IlhlPBWE,18388
scipy/sparse/csgraph/_matching.cp38-win_amd64.dll.a,sha256=rvJ2LrBXrlTG6OpmVZqV3-aGCUjnmMOd_gkAbOKqDAs,1564
scipy/sparse/csgraph/_matching.cp38-win_amd64.pyd,sha256=QlOXgmniDRNByvPUVb0LfPiSgqD3sCOfU8uQInQJ2XM,243200
scipy/sparse/csgraph/_min_spanning_tree.cp38-win_amd64.dll.a,sha256=GJd818BObxx7Du62lw2mzotXQYdZtO3VRf9l6ARZ0M0,1668
scipy/sparse/csgraph/_min_spanning_tree.cp38-win_amd64.pyd,sha256=mA_ZukIKdqOChF__oT83sZVxaUyzyHMTEtnzlxT_c4s,174080
scipy/sparse/csgraph/_reordering.cp38-win_amd64.dll.a,sha256=n_UAQZHXPiXz8ZwUhRnQATvvyZVp4aYKl-X4uzmOPMY,1588
scipy/sparse/csgraph/_reordering.cp38-win_amd64.pyd,sha256=TD0uQN0KbJPWWtS3ENDRHapgjQjsMQwVwtpxdk8mBvo,237568
scipy/sparse/csgraph/_shortest_path.cp38-win_amd64.dll.a,sha256=LX4SYrKleTF2MevAE7MkketkUQmscAMuX9w3RJ0Ljk8,1620
scipy/sparse/csgraph/_shortest_path.cp38-win_amd64.pyd,sha256=mAZBx1hcJSIxTiNDxHwHO4FyZitK36Q5bmY-dD1I8x8,396288
scipy/sparse/csgraph/_tools.cp38-win_amd64.dll.a,sha256=wyunXfHwcAniIuJArValDf2ZPHtMb3xhByrSNesGP4c,1524
scipy/sparse/csgraph/_tools.cp38-win_amd64.pyd,sha256=SX_L2TheckWYDvV5s9A0hbZ60kXUA-I54hhZGYnzBvA,154624
scipy/sparse/csgraph/_traversal.cp38-win_amd64.dll.a,sha256=JS_7f1wt2jy8vZNV2i8K5KsCqpzkjhldC_b9m2_jW48,1572
scipy/sparse/csgraph/_traversal.cp38-win_amd64.pyd,sha256=VqH-tQ_zoWor-SN5sCqu63DdAwSEJW4tEVXcZSjuJY4,146944
scipy/sparse/csgraph/_validation.py,sha256=Bqw5IaNIwCGba_4zKjpc4Ju0jhAft1GrqIy0uud6oYs,2383
scipy/sparse/csgraph/setup.py,sha256=_SD-MNtiGXfrZ2qa9jqbn7VVNreH-tuq-x3LTXC_KmM,1137
scipy/sparse/csgraph/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/csgraph/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_connected_components.cpython-38.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_conversions.cpython-38.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_flow.cpython-38.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_graph_laplacian.cpython-38.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_matching.cpython-38.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_reordering.cpython-38.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_shortest_path.cpython-38.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_spanning_tree.cpython-38.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_traversal.cpython-38.pyc,,
scipy/sparse/csgraph/tests/test_connected_components.py,sha256=djD7v_5oaAuvJx9svOlt-Hl-CDNpldfvFmycsb_34Co,3298
scipy/sparse/csgraph/tests/test_conversions.py,sha256=3gfyXwYIcC00KM6Ug2irTpMpuscTwJdAkuzd4cNzWdk,1916
scipy/sparse/csgraph/tests/test_flow.py,sha256=7Wwn4pcKcTgyfnsOeKqbNc6jtPuyYa7mIAG-dd6DZE4,7828
scipy/sparse/csgraph/tests/test_graph_laplacian.py,sha256=12qTmxyTv_1KzTLwTa6fRHv6_3BP8RnFXcTAMMkg2K0,10981
scipy/sparse/csgraph/tests/test_matching.py,sha256=AY_wn87K_I8o8aTpGk_z7RSFgi2OoVft3L1t_8NAB38,8771
scipy/sparse/csgraph/tests/test_reordering.py,sha256=4YgPFLLffko_c_k1QpqUd2NvXAwIsVbsECnIFNuVHmA,2683
scipy/sparse/csgraph/tests/test_shortest_path.py,sha256=0QopCD2rVQU1olqq_I1YNvBlfRC717LZta_pGOUg16M,14836
scipy/sparse/csgraph/tests/test_spanning_tree.py,sha256=x-m3m3NjK03t_Azn4BdJ_-SzkedmarFW1uQBhhfdTc8,2180
scipy/sparse/csgraph/tests/test_traversal.py,sha256=JP7uZD9wWbH6urmYRXtWdGvOi2YaOGTPFZP1XyMeCNo,2393
scipy/sparse/csr.py,sha256=VAwsUL0XGGZvobRQn4K63FXN_XLT29d5yaAUI7tFoSk,923
scipy/sparse/data.py,sha256=JoqtrFgPUGkwc_depYqiUbP7wVtCupA2EFQsQBBDI-k,844
scipy/sparse/dia.py,sha256=UjADiySCpJtlipGakKG07Lv64hxbyGbkdsInqax-aPU,975
scipy/sparse/dok.py,sha256=jRaZZHF34-joUkousdXd7GxMUx6s-eHWxOnDuIrMF6o,1022
scipy/sparse/extract.py,sha256=agyOt7kq2eg8pPvkuFq06whM4IhmF5xiuJznwvqHMcY,812
scipy/sparse/lil.py,sha256=DHwwW-_wY7LAKw2My3IetGNzGu6g5Eox_Bj10IhxrCE,1022
scipy/sparse/linalg/__init__.py,sha256=qaRfiIoGJ8XOJS4Y4kJtjmQgwQ4gz6-x9qJGjBBXHfE,3853
scipy/sparse/linalg/__pycache__/__init__.cpython-38.pyc,,
scipy/sparse/linalg/__pycache__/_expm_multiply.cpython-38.pyc,,
scipy/sparse/linalg/__pycache__/_interface.cpython-38.pyc,,
scipy/sparse/linalg/__pycache__/_matfuncs.cpython-38.pyc,,
scipy/sparse/linalg/__pycache__/_norm.cpython-38.pyc,,
scipy/sparse/linalg/__pycache__/_onenormest.cpython-38.pyc,,
scipy/sparse/linalg/__pycache__/_svdp.cpython-38.pyc,,
scipy/sparse/linalg/__pycache__/dsolve.cpython-38.pyc,,
scipy/sparse/linalg/__pycache__/eigen.cpython-38.pyc,,
scipy/sparse/linalg/__pycache__/interface.cpython-38.pyc,,
scipy/sparse/linalg/__pycache__/isolve.cpython-38.pyc,,
scipy/sparse/linalg/__pycache__/matfuncs.cpython-38.pyc,,
scipy/sparse/linalg/_dsolve/__init__.py,sha256=TtXewy9uEo0NWNiJ7VyfLARI_ruCsMry4Eh4b0q6zVQ,2062
scipy/sparse/linalg/_dsolve/__pycache__/__init__.cpython-38.pyc,,
scipy/sparse/linalg/_dsolve/__pycache__/_add_newdocs.cpython-38.pyc,,
scipy/sparse/linalg/_dsolve/__pycache__/linsolve.cpython-38.pyc,,
scipy/sparse/linalg/_dsolve/_add_newdocs.py,sha256=c8_fJLZeoxFD4KXx0Se76rd-rr-3VokL_o4Qkd1zEZk,3947
scipy/sparse/linalg/_dsolve/_superlu.cp38-win_amd64.dll.a,sha256=_Sqwx8qxIdvOtjiESdBZZcJkIPkrb1DJP7nFoZj90pw,1548
scipy/sparse/linalg/_dsolve/_superlu.cp38-win_amd64.pyd,sha256=kRvZNQNZ5JRF5lp_lxXaooNxbAavQxVCgvDUHH93vaw,359424
scipy/sparse/linalg/_dsolve/linsolve.py,sha256=zPl8gIviC91GgO7i5Q0EBmPs-X-LGjAK8iUfV9c1WVI,25938
scipy/sparse/linalg/_dsolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_dsolve/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/sparse/linalg/_dsolve/tests/__pycache__/test_linsolve.cpython-38.pyc,,
scipy/sparse/linalg/_dsolve/tests/test_linsolve.py,sha256=58lDZLdc6kRGWQeEBQ0tW3L-8KfzSyZJFYfgDJAxaxE,28078
scipy/sparse/linalg/_eigen/__init__.py,sha256=013F3u6pMe0J0wKjMFq_a1jSuO-pwS_h_XzmJI3xfMM,482
scipy/sparse/linalg/_eigen/__pycache__/__init__.cpython-38.pyc,,
scipy/sparse/linalg/_eigen/__pycache__/_svds.cpython-38.pyc,,
scipy/sparse/linalg/_eigen/__pycache__/_svds_doc.cpython-38.pyc,,
scipy/sparse/linalg/_eigen/_svds.py,sha256=5YKPSEGHktFbJp8OuiizBlkB3mhfxN4Axyw3DjknTH0,21293
scipy/sparse/linalg/_eigen/_svds_doc.py,sha256=TmbM_kPJcmNgdNAdH4X8gK5lKHVkXU0X3Zf1UmvUf_k,15923
scipy/sparse/linalg/_eigen/arpack/COPYING,sha256=_LPGx94UYM99CGPDxZluUY64AVouztNpEfPaF4RAs98,1937
scipy/sparse/linalg/_eigen/arpack/__init__.py,sha256=EU0vXuTlZzMcXxCITUTVxUZ_N0EgcDF06Q2rMzy0Q3o,582
scipy/sparse/linalg/_eigen/arpack/__pycache__/__init__.cpython-38.pyc,,
scipy/sparse/linalg/_eigen/arpack/__pycache__/arpack.cpython-38.pyc,,
scipy/sparse/linalg/_eigen/arpack/_arpack.cp38-win_amd64.dll.a,sha256=fokJrp4GMT3QdZOgBDjoY5vFVdZ1LEgBSJ9UxHT4DAk,1540
scipy/sparse/linalg/_eigen/arpack/_arpack.cp38-win_amd64.pyd,sha256=qpTwMARedEjNx1ITrsSkvgSkF7sMILnPYi_l_5ZFH-s,782336
scipy/sparse/linalg/_eigen/arpack/arpack.py,sha256=1RFnPuAGevWvls_ZkE-dNozSsQA_DIjtVAUNtS1iRqc,69029
scipy/sparse/linalg/_eigen/arpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/arpack/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/sparse/linalg/_eigen/arpack/tests/__pycache__/test_arpack.cpython-38.pyc,,
scipy/sparse/linalg/_eigen/arpack/tests/test_arpack.py,sha256=kBYPyo4-IHxTjc2eD5XGDsNKZhrSWLjPhqj2377F4GI,24620
scipy/sparse/linalg/_eigen/lobpcg/__init__.py,sha256=8aw6542gPHNriFRBENTZ5rb1M3cqJKToG--paaqpaxM,436
scipy/sparse/linalg/_eigen/lobpcg/__pycache__/__init__.cpython-38.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/__pycache__/lobpcg.cpython-38.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/lobpcg.py,sha256=ss5gqZjycxwjjV4zm7xQ4nckqHJqlABDUGo6jY_0-Zs,38323
scipy/sparse/linalg/_eigen/lobpcg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/lobpcg/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/tests/__pycache__/test_lobpcg.cpython-38.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/tests/test_lobpcg.py,sha256=Om4taxXfptzG4VBaKAK_MGzqk_uNI0PubOILpywEI0Q,19410
scipy/sparse/linalg/_eigen/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/sparse/linalg/_eigen/tests/__pycache__/test_svds.cpython-38.pyc,,
scipy/sparse/linalg/_eigen/tests/test_svds.py,sha256=1C7aLEwaeUvUBS7AXnDRVcSi00UhvyjohpO9DcQd1WM,38192
scipy/sparse/linalg/_expm_multiply.py,sha256=5lcFrP3KKdyJKCP83LZWmu2HexVcJwe4vaAyHOsAVus,26952
scipy/sparse/linalg/_interface.py,sha256=5Kx3gU7EoKAlmuwNBwTG6AmneI-LQQSETk9ND5M4m7I,26118
scipy/sparse/linalg/_isolve/__init__.py,sha256=rFcoMYM70Tbtjlc3xhWhgjdNHUOwc4TSV1sOFZwL6Gw,499
scipy/sparse/linalg/_isolve/__pycache__/__init__.cpython-38.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/_gcrotmk.cpython-38.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/iterative.cpython-38.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lgmres.cpython-38.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lsmr.cpython-38.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lsqr.cpython-38.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/minres.cpython-38.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/tfqmr.cpython-38.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/utils.cpython-38.pyc,,
scipy/sparse/linalg/_isolve/_gcrotmk.py,sha256=-901baTn-ixxEgrVv8u79XOI5QmlwJ8qVaFMsUbIrSU,16464
scipy/sparse/linalg/_isolve/_iterative.cp38-win_amd64.dll.a,sha256=WBdmwaKjigP-sZfZKTUVBIja2EujOeR9_exAVrU29ZA,1572
scipy/sparse/linalg/_isolve/_iterative.cp38-win_amd64.pyd,sha256=27SxBq_4AxA5Zi9BYiAtZzFVSjlZKw30shpsQg3VORk,258048
scipy/sparse/linalg/_isolve/iterative.py,sha256=QRnlzkcv2qdjoQxE903_xpWjTc-zPWW1hAbYE7QHz1s,31291
scipy/sparse/linalg/_isolve/lgmres.py,sha256=lRjz0M-A25b65fyxWpJLPsYFe-Ck_NmLEH_Csa24EbU,9169
scipy/sparse/linalg/_isolve/lsmr.py,sha256=Qt2HfV9h9bVnBGDNcOWN5hKxA-3SPLiPRfbubCPYLz4,16167
scipy/sparse/linalg/_isolve/lsqr.py,sha256=vW_qW6WerRpHNL20z_YdB_AfNac_udpCtwpsJlphSUI,21829
scipy/sparse/linalg/_isolve/minres.py,sha256=DnFu3TM9pxBCft08y1-OGcfbOcTl41AZUAC7s4x80g4,11817
scipy/sparse/linalg/_isolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_isolve/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_gcrotmk.cpython-38.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_iterative.cpython-38.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lgmres.cpython-38.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lsmr.cpython-38.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lsqr.cpython-38.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_minres.cpython-38.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_utils.cpython-38.pyc,,
scipy/sparse/linalg/_isolve/tests/test_gcrotmk.py,sha256=wCYQKfJF9dL3LNOFR63STx171miMRqtPDnHFR_83sjQ,5573
scipy/sparse/linalg/_isolve/tests/test_iterative.py,sha256=QN-uJ4r-OtJ2Pzn_DE3myCsRel0aGDMVFA_mqwGq-ys,27909
scipy/sparse/linalg/_isolve/tests/test_lgmres.py,sha256=30He16TFGH7PxmTPiG4gD9hZXOwMSTekFesHds19Ea0,7271
scipy/sparse/linalg/_isolve/tests/test_lsmr.py,sha256=Ijiw9oqe4gxNPS52Cq1x4lzS5FhZ2xztuDz8lwcqknQ,7552
scipy/sparse/linalg/_isolve/tests/test_lsqr.py,sha256=INE0Xa0UrygIjQBMaTmQ_ANI3I0FI4of4DUo9xYfZCI,4963
scipy/sparse/linalg/_isolve/tests/test_minres.py,sha256=Qyh4jVaBlhjUstHZaIKnx5LBfR4pUbqYRz83DK_T_OI,2543
scipy/sparse/linalg/_isolve/tests/test_utils.py,sha256=5LFVBgy2zQvakzgEMdVl8hxcCDXEMOZAXpm3x53Cyw0,255
scipy/sparse/linalg/_isolve/tfqmr.py,sha256=RkxysQ_Cdz6DnzUIU38PA_doE89zwrb3d2WhrDhcE5k,6425
scipy/sparse/linalg/_isolve/utils.py,sha256=LjktcTFezfc_dtnhxbzOfGB-TZhsjTxx_CDVLDA4MZE,3725
scipy/sparse/linalg/_matfuncs.py,sha256=hbadwHo93q-FJLLAkk3dsNCd-SAei3ZQ8qxTLP_zm9o,28091
scipy/sparse/linalg/_norm.py,sha256=YOCMIrz4V8Pg4QS1f3Xm5xQJKsWJb-trMPN1jGyauYw,6255
scipy/sparse/linalg/_onenormest.py,sha256=wVJXPEI7AwZQT0JOtC_044bM6DaQC7oMkMhxGgPZE_E,15953
scipy/sparse/linalg/_propack/_cpropack.cp38-win_amd64.dll.a,sha256=y44mgasJbzwp3HSW3Ey77vaTNO91OiwjCMapytGCZMY,1564
scipy/sparse/linalg/_propack/_cpropack.cp38-win_amd64.pyd,sha256=eRK8mkAR3cdAuYT7EWj0GBhOyB2A8PON5wQLc502CYU,457728
scipy/sparse/linalg/_propack/_dpropack.cp38-win_amd64.dll.a,sha256=wUEoVqyxVLaSo-9x1NQO20qaIFfNiaesnE7Q0l8OqWk,1564
scipy/sparse/linalg/_propack/_dpropack.cp38-win_amd64.pyd,sha256=rzG1DqS9vCEaVhL_iy_3hP2qS83besB37A2zLTadAjc,444416
scipy/sparse/linalg/_propack/_spropack.cp38-win_amd64.dll.a,sha256=-0Prsmpa94lePIKsbLBDsUan9G73sHdRI3_X3RFMEMQ,1564
scipy/sparse/linalg/_propack/_spropack.cp38-win_amd64.pyd,sha256=anFj6uXCOnwfHDIlT7a30tBj0BBGYw5jYnLhzuIDipU,443904
scipy/sparse/linalg/_propack/_zpropack.cp38-win_amd64.dll.a,sha256=slML7WEM9PLFDALmrevXAffz9bypxINDzNvxTKUNhgQ,1564
scipy/sparse/linalg/_propack/_zpropack.cp38-win_amd64.pyd,sha256=xg3nJzhROW3ioG4a_-YdyyGACX1_T-lCw9QVVfpC-Cs,458752
scipy/sparse/linalg/_svdp.py,sha256=T51kBDoeF44jYBe8ZXWehJjDHNRxTqSeZWonWh7hA1s,11902
scipy/sparse/linalg/dsolve.py,sha256=nYG7t4XlrdAoPvgrAepq4XNaINC9yfSjMbMG5EfByRI,1241
scipy/sparse/linalg/eigen.py,sha256=bOh3MTGtHhSsS3ArrwRzed87-oE8AJJPZ2oE3UKraSU,1188
scipy/sparse/linalg/interface.py,sha256=Se4ac_HtvwbuOCcwtvnYYI0Rr-iyiQBLn47vGHhRBRU,965
scipy/sparse/linalg/isolve.py,sha256=Vusbrwgbf3BuiOw_EZLmR7pFpxx5knsYUHj3y6GpN1k,934
scipy/sparse/linalg/matfuncs.py,sha256=wTkNcrOl0TuJuPq1e0QdM1g2ebfZso7mjoKhzF2Xl80,978
scipy/sparse/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_expm_multiply.cpython-38.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_interface.cpython-38.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_matfuncs.cpython-38.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_norm.cpython-38.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_onenormest.cpython-38.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_propack.cpython-38.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_pydata_sparse.cpython-38.pyc,,
scipy/sparse/linalg/tests/propack_test_data.npz,sha256=v-NNmpI1Pgj0APODcTblU6jpHUQRhpE9ObWb-KYnu6M,600350
scipy/sparse/linalg/tests/test_expm_multiply.py,sha256=s9J-TYvGQCB_zD2jc_jSpZ5me8pxHp8mQPJII-8W6_E,14264
scipy/sparse/linalg/tests/test_interface.py,sha256=7zEFEiHZONpOjOMUrAp9wKYnmhOd1h7uO5e1rJ1p7sI,16968
scipy/sparse/linalg/tests/test_matfuncs.py,sha256=AeKJcvJaIPdvfBcCj0hnjtidWw1jli4fxq-pN6Ndrc0,21861
scipy/sparse/linalg/tests/test_norm.py,sha256=ZuZ0yjU4U5XmkGkWJLPFTdAwF0T-sNUfaHovTuuXcUI,6304
scipy/sparse/linalg/tests/test_onenormest.py,sha256=WhTjQtFBlX8mXQ1XckfBOyxRT6kfbPenhSh9dzFv9Pc,9481
scipy/sparse/linalg/tests/test_propack.py,sha256=g2Rws63UIosl6q_8B3uwE068IahiAXQq9KILolbtaW4,6471
scipy/sparse/linalg/tests/test_pydata_sparse.py,sha256=RAcfJVKMNzL3QZV1TG93kJLIintPxh9CIUDLMEDHw-M,6365
scipy/sparse/sparsetools.py,sha256=RqGWecObVQBPDNkavAVJW9WulL-etEd3zbheY2OXdwg,2496
scipy/sparse/spfuncs.py,sha256=wpFJZyDrXihxfCzKKPqf6vz9wJrNHysl_UOpUq7uvXo,871
scipy/sparse/sputils.py,sha256=qdSMDzaOwOKiN4BDaaxfJqbj2-aPyDvNc_nL3WnS0_A,1239
scipy/sparse/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/sparse/tests/__pycache__/test_array_api.cpython-38.pyc,,
scipy/sparse/tests/__pycache__/test_base.cpython-38.pyc,,
scipy/sparse/tests/__pycache__/test_construct.cpython-38.pyc,,
scipy/sparse/tests/__pycache__/test_csc.cpython-38.pyc,,
scipy/sparse/tests/__pycache__/test_csr.cpython-38.pyc,,
scipy/sparse/tests/__pycache__/test_extract.cpython-38.pyc,,
scipy/sparse/tests/__pycache__/test_matrix_io.cpython-38.pyc,,
scipy/sparse/tests/__pycache__/test_sparsetools.cpython-38.pyc,,
scipy/sparse/tests/__pycache__/test_spfuncs.cpython-38.pyc,,
scipy/sparse/tests/__pycache__/test_sputils.cpython-38.pyc,,
scipy/sparse/tests/data/csc_py2.npz,sha256=usJ_Gj6x_dEC2uObfdYc6D6C8JY4jjROFChQcZhNAfo,846
scipy/sparse/tests/data/csc_py3.npz,sha256=axuEMVxwd0F-cgUS0IalpiF8KHW4GNJ3BK6bcjfGnf4,851
scipy/sparse/tests/test_array_api.py,sha256=dmARElG90ujY1auayLUfKGgsiq6XGtX3-Yd0QfOf8VQ,7819
scipy/sparse/tests/test_base.py,sha256=rj1DQISoEubnaY3ocRn216lLU35N2_FJAuwdSQhb42s,186576
scipy/sparse/tests/test_construct.py,sha256=dGc_uxoZDW0KRsj6Y3s1L0-fj8P08JamcrEbzIQDAD4,25473
scipy/sparse/tests/test_csc.py,sha256=mfZbIugI1T0etqGNgjH_3Skxj-13481Dog9U7cXaWx0,3000
scipy/sparse/tests/test_csr.py,sha256=a6BR5zJNPdQnZ92sxkVGK3dpE7Sgj6Y4uUPKQgZFxns,5820
scipy/sparse/tests/test_extract.py,sha256=4XpaYwpNmZhNF_sTF3vKz13uKlPTEd1VhE6Ocr-L8fk,1355
scipy/sparse/tests/test_matrix_io.py,sha256=1KftcjrHqffNjZ_0B57ZDjvphW0R00tMk_duUkk04iw,2628
scipy/sparse/tests/test_sparsetools.py,sha256=Nr2BDx8QMaTTMaiW1buSVRrYo3xqPwgR2a-4I_Je6LY,10778
scipy/sparse/tests/test_spfuncs.py,sha256=jUPhJjGat0YjcEeZWrD6Iz2G3el0ubAyts4ezvbrCM8,3355
scipy/sparse/tests/test_sputils.py,sha256=D_MfqiIcGS21lXBnw-_ghX1dAjyFzw2M0CVVX29eEok,7023
scipy/spatial/__init__.py,sha256=vp_VwCoHHTQv4Lixl95LERSAVr2YU44E6LkRxj8LwPU,3760
scipy/spatial/__pycache__/__init__.cpython-38.pyc,,
scipy/spatial/__pycache__/_geometric_slerp.cpython-38.pyc,,
scipy/spatial/__pycache__/_kdtree.cpython-38.pyc,,
scipy/spatial/__pycache__/_plotutils.cpython-38.pyc,,
scipy/spatial/__pycache__/_procrustes.cpython-38.pyc,,
scipy/spatial/__pycache__/_spherical_voronoi.cpython-38.pyc,,
scipy/spatial/__pycache__/ckdtree.cpython-38.pyc,,
scipy/spatial/__pycache__/distance.cpython-38.pyc,,
scipy/spatial/__pycache__/kdtree.cpython-38.pyc,,
scipy/spatial/__pycache__/qhull.cpython-38.pyc,,
scipy/spatial/_ckdtree.cp38-win_amd64.dll.a,sha256=hFeC7vn35zVZddWjepIjjHrdeRWVh4VUcuYOZl9xzhg,1548
scipy/spatial/_ckdtree.cp38-win_amd64.pyd,sha256=nx4zPCm4j-UfDv2fVCacOeyShk1oxEzRoawfuiyi40Y,1580544
scipy/spatial/_ckdtree.pyi,sha256=xLbhs62yWmTbrhp77lIc1b5eVg22oXlPUlD3TeuRRRI,6232
scipy/spatial/_distance_pybind.cp38-win_amd64.dll.a,sha256=bNCmw_X3NuDSOK6ZwoEPeIIoEqcHE24jUkSdsJII8ww,1644
scipy/spatial/_distance_pybind.cp38-win_amd64.pyd,sha256=JcD_G-I4GsAzaX_khOn8DoMp1NtC7xUyUKayCAoSsdo,1160704
scipy/spatial/_distance_wrap.cp38-win_amd64.dll.a,sha256=4MWZpL5qhZrYXRYBkXwpp94nytjjsIPeigdlX_bGFa0,1620
scipy/spatial/_distance_wrap.cp38-win_amd64.pyd,sha256=kx99yFDwmjU8bQAGufELSQkLnvjtvCGazYNvYcAV5jk,109568
scipy/spatial/_geometric_slerp.py,sha256=FqF1FoX3fnMIaHER7e1onwWlLDLufDiumAy0QSY79WQ,8184
scipy/spatial/_hausdorff.cp38-win_amd64.dll.a,sha256=AB1STDy-sDC3jQCJzOTZBiYuUWJLh7i1VHAN5Z8h5y8,1572
scipy/spatial/_hausdorff.cp38-win_amd64.pyd,sha256=_c_Iy_3x410Rzmd5yf67GDm-cp2wGD-6Vmhud4BSUJA,163328
scipy/spatial/_kdtree.py,sha256=0pC_iHnxvA86YJ-7Ew4Nu6ZvxSazaLlQoid62ny6Tmc,34364
scipy/spatial/_plotutils.py,sha256=hoLzA44Oqx9jZu7gFfZ57k1-WfVWkKpXUx7lChRVP7M,7437
scipy/spatial/_procrustes.py,sha256=xfcyARufJ98uzxJJzHwLG_VHXgajhNPZpIHTsniiaXI,4559
scipy/spatial/_qhull.cp38-win_amd64.dll.a,sha256=OCLiB4FsUG-5HzBkz13Dk_v9WmnlfuC9-Bbw61KUhr0,1524
scipy/spatial/_qhull.cp38-win_amd64.pyd,sha256=FDJkTjZTO_2zkJXyMIORLZHYGRKMx22nPqbmQI0uG_I,977408
scipy/spatial/_qhull.pyi,sha256=ZvFg5BGUErddUuLcR7Qx8FJZ2Mzoa1YbxHwgoaUSpvg,6222
scipy/spatial/_spherical_voronoi.py,sha256=zWU04GcNWG322emP99NFDXPqT01kHD4ZQoWF44upoUA,13915
scipy/spatial/_voronoi.cp38-win_amd64.dll.a,sha256=3GCG_RQ-8DPTU28Rj_Pk33Rgjd8msJa-PQGXgFbIecA,1548
scipy/spatial/_voronoi.cp38-win_amd64.pyd,sha256=9T3YeqZcn361RGzblbjDCX_P9wGmAhCBmKYXXKOksFM,159232
scipy/spatial/_voronoi.pyi,sha256=osYTBe1_DUSH-Ij0l_baKD06dqiB46DtYYSWZbVJAF0,141
scipy/spatial/ckdtree.py,sha256=MHwcJFwNH_asRheaq1FkqzFAqYLvjFyZoF6LVWlC0y8,897
scipy/spatial/distance.py,sha256=_tfWXxu-hDygf6zaqiCSyKrSzO768IflqrNWPCwkc5k,93166
scipy/spatial/distance.pyi,sha256=vnrGaEhi_VyYZgLVDQGWdnH7pvBqTlPpMOlABMC7H_8,5713
scipy/spatial/kdtree.py,sha256=wMI4S04wBuzIZXO7nVWdMi-WEXZTacUABER6dEQN0-w,904
scipy/spatial/qhull.py,sha256=AXTCNWWiZxgQKkTG0tNVj60fWY5BCBuvV_1qqa40ZCM,926
scipy/spatial/qhull_src/COPYING.txt,sha256=liRS5zfffHQ6PcJ0QjIHECi4wEUOdnSlUFfDMOoZd-s,1673
scipy/spatial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/spatial/tests/__pycache__/test__plotutils.cpython-38.pyc,,
scipy/spatial/tests/__pycache__/test__procrustes.cpython-38.pyc,,
scipy/spatial/tests/__pycache__/test_distance.cpython-38.pyc,,
scipy/spatial/tests/__pycache__/test_hausdorff.cpython-38.pyc,,
scipy/spatial/tests/__pycache__/test_kdtree.cpython-38.pyc,,
scipy/spatial/tests/__pycache__/test_qhull.cpython-38.pyc,,
scipy/spatial/tests/__pycache__/test_slerp.cpython-38.pyc,,
scipy/spatial/tests/__pycache__/test_spherical_voronoi.cpython-38.pyc,,
scipy/spatial/tests/data/cdist-X1.txt,sha256=LTxfJD5Hzxi0mmMzhzCgp0dUJflrg6O5duj8LVxa9lA,5760
scipy/spatial/tests/data/cdist-X2.txt,sha256=Tb2q8iKbPvUlxO_n1pBfYkWvu1z_LuBcwFXCIRZgBOg,11520
scipy/spatial/tests/data/degenerate_pointset.npz,sha256=BIq8Hd2SS_LU0fIWAVVS7ZQx-emVRvvzgnaO2lh4gXU,22548
scipy/spatial/tests/data/iris.txt,sha256=sWic54AuDE3IyLk6XOw0BcyCSzr5BkXrpDJc7B00OTQ,15150
scipy/spatial/tests/data/pdist-boolean-inp.txt,sha256=D6kMC8OK5H2Z6G07l592VtEENnehwy8wuM1rjVq7wXU,50020
scipy/spatial/tests/data/pdist-chebyshev-ml-iris.txt,sha256=Ysw196_xQ_ApwKiaB7OXZ-gVvhvhFH8wUZAw7EfacTs,178802
scipy/spatial/tests/data/pdist-chebyshev-ml.txt,sha256=jtPmZwg5AfpIom-wMjty8_6tI69ePUnJ7eYdFyFq9G4,3042
scipy/spatial/tests/data/pdist-cityblock-ml-iris.txt,sha256=Wf3dVmhan8EEYiKAsrgN5WNMmAmH9v9NjJpO4xRdmPE,178802
scipy/spatial/tests/data/pdist-cityblock-ml.txt,sha256=aMGSV28cjKIV2CRfdR2zZsisln6oxi1tYQbxZ9tuMMo,3042
scipy/spatial/tests/data/pdist-correlation-ml-iris.txt,sha256=PdVB_30O5CHL9ms8KRB1kFiswp9Sed8CV540WQvNaJc,178802
scipy/spatial/tests/data/pdist-correlation-ml.txt,sha256=VDgXqx3vUn26r1EZFcSk-rq1-wef2WVFcoBWLG8zvro,3042
scipy/spatial/tests/data/pdist-cosine-ml-iris.txt,sha256=q5nrvDyyqdltrjBRyT5eFVQIW-y2Igb2JdC8tBAXl7E,178802
scipy/spatial/tests/data/pdist-cosine-ml.txt,sha256=nLK5yDtcmU-svoYeLPuZZoolZpxmpOaeLswLLR5QoGM,3042
scipy/spatial/tests/data/pdist-double-inp.txt,sha256=2UzMIvz_r7ZT2Twt-jH-pst0Avue4ueGZezk7T2_6q0,50020
scipy/spatial/tests/data/pdist-euclidean-ml-iris.txt,sha256=VWMmzL2jP_vhDzTBhYtZH7-VBKJFpafRROrefDeokWI,178802
scipy/spatial/tests/data/pdist-euclidean-ml.txt,sha256=Kn8vHq17IbWtj1G9XzCFY1EuXFTPnD7gX9FwW2KSm44,3042
scipy/spatial/tests/data/pdist-hamming-ml.txt,sha256=OqjHwrqGBwmwsfTeN2fI1EFiWSpOA6ZlZYwN121Ys1o,3042
scipy/spatial/tests/data/pdist-jaccard-ml.txt,sha256=nj_JYQ6bfvhlFDKu5OC_zBeOzPKgewkmpxScJEPAzhs,3042
scipy/spatial/tests/data/pdist-jensenshannon-ml-iris.txt,sha256=wz1O-EHZT2cDwFtow4ZoLWQgS8WY6DPzb_Ik9SXA8Fs,172739
scipy/spatial/tests/data/pdist-jensenshannon-ml.txt,sha256=rps5AwUK4Up9qCR3wAE1wfwuKE5ftKZTHChPYH0hsfA,2819
scipy/spatial/tests/data/pdist-minkowski-3.2-ml-iris.txt,sha256=rfWuQ4RgSfY0ko_jqlSTjQIJYGCHeEbEwjHYpAC9Ivo,178802
scipy/spatial/tests/data/pdist-minkowski-3.2-ml.txt,sha256=jKlA87ldGof8ndZLkV9l97-Tsh2uXTIlyZRsMoWr0gA,3042
scipy/spatial/tests/data/pdist-minkowski-5.8-ml-iris.txt,sha256=bWYNT7XqpLTGkQBMfnCFJ7bnoxooZlzfI0_O54UOquI,178802
scipy/spatial/tests/data/pdist-seuclidean-ml-iris.txt,sha256=bGIB7ygJC8CGHAoVKtwOdPgAqDM5bzffpgxYMTCKgmQ,178802
scipy/spatial/tests/data/pdist-seuclidean-ml.txt,sha256=g9-qvSA4qe4S0xPxnTa17zqa4Z-P5S0TV1gAsbr-RAs,3042
scipy/spatial/tests/data/pdist-spearman-ml.txt,sha256=vpgBfMlrUUH-nYlnLqm_Ms-MIjrgOlNCW-TW8C4Yan0,3042
scipy/spatial/tests/data/random-bool-data.txt,sha256=sydRG9aL6CH9i7-Nt5X2mNyQDsu9OC1fbUD-MRdt5bc,6100
scipy/spatial/tests/data/random-double-data.txt,sha256=OcmsKJSbi_eY65ld50iAatodSzw2NxFfDJIDHx0lOpQ,75100
scipy/spatial/tests/data/random-int-data.txt,sha256=saHYkK0CMRzV78RV18tUGIF4uKQlOqkN4cEWqD4gnFw,10366
scipy/spatial/tests/data/random-uint-data.txt,sha256=aBbEt1tldXIvAvkDMbSr9WddkxXtMTa05Qq8j4s55CE,8811
scipy/spatial/tests/data/selfdual-4d-polytope.txt,sha256=x-c_sDM8alTwdaj7vYvGAQEsjrg6n5LycGrrPR4dlY0,507
scipy/spatial/tests/test__plotutils.py,sha256=LakEz_Dtuw8CExd_0__YE45wJ2Kdjcy1-1MzkZ-nob8,1997
scipy/spatial/tests/test__procrustes.py,sha256=iZypw3jevzOPiHv15UZHwc3sXE3pNlUlCeKTZfoL5vE,5090
scipy/spatial/tests/test_distance.py,sha256=tA65K9frv0jqnRojPHhrTZR0HClj1ina9-IaMtu82yw,86126
scipy/spatial/tests/test_hausdorff.py,sha256=7ZZJjse3SoM8wYx_roN7d2nYIJwHAh_QbXD2ZqZxEjE,7286
scipy/spatial/tests/test_kdtree.py,sha256=pru2HmoyZ1teeeRUclaCRa8-y2ZPuKg-5Zax-dJUuhQ,48807
scipy/spatial/tests/test_qhull.py,sha256=JryBMtVCRJWIX6ERSY6ttHfoCQX8pOeokL27EnnAO3Q,45325
scipy/spatial/tests/test_slerp.py,sha256=DDZie6nQFvdtM8PAY26gnUluKQALFUPfudI-WQbP1cA,16812
scipy/spatial/tests/test_spherical_voronoi.py,sha256=ZvVei6LbfAFhZn1muYetMOViik6zog-CKlDaHTJjt8I,14716
scipy/spatial/transform/__init__.py,sha256=AR19SJ8oEv3Pt2eXGRiHkglp7wU_rGvRJ8JEIBqs4AI,729
scipy/spatial/transform/__pycache__/__init__.cpython-38.pyc,,
scipy/spatial/transform/__pycache__/_rotation_groups.cpython-38.pyc,,
scipy/spatial/transform/__pycache__/_rotation_spline.cpython-38.pyc,,
scipy/spatial/transform/__pycache__/rotation.cpython-38.pyc,,
scipy/spatial/transform/_rotation.cp38-win_amd64.dll.a,sha256=C3yP_GMXvpig2YM8CZt3RlmIeZrjgrH-fgdPRE8IVCs,1564
scipy/spatial/transform/_rotation.cp38-win_amd64.pyd,sha256=lkho6uXnEeIx04ElGGlWfb92oqy5xs077lwFLos7WPk,603648
scipy/spatial/transform/_rotation.pyi,sha256=H9cG-ncnwh4Eh0BkzxdlKNXC5Ia_Pi3o7bDrgGecCVA,2700
scipy/spatial/transform/_rotation_groups.py,sha256=XBEazTsMg71VeDaexivk9VYmVDgHPkNji9FrlRypjyc,4562
scipy/spatial/transform/_rotation_spline.py,sha256=bQlcYfmYrF1-__9YFv_Utkr-5yLRwlsXJcS-y6eENDs,14543
scipy/spatial/transform/rotation.py,sha256=JbzMKJ7p-VSz4y1o5QEVZk6Yc6-c_FloPaLDkPpQHYU,905
scipy/spatial/transform/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/transform/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation.cpython-38.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_groups.cpython-38.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_spline.cpython-38.pyc,,
scipy/spatial/transform/tests/test_rotation.py,sha256=0XqrIOxDA53qPfwCLWnWH76C_bhcvrNVR0ZlDWpFbjI,42740
scipy/spatial/transform/tests/test_rotation_groups.py,sha256=mATjBt62JCkoqpEMHU2zXB9HF-h7KFpWMBrSLyxnkTA,5729
scipy/spatial/transform/tests/test_rotation_spline.py,sha256=yO_VY18e0JOi44I9vd40fKdq5TqujGXHLD2dLg4jw-8,5196
scipy/special.pxd,sha256=vMRWBwprU2SEQaeIjffhknQfsKpMeop9GKtIKp2dODs,38
scipy/special/__init__.py,sha256=l5RQ9Gk16eUSvQd3KW0n61S-iBb5GStFxHOMyGSfUqs,29715
scipy/special/__pycache__/__init__.cpython-38.pyc,,
scipy/special/__pycache__/_add_newdocs.cpython-38.pyc,,
scipy/special/__pycache__/_basic.cpython-38.pyc,,
scipy/special/__pycache__/_ellip_harm.cpython-38.pyc,,
scipy/special/__pycache__/_lambertw.cpython-38.pyc,,
scipy/special/__pycache__/_logsumexp.cpython-38.pyc,,
scipy/special/__pycache__/_mptestutils.cpython-38.pyc,,
scipy/special/__pycache__/_orthogonal.cpython-38.pyc,,
scipy/special/__pycache__/_sf_error.cpython-38.pyc,,
scipy/special/__pycache__/_spfun_stats.cpython-38.pyc,,
scipy/special/__pycache__/_spherical_bessel.cpython-38.pyc,,
scipy/special/__pycache__/_testutils.cpython-38.pyc,,
scipy/special/__pycache__/add_newdocs.cpython-38.pyc,,
scipy/special/__pycache__/basic.cpython-38.pyc,,
scipy/special/__pycache__/orthogonal.cpython-38.pyc,,
scipy/special/__pycache__/sf_error.cpython-38.pyc,,
scipy/special/__pycache__/specfun.cpython-38.pyc,,
scipy/special/__pycache__/spfun_stats.cpython-38.pyc,,
scipy/special/_add_newdocs.py,sha256=6Hhh4XvYah0BykGl27sQsVdVUvNopdjyBB5wbUlqzLg,380783
scipy/special/_basic.py,sha256=CIhLSOB03goQ8nAnIOrd0MFcScg-jfIsPSMf1P5wLu4,93047
scipy/special/_comb.cp38-win_amd64.dll.a,sha256=S-4EP8_xRKWcAB_bkF1DrZUh-cktpHfD79HitXH4TK8,1516
scipy/special/_comb.cp38-win_amd64.pyd,sha256=lI85Kn-IxkPwKf77ECZ680ibC4QqrNkZYZJxoHo9kWM,35840
scipy/special/_ellip_harm.py,sha256=T2SA2CUqdQbAaI-gGhswXc_NTu1zDfgqqgtbRm_Sssw,5480
scipy/special/_ellip_harm_2.cp38-win_amd64.dll.a,sha256=mz4t7Idx3aKauidO_B66CTTXUuvOM27PkpNWzkO7Zf0,1612
scipy/special/_ellip_harm_2.cp38-win_amd64.pyd,sha256=yggK1pTVWILGVZH-bz5FTnpq40rtNyucuAu9wxg7csE,78848
scipy/special/_lambertw.py,sha256=UPisDJ7qZgWY2g_YeRd2XOnZOfE8ARp8TP9Gef9MEdw,3100
scipy/special/_logsumexp.py,sha256=atmzxn3ZufgjPG-insiMHtIZktn0SvdZjZIBmCwTzEI,8821
scipy/special/_mptestutils.py,sha256=GEbiyuu8nmbMHhVFHhBpMpqfbiLAauk-zT4y1hoYdFA,14994
scipy/special/_orthogonal.py,sha256=pJ0BGEFF1H2V03-aHVtADcJmlMqGRnJcPMIRkj9tHZY,76478
scipy/special/_orthogonal.pyi,sha256=Wkpi11xFox7vILG7nvqw-OzbnzzxFFyAFmSZl_E4Jmk,8677
scipy/special/_precompute/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/_precompute/__pycache__/__init__.cpython-38.pyc,,
scipy/special/_precompute/__pycache__/cosine_cdf.cpython-38.pyc,,
scipy/special/_precompute/__pycache__/expn_asy.cpython-38.pyc,,
scipy/special/_precompute/__pycache__/gammainc_asy.cpython-38.pyc,,
scipy/special/_precompute/__pycache__/gammainc_data.cpython-38.pyc,,
scipy/special/_precompute/__pycache__/lambertw.cpython-38.pyc,,
scipy/special/_precompute/__pycache__/loggamma.cpython-38.pyc,,
scipy/special/_precompute/__pycache__/struve_convergence.cpython-38.pyc,,
scipy/special/_precompute/__pycache__/utils.cpython-38.pyc,,
scipy/special/_precompute/__pycache__/wright_bessel.cpython-38.pyc,,
scipy/special/_precompute/__pycache__/wright_bessel_data.cpython-38.pyc,,
scipy/special/_precompute/__pycache__/wrightomega.cpython-38.pyc,,
scipy/special/_precompute/__pycache__/zetac.cpython-38.pyc,,
scipy/special/_precompute/cosine_cdf.py,sha256=Wjm4O1FQiRC6HKspIaUwgWuEb98gifbXovLcVyR7FXQ,373
scipy/special/_precompute/expn_asy.py,sha256=bORm_1lsTfTM2KDgsdyfBI1AcM2taXt09yddfzfsXtI,1387
scipy/special/_precompute/gammainc_asy.py,sha256=8XBo6RBUzdxUhH7GrVeVTzsMJgSnGb9NpknFAdHwcBY,2618
scipy/special/_precompute/gammainc_data.py,sha256=2AzUm4Ce98wSFhijBmhuDqhEEP1IL4J_NAlyXyinQA8,4217
scipy/special/_precompute/lambertw.py,sha256=e1HCRYiDqJTZ-RcJsqCIRqndrgeXbo8OcUc70QWFRQo,2045
scipy/special/_precompute/loggamma.py,sha256=J2jz62nBO-7JEOA7nG4JQQt5BuXSfUPRik2r3pwTr_s,1137
scipy/special/_precompute/struve_convergence.py,sha256=QURD_erEKfkgtZDMau3tl_X3aVHeCQg6Fz03EWD_BRg,3552
scipy/special/_precompute/utils.py,sha256=tJpjcNS6WaXzqP7cLac7yFyXfb_pfe3AKvDCa0mwi9A,925
scipy/special/_precompute/wright_bessel.py,sha256=DSYXJT1fqKewwxUQ3HeikpOMPWgC-r4ty4Vphd9-KhI,13224
scipy/special/_precompute/wright_bessel_data.py,sha256=B7wEarAqI6L37ovH19ZGuURPb_JHPEKmzAnnUmgdFOI,5807
scipy/special/_precompute/wrightomega.py,sha256=hAKXz3Kk9L-5JOBA86U6-yC_tsF8gOz0p8Y1vmWcAzE,996
scipy/special/_precompute/zetac.py,sha256=cZMVkubEXtildiIM9p5ldrV1sEoy240MckKPO3JhM6A,618
scipy/special/_sf_error.py,sha256=hDbymj1eSiPjl828PR-Kidkcd8RiVDpJEB-fGA9ltHM,390
scipy/special/_specfun.cp38-win_amd64.dll.a,sha256=UQZ3D7_zFRqX2r2a42LOAL35YnF8fM7vDomk77tMIdQ,1548
scipy/special/_specfun.cp38-win_amd64.pyd,sha256=-by7Gd4F98vnD39fQ4GCBFqG06WH3eCzcBoRPTPLB1g,417792
scipy/special/_spfun_stats.py,sha256=uIOOQPBeg-JD_W4ADbybIjlYSjtsqI-4w5YxgbrYfnU,3913
scipy/special/_spherical_bessel.py,sha256=Xgn6yWFTDc4nqvQMFU9hlDmRm5PieOsbHkGvdebsQqw,10566
scipy/special/_test_internal.cp38-win_amd64.dll.a,sha256=QzZ59tssrIZ_Y2Qzu8cGucncl-AbcfdAZHijmm4knGs,1620
scipy/special/_test_internal.cp38-win_amd64.pyd,sha256=XQRXpi7K-cUX0AJtMlmWles0gTwgeR-Aw3zJB5DCjVw,194048
scipy/special/_test_internal.pyi,sha256=TRnWvsDkNYeIdXPvetaZVifB67-JSWzVJo8xZWI9yYo,373
scipy/special/_testutils.py,sha256=AAQDs9Do-XgcB4dPVd5yFCWL0C9HDve1R7afjIyUJsk,12290
scipy/special/_ufuncs.cp38-win_amd64.dll.a,sha256=9o0jxnywWnCh2QUBlFN4tM-_wZ26By2iWa65_nCrGi0,1540
scipy/special/_ufuncs.cp38-win_amd64.pyd,sha256=jtSrzpreWLobRnaQoQxUzkePKtgaDwGHrC4DUte1AYM,1752576
scipy/special/_ufuncs.pyi,sha256=8E-s20oDvInUcuLJ8TitcPWDM4OuAUPuB_W8TJEejhc,9329
scipy/special/_ufuncs.pyx,sha256=_hqdu9q7sAApzv0GOREV-WT-413bhtMscRf65koaeaY,877384
scipy/special/_ufuncs_cxx.cp38-win_amd64.dll.a,sha256=7gPLUi9x3yDK7znaGstjOa7hbDMDji0_uHw165lp3zA,1588
scipy/special/_ufuncs_cxx.cp38-win_amd64.pyd,sha256=lSzSVkGYsBQa0UvS3NiHArqbjCkBYsnZf8EBZgy-ouU,1354752
scipy/special/_ufuncs_cxx.pxd,sha256=PCZFJB6SjZbmVimrNUt4rP5H5H1iEjnpzWGiul2wWVk,1391
scipy/special/_ufuncs_cxx.pyx,sha256=E5Nn656t44FDpeZimQQWL2__mCCMLIRFOh01yx_G_nM,9415
scipy/special/_ufuncs_cxx_defs.h,sha256=2Y0jGOFcbM93dODOZEDQfh6bqq43NF9kUwKNryV1UmA,2052
scipy/special/_ufuncs_defs.h,sha256=G0Veycg-zfPvXR5tqHgSxHf9fkg7-dxnLyhkdFQOz80,11273
scipy/special/add_newdocs.py,sha256=uBu_FANz5Ljbpj_D6qt6caSBRhNE1ynR_eLOVFn14w8,667
scipy/special/basic.py,sha256=ciDsf1oN-UMMP_EuitkiYoHQLV3BTBJxG3Jr2kom12M,1993
scipy/special/cython_special.cp38-win_amd64.dll.a,sha256=Q494ohAYF90_tPrC9XIfzM1BQxDY8BkjKAtapMIscO8,1620
scipy/special/cython_special.cp38-win_amd64.pyd,sha256=Hw7Q81a4LQre-wN1Y4lRPzWXcymION1sjJp8ZtfkeME,2610688
scipy/special/cython_special.pxd,sha256=U20XqDqK3BqJV13YpHybZ3vzcX52R7uTWxVChGIulfI,14278
scipy/special/cython_special.pyi,sha256=sC6nE-1ffPYfFKVvopszCin-ilDqBWkDW2O6s0and2M,61
scipy/special/cython_special.pyx,sha256=LaxtqSW7CBafpo9XLGnWHNle5cLomUQ5IRmVRrboZ2Y,150528
scipy/special/orthogonal.py,sha256=LvwpYXqJl8Ks1s2oHQpQiRDfg20m5I10udE1RfaGQFk,2108
scipy/special/sf_error.py,sha256=DljWhSwCFY0NUbufWQ0VNCVlqZDpxR_qfLRezboMTac,820
scipy/special/specfun.py,sha256=kFR7qPScWwSHb_tT57o9rtapiTxnpN-xp1OqfnHysT8,1110
scipy/special/spfun_stats.py,sha256=faboFB2SOG333mwd3nSnBDQbHI89fa9om8QdvXUfB9s,795
scipy/special/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_basic.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_bdtr.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_boxcox.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_cdflib.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_cdft_asymptotic.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_cosine_distr.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_cython_special.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_data.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_dd.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_digamma.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_ellip_harm.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_erfinv.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_exponential_integrals.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_faddeeva.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_gamma.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_gammainc.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_hyp2f1.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_hypergeometric.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_kolmogorov.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_lambertw.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_log_softmax.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_loggamma.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_logit.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_logsumexp.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_mpmath.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_nan_inputs.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_ndtr.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_ndtri_exp.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_orthogonal.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_orthogonal_eval.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_owens_t.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_pcf.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_pdtr.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_powm1.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_precompute_expn_asy.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_precompute_gammainc.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_precompute_utils.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_round.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_sf_error.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_sici.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_spence.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_spfun_stats.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_sph_harm.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_spherical_bessel.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_trig.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_wright_bessel.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_wrightomega.cpython-38.pyc,,
scipy/special/tests/__pycache__/test_zeta.cpython-38.pyc,,
scipy/special/tests/data/boost.npz,sha256=ajLnBjdgPJNrKMMiK8ydh7Dbx9XDSE928iyvxs9aXA0,1270643
scipy/special/tests/data/gsl.npz,sha256=8oHuiW1OU8rYzfn2jv1ER_-SikJFilrLkDutNAg5-OE,51433
scipy/special/tests/data/local.npz,sha256=QXoYcTheO_idOFOTCoMT1wvCh21bj8SbhpR5qK6p4tI,203438
scipy/special/tests/test_basic.py,sha256=d7duPp8ro4MXRH5Ul48LrjCQLD_qyKw4sQLlFghbWhY,147842
scipy/special/tests/test_bdtr.py,sha256=ZPWchkB2hVjJ7MALAN9K4QWI8PeGSxcqduER3_myJRk,3343
scipy/special/tests/test_boxcox.py,sha256=K3BgEIIMADJLcnrzi9Nq7SWErOIiLQP36t6Bz6aoRMk,2778
scipy/special/tests/test_cdflib.py,sha256=44bVvbotJNdjCJEPw5OdxRv6nyioABrmD2qRx69n6PI,13589
scipy/special/tests/test_cdft_asymptotic.py,sha256=uF6yhrAGSPdlP1tdLDoR2-IBlkjFUeEo2hBAr7DTxJY,1478
scipy/special/tests/test_cosine_distr.py,sha256=if6Lu_kcBV63UbYlCehC2-mKqEG7VkMgC49Dgzs-rFk,2775
scipy/special/tests/test_cython_special.py,sha256=ldbK6dIeAhr0mXsZXUmb74NlajFKbE6aCNuOs_ea5mM,19046
scipy/special/tests/test_data.py,sha256=zk2NjzrM7dYfDA5uFWdT9lg-wXZH225vyN5dxWg13lE,29151
scipy/special/tests/test_dd.py,sha256=3tsB8dnU22c5CEG5l-NJBxDmra5iQGhvUVKn7POdqvs,2020
scipy/special/tests/test_digamma.py,sha256=WGB-E2XOPFtIZLVidWFiC2gCQbSMpOpOQyO_o2_WLr8,1436
scipy/special/tests/test_ellip_harm.py,sha256=qOVC4b0N6y7ubJNYa4K54W4R5oiHdWghdhy_rO5_gac,9918
scipy/special/tests/test_erfinv.py,sha256=JMvb2hWNQ63uPevk3kAL6KypAtOMJzg8n1G3Om9A15c,3148
scipy/special/tests/test_exponential_integrals.py,sha256=W-XN3wCnKqAc76u-p5iYbW-ptwi0C8Kt898KZlSgjhg,1943
scipy/special/tests/test_faddeeva.py,sha256=2BXe8JR2d_QBMvjX-bmHkP0TDzy4JgZhM6XvSlqO1HA,2653
scipy/special/tests/test_gamma.py,sha256=-u9wzNPSPsJTs_DJL6VZAXXIcIqJsxOeQy__MdRTGxY,270
scipy/special/tests/test_gammainc.py,sha256=Rrv_OnC7U6EtXSvrbGQpcPb7sccWVPIgIAzjs6DBIxM,3951
scipy/special/tests/test_hyp2f1.py,sha256=R6S0oy8R52b26uw3d0BJC0bZ-k3XSwAhG3AVVAnJWcE,80683
scipy/special/tests/test_hypergeometric.py,sha256=r74UINrqppB7WyVWhsX3cOkDeognCSWm__aeUn0CAaI,5738
scipy/special/tests/test_kolmogorov.py,sha256=wzCFKI0n3hUyPsc8-lybLx18UgEsOq7X5kl7hFkyyco,18819
scipy/special/tests/test_lambertw.py,sha256=yUKLzwXbuRiosc33zN0mqEiuSnxrfO4LOtwnH6mKNl4,4665
scipy/special/tests/test_log_softmax.py,sha256=0VhKfjYbv5-bV_d4tWyrG6OawlRjCUi_O8mRSgW0Mn8,3524
scipy/special/tests/test_loggamma.py,sha256=SdxYiItvixR4NabOKLyBHu-iEP7R52Rew8N8bZugH2s,2062
scipy/special/tests/test_logit.py,sha256=eJfxl4AYJF2VRFebHTw5NS7eF7Q_R82C42x3cou7uwE,5685
scipy/special/tests/test_logsumexp.py,sha256=89z07XS91GhkTVSQIIpRTVjdli9_8cCggHY0a5QenPg,6374
scipy/special/tests/test_mpmath.py,sha256=mUy8Hm6QQPb5RWBwvh0aqc76U-FJedDK9ekgZfD_6bE,77216
scipy/special/tests/test_nan_inputs.py,sha256=ruDmDHIO4xnNuVHuf3PlObuavbWEYEv8dlJ4-OnD3A0,1909
scipy/special/tests/test_ndtr.py,sha256=n_Y8wG9MYVmZOXPgJoiYjPGlUULdiPoDQFsuR6HzPWg,2757
scipy/special/tests/test_ndtri_exp.py,sha256=b4vFFkhdzIDWNgmKOEy5UjRCYtHsdkoaasRi_L9lzIM,3802
scipy/special/tests/test_orthogonal.py,sha256=szGsL3uAfyGdp2HJZ19csSAVjFFN3BQhUNuFS7ee1DI,32086
scipy/special/tests/test_orthogonal_eval.py,sha256=OB93dI8G9fukPlpssmzN2n1wPEhj1DQye4TIjMsODCI,9587
scipy/special/tests/test_owens_t.py,sha256=sotiVxRrjoQ4lYjbbBx88nQKHEHhNowfJ7KPdIM-vNQ,1845
scipy/special/tests/test_pcf.py,sha256=VTEqviYLsm0DBnuu1Bpyn6rEocf9sZGSyY12ufwxaYo,688
scipy/special/tests/test_pdtr.py,sha256=YpZQ7ssQ5z0yL2lKss4lDooRlz_3n-aY2q4a8aVXXB8,1332
scipy/special/tests/test_powm1.py,sha256=AG5D4ex4jCS97xN7RlyumVc09OUTuczNxS2IYwt9OAg,2341
scipy/special/tests/test_precompute_expn_asy.py,sha256=DFW5CiFXjHNUkWgZrehKUxPNTd09Cllp1nZ0_FU172g,607
scipy/special/tests/test_precompute_gammainc.py,sha256=pGve_JRcD-9pl9D4HiNlxJ86P7FxJjKiWzjUoaE77WI,4636
scipy/special/tests/test_precompute_utils.py,sha256=EWXMuVz5WDUQISPzh87i02u1im9TcLbDBxyk-BO6iZY,1201
scipy/special/tests/test_round.py,sha256=aySiXp52xcim56HDkpumtSnFYgRTGzZFQ2USLNmcECI,437
scipy/special/tests/test_sf_error.py,sha256=oS-LiXV8HnctZOEtsUC_rl-hGDksMcTL7i0gjOcjmgI,3643
scipy/special/tests/test_sici.py,sha256=REYntiqC0T4Pb-66ESBmjEmIoTJDbjYs786XNwUkzIw,1263
scipy/special/tests/test_spence.py,sha256=r4chXBi_nKK1uSfPse7owNhvcC93aBvcMBZf_8wTlF8,1131
scipy/special/tests/test_spfun_stats.py,sha256=wiPQbTQD1vpgk3M1tFkTm0x5FnPgMGdp-5wO1XVm-7Q,2059
scipy/special/tests/test_sph_harm.py,sha256=oZPB3IVaOXB4idFqEmIbxPt6MfslaGVOi4O0dRHz-AU,1153
scipy/special/tests/test_spherical_bessel.py,sha256=nTlVlx0IMpKmI9rKUBUJCxIhoXvyLnTdtLiEnds1iiY,14663
scipy/special/tests/test_trig.py,sha256=1_SHYdg-HNGQjEOr9QcDz2PwPWXnnPdZ1QEkuxXB5HQ,2163
scipy/special/tests/test_wright_bessel.py,sha256=t15XtWFFnN9YapbVZyyA88I32j8X14ADbUhZzbMRbLU,4270
scipy/special/tests/test_wrightomega.py,sha256=BPZRTv0qDhpXu1WRTlf6HG0xnzWB5P-v8EPoDFvmJ_c,3667
scipy/special/tests/test_zeta.py,sha256=FbhrcRuDXJ_ZrVMRdUvICiaGMp7DM5CeWcOwYSKhXvk,1416
scipy/stats/__init__.py,sha256=ryWfyFtO1HbOsYv1YSOWdYIDG7ZuQikQxsFtlVFkC2o,14217
scipy/stats/__pycache__/__init__.cpython-38.pyc,,
scipy/stats/__pycache__/_axis_nan_policy.cpython-38.pyc,,
scipy/stats/__pycache__/_binned_statistic.cpython-38.pyc,,
scipy/stats/__pycache__/_binomtest.cpython-38.pyc,,
scipy/stats/__pycache__/_common.cpython-38.pyc,,
scipy/stats/__pycache__/_constants.cpython-38.pyc,,
scipy/stats/__pycache__/_continuous_distns.cpython-38.pyc,,
scipy/stats/__pycache__/_covariance.cpython-38.pyc,,
scipy/stats/__pycache__/_crosstab.cpython-38.pyc,,
scipy/stats/__pycache__/_discrete_distns.cpython-38.pyc,,
scipy/stats/__pycache__/_distn_infrastructure.cpython-38.pyc,,
scipy/stats/__pycache__/_distr_params.cpython-38.pyc,,
scipy/stats/__pycache__/_entropy.cpython-38.pyc,,
scipy/stats/__pycache__/_fit.cpython-38.pyc,,
scipy/stats/__pycache__/_generate_pyx.cpython-38.pyc,,
scipy/stats/__pycache__/_hypotests.cpython-38.pyc,,
scipy/stats/__pycache__/_kde.cpython-38.pyc,,
scipy/stats/__pycache__/_ksstats.cpython-38.pyc,,
scipy/stats/__pycache__/_mannwhitneyu.cpython-38.pyc,,
scipy/stats/__pycache__/_morestats.cpython-38.pyc,,
scipy/stats/__pycache__/_mstats_basic.cpython-38.pyc,,
scipy/stats/__pycache__/_mstats_extras.cpython-38.pyc,,
scipy/stats/__pycache__/_multivariate.cpython-38.pyc,,
scipy/stats/__pycache__/_odds_ratio.cpython-38.pyc,,
scipy/stats/__pycache__/_page_trend_test.cpython-38.pyc,,
scipy/stats/__pycache__/_qmc.cpython-38.pyc,,
scipy/stats/__pycache__/_relative_risk.cpython-38.pyc,,
scipy/stats/__pycache__/_resampling.cpython-38.pyc,,
scipy/stats/__pycache__/_result_classes.cpython-38.pyc,,
scipy/stats/__pycache__/_rvs_sampling.cpython-38.pyc,,
scipy/stats/__pycache__/_stats_mstats_common.cpython-38.pyc,,
scipy/stats/__pycache__/_stats_py.cpython-38.pyc,,
scipy/stats/__pycache__/_tukeylambda_stats.cpython-38.pyc,,
scipy/stats/__pycache__/_variation.cpython-38.pyc,,
scipy/stats/__pycache__/_warnings_errors.cpython-38.pyc,,
scipy/stats/__pycache__/biasedurn.cpython-38.pyc,,
scipy/stats/__pycache__/contingency.cpython-38.pyc,,
scipy/stats/__pycache__/distributions.cpython-38.pyc,,
scipy/stats/__pycache__/kde.cpython-38.pyc,,
scipy/stats/__pycache__/morestats.cpython-38.pyc,,
scipy/stats/__pycache__/mstats.cpython-38.pyc,,
scipy/stats/__pycache__/mstats_basic.cpython-38.pyc,,
scipy/stats/__pycache__/mstats_extras.cpython-38.pyc,,
scipy/stats/__pycache__/mvn.cpython-38.pyc,,
scipy/stats/__pycache__/qmc.cpython-38.pyc,,
scipy/stats/__pycache__/sampling.cpython-38.pyc,,
scipy/stats/__pycache__/statlib.cpython-38.pyc,,
scipy/stats/__pycache__/stats.cpython-38.pyc,,
scipy/stats/_axis_nan_policy.py,sha256=qqid0aIltIevnfzjGOncfemgIebIO_3-cOkndNc1YuE,27470
scipy/stats/_biasedurn.cp38-win_amd64.dll.a,sha256=mDjjDJzrn_fOXvSjulYHR7pn2pnyA4Sd3218ql-GFoo,1572
scipy/stats/_biasedurn.cp38-win_amd64.pyd,sha256=PIhTCW9LwT7OF7_KkrWzGmds8KJy8bNndGlsZpRpzxY,338432
scipy/stats/_biasedurn.pxd,sha256=OHa5weTjmvbIdkvmgDnD021p44bVaS5mNvDIx562SLI,1073
scipy/stats/_binned_statistic.py,sha256=nfM_7K7YOdcDLb-1v5rIMtwiPsgWTRp2h29EKxc3Uyo,33505
scipy/stats/_binomtest.py,sha256=hr6r2GAaKfXQmhtk2kujbPEUmM_CPvKN5i_18LZLksM,13418
scipy/stats/_boost/__init__.py,sha256=TOjKqfv_jPF6l8By1mdPbQFrV_H-ZMLRCUFZ7mNfL8A,1812
scipy/stats/_boost/__pycache__/__init__.cpython-38.pyc,,
scipy/stats/_boost/beta_ufunc.cp38-win_amd64.dll.a,sha256=qk-Zalk4eoYHul_lfoyaDq1dn8kTUT-Q1lCXOwxO6ZM,1572
scipy/stats/_boost/beta_ufunc.cp38-win_amd64.pyd,sha256=UCXYUNj612UiZTfChjdovSAnS4IIHdQvrYNIqKD4iGw,1153024
scipy/stats/_boost/binom_ufunc.cp38-win_amd64.dll.a,sha256=RJWQEZ11xEZBWVf0NX6rj2xEs9Su6MKd3f0j7BaJGsA,1588
scipy/stats/_boost/binom_ufunc.cp38-win_amd64.pyd,sha256=imbv2vkt2El87kwHahFevCFtRJRlLlCvtn1X6crNp3E,1140736
scipy/stats/_boost/hypergeom_ufunc.cp38-win_amd64.dll.a,sha256=B501DChrLbC5-tlzrRM5yMv-DeoWqVaVELrf8jESr2M,1636
scipy/stats/_boost/hypergeom_ufunc.cp38-win_amd64.pyd,sha256=CeujTBof98g8qG9soP1bO_2DOeXVXNeFay4VnD8fT1E,1108480
scipy/stats/_boost/invgauss_ufunc.cp38-win_amd64.dll.a,sha256=aOMu-cZ25ecFtLB63X5xTJiNFJPARvj80GwFBhKj8_E,1620
scipy/stats/_boost/invgauss_ufunc.cp38-win_amd64.pyd,sha256=E1nvOZkGwUueOjEbzLkElGatmv_HAsxyt0QqjjKRAXM,1136640
scipy/stats/_boost/nbinom_ufunc.cp38-win_amd64.dll.a,sha256=3HZe7azlJJ0VFk_EJ8mNelxHWo3w4y8t-zp_80iBXjo,1596
scipy/stats/_boost/nbinom_ufunc.cp38-win_amd64.pyd,sha256=jmNrd_C5iDc-kp8K9iMWJTzn1aslW4dq0uPuwMmxILA,1143808
scipy/stats/_boost/ncf_ufunc.cp38-win_amd64.dll.a,sha256=fT8TFcwQkiKNp6h9mgOJ3l4CO0YbQjLLnHt2vot5gKg,1564
scipy/stats/_boost/ncf_ufunc.cp38-win_amd64.pyd,sha256=NEP1oUfPoVnQzUOgJpjDjPINtOBIbGze2eaxVnRLA7Q,1129472
scipy/stats/_boost/nct_ufunc.cp38-win_amd64.dll.a,sha256=6Lp5BxOg5pQA64-yvzGDUV_UPWMIoJka6AQYto8-XUQ,1564
scipy/stats/_boost/nct_ufunc.cp38-win_amd64.pyd,sha256=F_EKaNJDSGb7o5IfTrAiBJirRfhzEkHzv3HlEtXprIA,1163776
scipy/stats/_boost/ncx2_ufunc.cp38-win_amd64.dll.a,sha256=wqSsdt2zk-s0KjhcpPWkGdPeBGadd99Idy7snDf1wk8,1572
scipy/stats/_boost/ncx2_ufunc.cp38-win_amd64.pyd,sha256=6Gxu2YwuRlMYdC8OdJOVAK3lofXzDkv1qViVwJdrCcw,1143296
scipy/stats/_boost/skewnorm_ufunc.cp38-win_amd64.dll.a,sha256=zLzGC3NWCpTQ607iDEKDusB44f6MX_agGi0m50SSc58,1620
scipy/stats/_boost/skewnorm_ufunc.cp38-win_amd64.pyd,sha256=7m7VwzkrENTELNk1VQQ526SzyUKoJAvWhJbwkYZJQ6I,1030144
scipy/stats/_common.py,sha256=ByMLLDPE_S87hSK1O-vZ4mM0Ffp_5gSi_8-Z83qwaew,179
scipy/stats/_constants.py,sha256=yk6ZMdCZOKsTR1_BPqACqeBC1mYDOqIpNGSQo1-jAcQ,827
scipy/stats/_continuous_distns.py,sha256=aoeZpesiMsGG9fIzFnPar5T9X-HrXEVw2-mL--aPD2I,333201
scipy/stats/_covariance.py,sha256=bQCwuyc2GnnBw0Qshy81HIMxsvR_8l4jWLoBsxqI5tE,23104
scipy/stats/_crosstab.py,sha256=EzyNTZAsaJPrcNPxkXjWmiattNX3plLJASAFrkR3XdY,7557
scipy/stats/_discrete_distns.py,sha256=qprzJOAIyFRSOQ-yztzMFrQyatNi7wto3goew21J1IM,56513
scipy/stats/_distn_infrastructure.py,sha256=gXyLYJwoMfHJU_V2sl9tb8dKiZbdyEW5klQQqXaFod8,153134
scipy/stats/_distr_params.py,sha256=T_bIZBYcudn2GkvVRCTlmmqWAS7NIKkOBgjZqQTeuE8,8776
scipy/stats/_entropy.py,sha256=3IzoLN0AGf-aKTbz4aDTI2611AYGufsml6z3mmyvmkY,14761
scipy/stats/_fit.py,sha256=mTtbL2J6d3D4j5lWkhUd9yaliPeusACP3TAqfA3BRTo,58276
scipy/stats/_generate_pyx.py,sha256=aIH4saZbXgrLAdxfVuhgQ2oLNW4auNzG559Xzi53M9U,2759
scipy/stats/_hypotests.py,sha256=kBiDVB5XJVISDI74xNEMYuhImj61o6WP17-lCQDYtNE,80547
scipy/stats/_kde.py,sha256=gOtvClMcsdzIgk9Uf24rE0tObf6jDpiD3A56DmUj6JE,25709
scipy/stats/_ksstats.py,sha256=V093fM3H2qES7NsRk_iySjmgLqIaogHBpDTqxpGdAgI,20682
scipy/stats/_levy_stable/__init__.py,sha256=PJebUoqaUcly1Tni5iQMiEh3tRhGuu2MeI2w4KEcQdo,45057
scipy/stats/_levy_stable/__pycache__/__init__.cpython-38.pyc,,
scipy/stats/_levy_stable/levyst.cp38-win_amd64.dll.a,sha256=JndKXX4t0pN2W8Ir7DtTH5ve9lR0I1L-iNK5yGeeSfE,1524
scipy/stats/_levy_stable/levyst.cp38-win_amd64.pyd,sha256=n6sjMoC6GF0-Xp8k2u7tH8eX3ALp3xqdo-e02nZDV80,38912
scipy/stats/_mannwhitneyu.py,sha256=BBOI-uu1q6xXehGu_d7OQVE_VI6JtpwS-Y1frTq370I,19448
scipy/stats/_morestats.py,sha256=Ng_t_GDusR9ISLwusxgqAEetKSbgxIVMz008Xy1E3zY,154049
scipy/stats/_mstats_basic.py,sha256=oihxoxKSg4XgX-CosZNMAeVIPe2-90VbuWhgcduoEzI,121428
scipy/stats/_mstats_extras.py,sha256=JyKLfV6-cGeLglJkuOEOzN41Zc_6Nchgs9Z5cRzXqQU,16110
scipy/stats/_multivariate.py,sha256=h-4sW-F6KWk4PBJVV43BtcfiK65E4Tj0NSJmdGEVqD4,195022
scipy/stats/_mvn.cp38-win_amd64.dll.a,sha256=fWqttPgPDsScY1P1DNebIzDlS3I1SPGvyfKQcwk-vI0,1500
scipy/stats/_mvn.cp38-win_amd64.pyd,sha256=FDvQgJAvLGUHjvsgSzlubg8cR2Rkd_Uenr_-5sfmq5Y,103424
scipy/stats/_odds_ratio.py,sha256=x2wNEgttGOfK55FiDKOb-TunnX5UtO4yQL3G6tRICZs,17360
scipy/stats/_page_trend_test.py,sha256=p01wmcnsMTIBI_xzVs2ylqGZgal0wx1GyPWEqtiTnBo,19518
scipy/stats/_qmc.py,sha256=kSDrllOp4JQCcTmDm7D68_mwTlm2yUjtrj3JGzPKkWQ,93706
scipy/stats/_qmc_cy.cp38-win_amd64.dll.a,sha256=h3a9CNzGxMnMl1OrMuOjh7j5YdZtnyZafhNo7bYz7No,1540
scipy/stats/_qmc_cy.cp38-win_amd64.pyd,sha256=u91d_3LUtPrV6ySoscmN3tec_dBZZLCAruzqeIkjesU,374784
scipy/stats/_qmc_cy.pyi,sha256=l2173UiDribq_G9S47QozpcKo8nO9D3rojwKLEd8m8k,1188
scipy/stats/_rcont/__init__.py,sha256=PJ9kYXBgXVx2DjzhzWGggomxa01VT6Xo1XhjYU6Y_aU,113
scipy/stats/_rcont/__pycache__/__init__.cpython-38.pyc,,
scipy/stats/_rcont/rcont.cp38-win_amd64.dll.a,sha256=Qn2PE0n1FpZyfNoHZl3E1x-jgsSieonlPt_kwbFzc6Q,1516
scipy/stats/_rcont/rcont.cp38-win_amd64.pyd,sha256=weE00ybPaqEe8R_kn7w8FiyyuFBG2J2uoYGcv_zyUHA,220672
scipy/stats/_relative_risk.py,sha256=cRxbVkU7VFzIrUzYtHBqECyUQ3hMA5x9l2_4HECiEIY,9836
scipy/stats/_resampling.py,sha256=1jEOa1daPjT8wARwIYLgwtu9LKAACjVm4tbMAUsD0LI,71438
scipy/stats/_result_classes.py,sha256=R5ogWVgl-2Xjw7x9muX7MF8FRmDGgmeryh26iosJr6w,877
scipy/stats/_rvs_sampling.py,sha256=_DwLfldc18Za3CyYtrU3PxMshRb7Hbl6AutCY3--7fA,7400
scipy/stats/_sobol.cp38-win_amd64.dll.a,sha256=Uzitt5cf1jET86-8001hbmDeGSNxhLamwH6icwDvQhs,1524
scipy/stats/_sobol.cp38-win_amd64.pyd,sha256=SDHb1lL8kvRwXn_HKUh2WSDUK3HTDb6-ZAMjb5RqEVw,277504
scipy/stats/_sobol.pyi,sha256=tskim056aVi6WIymUMEMdWHaLk8QMqWa2kCAbF2ZTPo,1025
scipy/stats/_sobol_direction_numbers.npz,sha256=SFmTEUfULORluGBcsnf5V9mLg50DGU_fBleTV5BtGTs,589334
scipy/stats/_statlib.cp38-win_amd64.dll.a,sha256=yPbkqhc08rPIAfMcec3E8g6V0SLioQGeL2Yk6ATs5C8,1548
scipy/stats/_statlib.cp38-win_amd64.pyd,sha256=RP2jQVQHwCTK0at1L6AMLOM8YeQelxAGMD9DgodmnMI,74240
scipy/stats/_stats.cp38-win_amd64.dll.a,sha256=mD6U9ig6oNNptN2Uz0POUvW-3Pl8Nhn2cLhmJe7SLv8,1524
scipy/stats/_stats.cp38-win_amd64.pyd,sha256=JIYv29oZtQ6VnpcJ2FPQUIELbr3HungVeKyrI4GQlUo,633344
scipy/stats/_stats.pxd,sha256=RF9rGf3Uv1RaQX0rO2Gdhc8klx8UJvrI-7HB3H9hAuk,672
scipy/stats/_stats_mstats_common.py,sha256=4OcGe57AqqGHx0p87TV-aQwK2s8ptAuWGo6NGTE3uIk,19153
scipy/stats/_stats_py.py,sha256=D33Y7hNVXFw6vfIXsgvbNpT_6ot0mCb3ampzWPH5A0Y,365238
scipy/stats/_stats_pythran.cp38-win_amd64.dll.a,sha256=aroXfmCJ1GltZM9FMMHFDEpDoFEuAT6cKyw7uOdq1-o,1620
scipy/stats/_stats_pythran.cp38-win_amd64.pyd,sha256=6VOBPPDfQSzgHjo5QrLCGjCHZQ0PXGUG0jWgLshfvKo,1068032
scipy/stats/_tukeylambda_stats.py,sha256=Y7rMBC8TRZMahuJ1yas5MXOBDLzuZNG-yzQylrHf7SQ,7068
scipy/stats/_unuran/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/_unuran/__pycache__/__init__.cpython-38.pyc,,
scipy/stats/_unuran/unuran.pxd,sha256=5X4UKcLgRHfhiWNY0mLjreJMZL3xUsDyj654VexGo4g,44666
scipy/stats/_unuran/unuran_wrapper.cp38-win_amd64.dll.a,sha256=Y9tcqnRXP67dPadSZvTOxkMHQjhcxY26CCeSeFbKMPI,1620
scipy/stats/_unuran/unuran_wrapper.cp38-win_amd64.pyd,sha256=BokVIt4jfzFCwvDZweC5aLqCqPKZckM63e2PteF0wN4,1362944
scipy/stats/_unuran/unuran_wrapper.pyi,sha256=R3hvm1LeO_5sWh_ZWNbwm7qsLUgMt8uPbUaFwxn8Ims,5779
scipy/stats/_variation.py,sha256=DA2ETKrjFEyaHx1BFHbyFZ4-0bdql8WUKR9OepTgvdU,8553
scipy/stats/_warnings_errors.py,sha256=cP_MgHswNTSh-zDSBpxnTvuVzl3_AspUcaFIuGRniGs,1233
scipy/stats/biasedurn.py,sha256=bXHhZqM472v0-lqj0UMBof-qtjruOLpstdFIAPcBOEM,719
scipy/stats/contingency.py,sha256=3ZXN-G5yge0qugjByAwYFHmJR15V5UqfjbWor5KnOkU,14627
scipy/stats/distributions.py,sha256=Fk6GdmP5MWXdBDx8uDk3ApNeF6jVZr_GYzd6WE5IoR8,827
scipy/stats/kde.py,sha256=5EhrHL0zZ7FLyFF4VBmYCc1M-wa7VNfD87xVAN_gzEs,954
scipy/stats/morestats.py,sha256=jqgjNz70dAjfgp9tc7LJryQoEBz040fvTo0ldpUHflc,1662
scipy/stats/mstats.py,sha256=JdqfzHXLEmI5xiiBcbM-Wdb5Q_yH3njqruTRVO_2dL0,2397
scipy/stats/mstats_basic.py,sha256=1fbmNeUjlO3t6peD5zcTejM7tFNB8gP2-u0dnOi_SGU,2181
scipy/stats/mstats_extras.py,sha256=KQV_AcD1IysR_k72bZLum4oE1Jq2voSEuSY4huHqJHc,1035
scipy/stats/mvn.py,sha256=Yw91PcX47SDmlwSsDV-SoibWYT6RwbjfX00F5C2hZVQ,815
scipy/stats/qmc.py,sha256=HiJooF7fJC4dfjR8D8_pD8mFLCHQtR8bHBM_-zZ8OXA,11883
scipy/stats/sampling.py,sha256=sOll8H8kHb5LW8lz1gJffTxfyv8mjkeTh8zseJsuXoc,1247
scipy/stats/statlib.py,sha256=R2uxcmTgcaJ9eK733dsvQe19a-MiJ0UXH_hy8BjR0CM,806
scipy/stats/stats.py,sha256=JRZiiDUgp8rsrhzirSFrlzckGkFEHj1JJfCIddRZ8YA,2764
scipy/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/tests/__pycache__/__init__.cpython-38.pyc,,
scipy/stats/tests/__pycache__/common_tests.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_axis_nan_policy.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_binned_statistic.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_boost_ufuncs.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_contingency.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_continuous_basic.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_crosstab.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_discrete_basic.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_discrete_distns.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_distributions.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_entropy.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_fit.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_hypotests.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_kdeoth.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_morestats.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_mstats_basic.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_mstats_extras.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_multivariate.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_odds_ratio.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_qmc.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_rank.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_relative_risk.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_resampling.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_sampling.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_stats.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_tukeylambda_stats.cpython-38.pyc,,
scipy/stats/tests/__pycache__/test_variation.cpython-38.pyc,,
scipy/stats/tests/common_tests.py,sha256=QGn_3maU1X48_8KfEqG8gT82f6rFVrKhSeWF6x8VZcc,15830
scipy/stats/tests/data/__pycache__/fisher_exact_results_from_r.cpython-38.pyc,,
scipy/stats/tests/data/fisher_exact_results_from_r.py,sha256=AqKIctwYj-6FUc6XhwZIfrPCEw073oMKhSWrDhuz00I,27956
scipy/stats/tests/data/levy_stable/stable-Z1-cdf-sample-data.npy,sha256=zxjB8tZaIyvyxxISgt8xvyqL6Cevr8TtgQ7TdFfuiYo,183728
scipy/stats/tests/data/levy_stable/stable-Z1-pdf-sample-data.npy,sha256=_umVErq0zMZWm0e5JOSwNOHNurViT6_H4SBki9X3oSg,183688
scipy/stats/tests/data/levy_stable/stable-loc-scale-sample-data.npy,sha256=88cZ7dVDH7nnuey20Z48p6kJUpi9GfImaFsPykDwwHM,9328
scipy/stats/tests/data/nist_anova/AtmWtAg.dat,sha256=WhCDyQYtOWbyRL_6N2_JsDMkSr-GoNDdAWrznNcfuKc,3171
scipy/stats/tests/data/nist_anova/SiRstv.dat,sha256=Wk8C1_JW4Yww3K0bgeAmC7gQtawRP2EH4G3uFo0HZj4,2032
scipy/stats/tests/data/nist_anova/SmLs01.dat,sha256=rSjKwNT7ef_qJSK1U0kIO96hDxKm-MRs-6vMzbTWuJY,6304
scipy/stats/tests/data/nist_anova/SmLs02.dat,sha256=cI4y6vNnaavnUz59zRttxnCRtg35ON03xPDmVmDvews,48430
scipy/stats/tests/data/nist_anova/SmLs03.dat,sha256=4YQE0GKLeGcFgLIwo6O2zZl9lZ0A-Soog6TalCsZN3o,469635
scipy/stats/tests/data/nist_anova/SmLs04.dat,sha256=EwjrFYHyjObJ3kypUg_Mcp-RbDTx-d7hD7bmWB5bTOc,7064
scipy/stats/tests/data/nist_anova/SmLs05.dat,sha256=exMz5uVs7XQUUJbfd0z7H2MFITyyj5qCIsQYyoXQwpY,55668
scipy/stats/tests/data/nist_anova/SmLs06.dat,sha256=DA8Jot8F8unbUySmqu7YVkdlHodOn1w4krjyn8SiAPc,541674
scipy/stats/tests/data/nist_anova/SmLs07.dat,sha256=_ZXFEKqLPOf2fd0yhGzOrX2auuRBeQM2xvsPywrjVY8,7630
scipy/stats/tests/data/nist_anova/SmLs08.dat,sha256=h9043__rZdOdvj6LpzvRZkT8OWwarQP8ApUiXzumrvk,61097
scipy/stats/tests/data/nist_anova/SmLs09.dat,sha256=9GiMWg1WkPyG-ifgxaJl7vCyRkHnSvJ6py2WFQhN0T8,595702
scipy/stats/tests/data/nist_linregress/Norris.dat,sha256=WraQbGipxLLrFmHAzX0qCfWkaabU4FtraR8ZD197lE8,2688
scipy/stats/tests/data/studentized_range_mpmath_ref.json,sha256=ZIARw6MmSmQkPpmLKGurnF_WxcZqQxUJmgoVsooTJIU,30737
scipy/stats/tests/test_axis_nan_policy.py,sha256=fzyy5ToTOvOYgsZaocgIycE9HLA2Omkl08pCs43ZwWs,45509
scipy/stats/tests/test_binned_statistic.py,sha256=v04jJ8b-uYINTcJfUeAwJ3TrZkFrj_9SzBJWe_5BUZA,19386
scipy/stats/tests/test_boost_ufuncs.py,sha256=dThYkA5k43fLEksTs4kNsBYaSves85SrFL_QDLHHjrA,1656
scipy/stats/tests/test_contingency.py,sha256=IXGuId-yDBKkt3aYl-Qj16Ojl_hFKQ0NEkLjfFCzlZ8,7947
scipy/stats/tests/test_continuous_basic.py,sha256=pH7APjPl62uLwhXZqoJ1j6f4TKYjQ5DWCX69RAIIl3w,42520
scipy/stats/tests/test_crosstab.py,sha256=8NEB6tQ-kVkPYhGvW_nH0WQbWzTruHGWzlrZdTyXPcc,3997
scipy/stats/tests/test_discrete_basic.py,sha256=cJgG9Cv8SEyFSeBh4l4IhZ9ZRjwVltprz_AfnUdu2YQ,20642
scipy/stats/tests/test_discrete_distns.py,sha256=phPPbqoRZOdbRSb0qQZVjUxQhCsaZNlvDQFyrwh5rJo,20632
scipy/stats/tests/test_distributions.py,sha256=hwZdgVF-y7D0hvmjhlZy46D0J7G2LhqRgL1K61pktCU,307007
scipy/stats/tests/test_entropy.py,sha256=mKRYaCaUcmy9mN2DKrQembgE9svtrl_oiA9O2q_o4Do,11565
scipy/stats/tests/test_fit.py,sha256=fyAAdlYZYNDTqXDzunT73Xq9w2b4xt20MB0Tw4W9CTc,38082
scipy/stats/tests/test_hypotests.py,sha256=vW5bWlCROIfxRiagDIMAa8ziAOK5wukRSw9wYT6A1yc,74779
scipy/stats/tests/test_kdeoth.py,sha256=bdIoz6mVueQ7r3x0bnnd4C9jMwtmZbBNHbBVt1fztZ4,20935
scipy/stats/tests/test_morestats.py,sha256=K8FO5lsjQSrZ9znbGb2Z-DAn34e6eGHvdYCtMTrzAyc,115860
scipy/stats/tests/test_mstats_basic.py,sha256=SCVr_SsuRhxctR4uqozXdGx2s3DMgkOQOF8-IprsTIE,85116
scipy/stats/tests/test_mstats_extras.py,sha256=DMKOSBLeBp0eOueKzvLSrV1y4EXl24w_ZhdOwNdRhys,6216
scipy/stats/tests/test_multivariate.py,sha256=UvW3uXIIujSOtVqyq2j9aGx4cLWErUH-vSDy9QxhGZw,115424
scipy/stats/tests/test_odds_ratio.py,sha256=J5A_AiTlmnMM4ccKOl2cNrv9oBBRFyI5krfJB1F0GMo,6852
scipy/stats/tests/test_qmc.py,sha256=HX-qUu_WgvhgSWPbqstVOz1bs7P6XSmCW5Di4F79m5k,52499
scipy/stats/tests/test_rank.py,sha256=AjkWrCN4GyNtDpHp-GAIOF-ADzNSyFwM-f9PYZrvUHI,11593
scipy/stats/tests/test_relative_risk.py,sha256=WN47K2RqLC3aJXimqeio5TZQhr5Ra6jOjt22aQzousk,3743
scipy/stats/tests/test_resampling.py,sha256=5edF7dluPLS9q47cqkOKt-tKGnYyADsZ0cMUhRpTTDg,68823
scipy/stats/tests/test_sampling.py,sha256=MRW_C3oYQuMyLTRlFpRAy4igRTtzxuB5MYkCiBPCVU4,52114
scipy/stats/tests/test_stats.py,sha256=ZYpyNofhs_FDAnfk1Fa8mKYPR48U4pxGdvC94ro8lZ0,345950
scipy/stats/tests/test_tukeylambda_stats.py,sha256=8L85ZcUmYMIaFJrSlhcdTpUkWRiQ414wxQYUEbxBRnw,3318
scipy/stats/tests/test_variation.py,sha256=enueq0viYlJAV_YBD9DPlmGeHQ27Jngdp2WapAbkpiE,6403
scipy/version.py,sha256=YLoitFDX7xeL0MS7LEbFGiVFcQh3lgKPw0CQUnZVrug,276
