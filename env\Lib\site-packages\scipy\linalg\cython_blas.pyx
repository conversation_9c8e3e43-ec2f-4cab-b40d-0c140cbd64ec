# This file was generated by _generate_pyx.py.
# Do not edit this file directly.

# cython: boundscheck = False
# cython: wraparound = False
# cython: cdivision = True

"""
BLAS Functions for Cython
=========================

Usable from Cython via::

    cimport scipy.linalg.cython_blas

These wrappers do not check for alignment of arrays.
Alignment should be checked before these wrappers are used.

Raw function pointers (Fortran-style pointer arguments):

- caxpy
- ccopy
- cdotc
- cdotu
- cgbmv
- cgemm
- cgemv
- cgerc
- cgeru
- chbmv
- chemm
- chemv
- cher
- cher2
- cher2k
- cherk
- chpmv
- chpr
- chpr2
- crotg
- cscal
- csrot
- csscal
- cswap
- csymm
- csyr2k
- csyrk
- ctbmv
- ctbsv
- ctpmv
- ctpsv
- ctrmm
- ctrmv
- ctrsm
- ctrsv
- dasum
- daxpy
- dcabs1
- dcopy
- ddot
- dgbmv
- dgemm
- dgemv
- dger
- dnrm2
- drot
- drotg
- drotm
- drotmg
- dsbmv
- dscal
- dsdot
- dspmv
- dspr
- dspr2
- dswap
- dsymm
- dsymv
- dsyr
- dsyr2
- dsyr2k
- dsyrk
- dtbmv
- dtbsv
- dtpmv
- dtpsv
- dtrmm
- dtrmv
- dtrsm
- dtrsv
- dzasum
- dznrm2
- icamax
- idamax
- isamax
- izamax
- lsame
- sasum
- saxpy
- scasum
- scnrm2
- scopy
- sdot
- sdsdot
- sgbmv
- sgemm
- sgemv
- sger
- snrm2
- srot
- srotg
- srotm
- srotmg
- ssbmv
- sscal
- sspmv
- sspr
- sspr2
- sswap
- ssymm
- ssymv
- ssyr
- ssyr2
- ssyr2k
- ssyrk
- stbmv
- stbsv
- stpmv
- stpsv
- strmm
- strmv
- strsm
- strsv
- zaxpy
- zcopy
- zdotc
- zdotu
- zdrot
- zdscal
- zgbmv
- zgemm
- zgemv
- zgerc
- zgeru
- zhbmv
- zhemm
- zhemv
- zher
- zher2
- zher2k
- zherk
- zhpmv
- zhpr
- zhpr2
- zrotg
- zscal
- zswap
- zsymm
- zsyr2k
- zsyrk
- ztbmv
- ztbsv
- ztpmv
- ztpsv
- ztrmm
- ztrmv
- ztrsm
- ztrsv


"""

# Within SciPy, these wrappers can be used via relative or absolute cimport.
# Examples:
# from ..linalg cimport cython_blas
# from scipy.linalg cimport cython_blas
# cimport scipy.linalg.cython_blas as cython_blas
# cimport ..linalg.cython_blas as cython_blas

# Within SciPy, if BLAS functions are needed in C/C++/Fortran,
# these wrappers should not be used.
# The original libraries should be linked directly.

cdef extern from "fortran_defs.h":
    pass

from numpy cimport npy_complex64, npy_complex128


cdef extern from "_blas_subroutines.h":
    void _fortran_cdotc "F_FUNC(cdotcwrp, CDOTCWRP)"(c *out, int *n, npy_complex64 *cx, int *incx, npy_complex64 *cy, int *incy) nogil
cdef c cdotc(int *n, c *cx, int *incx, c *cy, int *incy) nogil:
    cdef c out
    _fortran_cdotc(&out, n, <npy_complex64*>cx, incx, <npy_complex64*>cy, incy)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_cdotu "F_FUNC(cdotuwrp, CDOTUWRP)"(c *out, int *n, npy_complex64 *cx, int *incx, npy_complex64 *cy, int *incy) nogil
cdef c cdotu(int *n, c *cx, int *incx, c *cy, int *incy) nogil:
    cdef c out
    _fortran_cdotu(&out, n, <npy_complex64*>cx, incx, <npy_complex64*>cy, incy)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_dasum "F_FUNC(dasumwrp, DASUMWRP)"(d *out, int *n, d *dx, int *incx) nogil
cdef d dasum(int *n, d *dx, int *incx) nogil:
    cdef d out
    _fortran_dasum(&out, n, dx, incx)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_dcabs1 "F_FUNC(dcabs1wrp, DCABS1WRP)"(d *out, npy_complex128 *z) nogil
cdef d dcabs1(z *z) nogil:
    cdef d out
    _fortran_dcabs1(&out, <npy_complex128*>z)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_ddot "F_FUNC(ddotwrp, DDOTWRP)"(d *out, int *n, d *dx, int *incx, d *dy, int *incy) nogil
cdef d ddot(int *n, d *dx, int *incx, d *dy, int *incy) nogil:
    cdef d out
    _fortran_ddot(&out, n, dx, incx, dy, incy)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_dnrm2 "F_FUNC(dnrm2wrp, DNRM2WRP)"(d *out, int *n, d *x, int *incx) nogil
cdef d dnrm2(int *n, d *x, int *incx) nogil:
    cdef d out
    _fortran_dnrm2(&out, n, x, incx)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_dsdot "F_FUNC(dsdotwrp, DSDOTWRP)"(d *out, int *n, s *sx, int *incx, s *sy, int *incy) nogil
cdef d dsdot(int *n, s *sx, int *incx, s *sy, int *incy) nogil:
    cdef d out
    _fortran_dsdot(&out, n, sx, incx, sy, incy)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_dzasum "F_FUNC(dzasumwrp, DZASUMWRP)"(d *out, int *n, npy_complex128 *zx, int *incx) nogil
cdef d dzasum(int *n, z *zx, int *incx) nogil:
    cdef d out
    _fortran_dzasum(&out, n, <npy_complex128*>zx, incx)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_dznrm2 "F_FUNC(dznrm2wrp, DZNRM2WRP)"(d *out, int *n, npy_complex128 *x, int *incx) nogil
cdef d dznrm2(int *n, z *x, int *incx) nogil:
    cdef d out
    _fortran_dznrm2(&out, n, <npy_complex128*>x, incx)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_icamax "F_FUNC(icamaxwrp, ICAMAXWRP)"(int *out, int *n, npy_complex64 *cx, int *incx) nogil
cdef int icamax(int *n, c *cx, int *incx) nogil:
    cdef int out
    _fortran_icamax(&out, n, <npy_complex64*>cx, incx)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_idamax "F_FUNC(idamaxwrp, IDAMAXWRP)"(int *out, int *n, d *dx, int *incx) nogil
cdef int idamax(int *n, d *dx, int *incx) nogil:
    cdef int out
    _fortran_idamax(&out, n, dx, incx)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_isamax "F_FUNC(isamaxwrp, ISAMAXWRP)"(int *out, int *n, s *sx, int *incx) nogil
cdef int isamax(int *n, s *sx, int *incx) nogil:
    cdef int out
    _fortran_isamax(&out, n, sx, incx)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_izamax "F_FUNC(izamaxwrp, IZAMAXWRP)"(int *out, int *n, npy_complex128 *zx, int *incx) nogil
cdef int izamax(int *n, z *zx, int *incx) nogil:
    cdef int out
    _fortran_izamax(&out, n, <npy_complex128*>zx, incx)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_lsame "F_FUNC(lsamewrp, LSAMEWRP)"(bint *out, char *ca, char *cb) nogil
cdef bint lsame(char *ca, char *cb) nogil:
    cdef bint out
    _fortran_lsame(&out, ca, cb)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_sasum "F_FUNC(sasumwrp, SASUMWRP)"(s *out, int *n, s *sx, int *incx) nogil
cdef s sasum(int *n, s *sx, int *incx) nogil:
    cdef s out
    _fortran_sasum(&out, n, sx, incx)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_scasum "F_FUNC(scasumwrp, SCASUMWRP)"(s *out, int *n, npy_complex64 *cx, int *incx) nogil
cdef s scasum(int *n, c *cx, int *incx) nogil:
    cdef s out
    _fortran_scasum(&out, n, <npy_complex64*>cx, incx)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_scnrm2 "F_FUNC(scnrm2wrp, SCNRM2WRP)"(s *out, int *n, npy_complex64 *x, int *incx) nogil
cdef s scnrm2(int *n, c *x, int *incx) nogil:
    cdef s out
    _fortran_scnrm2(&out, n, <npy_complex64*>x, incx)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_sdot "F_FUNC(sdotwrp, SDOTWRP)"(s *out, int *n, s *sx, int *incx, s *sy, int *incy) nogil
cdef s sdot(int *n, s *sx, int *incx, s *sy, int *incy) nogil:
    cdef s out
    _fortran_sdot(&out, n, sx, incx, sy, incy)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_sdsdot "F_FUNC(sdsdotwrp, SDSDOTWRP)"(s *out, int *n, s *sb, s *sx, int *incx, s *sy, int *incy) nogil
cdef s sdsdot(int *n, s *sb, s *sx, int *incx, s *sy, int *incy) nogil:
    cdef s out
    _fortran_sdsdot(&out, n, sb, sx, incx, sy, incy)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_snrm2 "F_FUNC(snrm2wrp, SNRM2WRP)"(s *out, int *n, s *x, int *incx) nogil
cdef s snrm2(int *n, s *x, int *incx) nogil:
    cdef s out
    _fortran_snrm2(&out, n, x, incx)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_zdotc "F_FUNC(zdotcwrp, ZDOTCWRP)"(z *out, int *n, npy_complex128 *zx, int *incx, npy_complex128 *zy, int *incy) nogil
cdef z zdotc(int *n, z *zx, int *incx, z *zy, int *incy) nogil:
    cdef z out
    _fortran_zdotc(&out, n, <npy_complex128*>zx, incx, <npy_complex128*>zy, incy)
    return out


cdef extern from "_blas_subroutines.h":
    void _fortran_zdotu "F_FUNC(zdotuwrp, ZDOTUWRP)"(z *out, int *n, npy_complex128 *zx, int *incx, npy_complex128 *zy, int *incy) nogil
cdef z zdotu(int *n, z *zx, int *incx, z *zy, int *incy) nogil:
    cdef z out
    _fortran_zdotu(&out, n, <npy_complex128*>zx, incx, <npy_complex128*>zy, incy)
    return out

cdef extern from "_blas_subroutines.h":
    void _fortran_caxpy "F_FUNC(caxpy,CAXPY)"(int *n, npy_complex64 *ca, npy_complex64 *cx, int *incx, npy_complex64 *cy, int *incy) nogil
cdef void caxpy(int *n, c *ca, c *cx, int *incx, c *cy, int *incy) nogil:
    _fortran_caxpy(n, <npy_complex64*>ca, <npy_complex64*>cx, incx, <npy_complex64*>cy, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_ccopy "F_FUNC(ccopy,CCOPY)"(int *n, npy_complex64 *cx, int *incx, npy_complex64 *cy, int *incy) nogil
cdef void ccopy(int *n, c *cx, int *incx, c *cy, int *incy) nogil:
    _fortran_ccopy(n, <npy_complex64*>cx, incx, <npy_complex64*>cy, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_cgbmv "F_FUNC(cgbmv,CGBMV)"(char *trans, int *m, int *n, int *kl, int *ku, npy_complex64 *alpha, npy_complex64 *a, int *lda, npy_complex64 *x, int *incx, npy_complex64 *beta, npy_complex64 *y, int *incy) nogil
cdef void cgbmv(char *trans, int *m, int *n, int *kl, int *ku, c *alpha, c *a, int *lda, c *x, int *incx, c *beta, c *y, int *incy) nogil:
    _fortran_cgbmv(trans, m, n, kl, ku, <npy_complex64*>alpha, <npy_complex64*>a, lda, <npy_complex64*>x, incx, <npy_complex64*>beta, <npy_complex64*>y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_cgemm "F_FUNC(cgemm,CGEMM)"(char *transa, char *transb, int *m, int *n, int *k, npy_complex64 *alpha, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *beta, npy_complex64 *c, int *ldc) nogil
cdef void cgemm(char *transa, char *transb, int *m, int *n, int *k, c *alpha, c *a, int *lda, c *b, int *ldb, c *beta, c *c, int *ldc) nogil:
    _fortran_cgemm(transa, transb, m, n, k, <npy_complex64*>alpha, <npy_complex64*>a, lda, <npy_complex64*>b, ldb, <npy_complex64*>beta, <npy_complex64*>c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_cgemv "F_FUNC(cgemv,CGEMV)"(char *trans, int *m, int *n, npy_complex64 *alpha, npy_complex64 *a, int *lda, npy_complex64 *x, int *incx, npy_complex64 *beta, npy_complex64 *y, int *incy) nogil
cdef void cgemv(char *trans, int *m, int *n, c *alpha, c *a, int *lda, c *x, int *incx, c *beta, c *y, int *incy) nogil:
    _fortran_cgemv(trans, m, n, <npy_complex64*>alpha, <npy_complex64*>a, lda, <npy_complex64*>x, incx, <npy_complex64*>beta, <npy_complex64*>y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_cgerc "F_FUNC(cgerc,CGERC)"(int *m, int *n, npy_complex64 *alpha, npy_complex64 *x, int *incx, npy_complex64 *y, int *incy, npy_complex64 *a, int *lda) nogil
cdef void cgerc(int *m, int *n, c *alpha, c *x, int *incx, c *y, int *incy, c *a, int *lda) nogil:
    _fortran_cgerc(m, n, <npy_complex64*>alpha, <npy_complex64*>x, incx, <npy_complex64*>y, incy, <npy_complex64*>a, lda)

cdef extern from "_blas_subroutines.h":
    void _fortran_cgeru "F_FUNC(cgeru,CGERU)"(int *m, int *n, npy_complex64 *alpha, npy_complex64 *x, int *incx, npy_complex64 *y, int *incy, npy_complex64 *a, int *lda) nogil
cdef void cgeru(int *m, int *n, c *alpha, c *x, int *incx, c *y, int *incy, c *a, int *lda) nogil:
    _fortran_cgeru(m, n, <npy_complex64*>alpha, <npy_complex64*>x, incx, <npy_complex64*>y, incy, <npy_complex64*>a, lda)

cdef extern from "_blas_subroutines.h":
    void _fortran_chbmv "F_FUNC(chbmv,CHBMV)"(char *uplo, int *n, int *k, npy_complex64 *alpha, npy_complex64 *a, int *lda, npy_complex64 *x, int *incx, npy_complex64 *beta, npy_complex64 *y, int *incy) nogil
cdef void chbmv(char *uplo, int *n, int *k, c *alpha, c *a, int *lda, c *x, int *incx, c *beta, c *y, int *incy) nogil:
    _fortran_chbmv(uplo, n, k, <npy_complex64*>alpha, <npy_complex64*>a, lda, <npy_complex64*>x, incx, <npy_complex64*>beta, <npy_complex64*>y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_chemm "F_FUNC(chemm,CHEMM)"(char *side, char *uplo, int *m, int *n, npy_complex64 *alpha, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *beta, npy_complex64 *c, int *ldc) nogil
cdef void chemm(char *side, char *uplo, int *m, int *n, c *alpha, c *a, int *lda, c *b, int *ldb, c *beta, c *c, int *ldc) nogil:
    _fortran_chemm(side, uplo, m, n, <npy_complex64*>alpha, <npy_complex64*>a, lda, <npy_complex64*>b, ldb, <npy_complex64*>beta, <npy_complex64*>c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_chemv "F_FUNC(chemv,CHEMV)"(char *uplo, int *n, npy_complex64 *alpha, npy_complex64 *a, int *lda, npy_complex64 *x, int *incx, npy_complex64 *beta, npy_complex64 *y, int *incy) nogil
cdef void chemv(char *uplo, int *n, c *alpha, c *a, int *lda, c *x, int *incx, c *beta, c *y, int *incy) nogil:
    _fortran_chemv(uplo, n, <npy_complex64*>alpha, <npy_complex64*>a, lda, <npy_complex64*>x, incx, <npy_complex64*>beta, <npy_complex64*>y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_cher "F_FUNC(cher,CHER)"(char *uplo, int *n, s *alpha, npy_complex64 *x, int *incx, npy_complex64 *a, int *lda) nogil
cdef void cher(char *uplo, int *n, s *alpha, c *x, int *incx, c *a, int *lda) nogil:
    _fortran_cher(uplo, n, alpha, <npy_complex64*>x, incx, <npy_complex64*>a, lda)

cdef extern from "_blas_subroutines.h":
    void _fortran_cher2 "F_FUNC(cher2,CHER2)"(char *uplo, int *n, npy_complex64 *alpha, npy_complex64 *x, int *incx, npy_complex64 *y, int *incy, npy_complex64 *a, int *lda) nogil
cdef void cher2(char *uplo, int *n, c *alpha, c *x, int *incx, c *y, int *incy, c *a, int *lda) nogil:
    _fortran_cher2(uplo, n, <npy_complex64*>alpha, <npy_complex64*>x, incx, <npy_complex64*>y, incy, <npy_complex64*>a, lda)

cdef extern from "_blas_subroutines.h":
    void _fortran_cher2k "F_FUNC(cher2k,CHER2K)"(char *uplo, char *trans, int *n, int *k, npy_complex64 *alpha, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, s *beta, npy_complex64 *c, int *ldc) nogil
cdef void cher2k(char *uplo, char *trans, int *n, int *k, c *alpha, c *a, int *lda, c *b, int *ldb, s *beta, c *c, int *ldc) nogil:
    _fortran_cher2k(uplo, trans, n, k, <npy_complex64*>alpha, <npy_complex64*>a, lda, <npy_complex64*>b, ldb, beta, <npy_complex64*>c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_cherk "F_FUNC(cherk,CHERK)"(char *uplo, char *trans, int *n, int *k, s *alpha, npy_complex64 *a, int *lda, s *beta, npy_complex64 *c, int *ldc) nogil
cdef void cherk(char *uplo, char *trans, int *n, int *k, s *alpha, c *a, int *lda, s *beta, c *c, int *ldc) nogil:
    _fortran_cherk(uplo, trans, n, k, alpha, <npy_complex64*>a, lda, beta, <npy_complex64*>c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_chpmv "F_FUNC(chpmv,CHPMV)"(char *uplo, int *n, npy_complex64 *alpha, npy_complex64 *ap, npy_complex64 *x, int *incx, npy_complex64 *beta, npy_complex64 *y, int *incy) nogil
cdef void chpmv(char *uplo, int *n, c *alpha, c *ap, c *x, int *incx, c *beta, c *y, int *incy) nogil:
    _fortran_chpmv(uplo, n, <npy_complex64*>alpha, <npy_complex64*>ap, <npy_complex64*>x, incx, <npy_complex64*>beta, <npy_complex64*>y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_chpr "F_FUNC(chpr,CHPR)"(char *uplo, int *n, s *alpha, npy_complex64 *x, int *incx, npy_complex64 *ap) nogil
cdef void chpr(char *uplo, int *n, s *alpha, c *x, int *incx, c *ap) nogil:
    _fortran_chpr(uplo, n, alpha, <npy_complex64*>x, incx, <npy_complex64*>ap)

cdef extern from "_blas_subroutines.h":
    void _fortran_chpr2 "F_FUNC(chpr2,CHPR2)"(char *uplo, int *n, npy_complex64 *alpha, npy_complex64 *x, int *incx, npy_complex64 *y, int *incy, npy_complex64 *ap) nogil
cdef void chpr2(char *uplo, int *n, c *alpha, c *x, int *incx, c *y, int *incy, c *ap) nogil:
    _fortran_chpr2(uplo, n, <npy_complex64*>alpha, <npy_complex64*>x, incx, <npy_complex64*>y, incy, <npy_complex64*>ap)

cdef extern from "_blas_subroutines.h":
    void _fortran_crotg "F_FUNC(crotg,CROTG)"(npy_complex64 *ca, npy_complex64 *cb, s *c, npy_complex64 *s) nogil
cdef void crotg(c *ca, c *cb, s *c, c *s) nogil:
    _fortran_crotg(<npy_complex64*>ca, <npy_complex64*>cb, c, <npy_complex64*>s)

cdef extern from "_blas_subroutines.h":
    void _fortran_cscal "F_FUNC(cscal,CSCAL)"(int *n, npy_complex64 *ca, npy_complex64 *cx, int *incx) nogil
cdef void cscal(int *n, c *ca, c *cx, int *incx) nogil:
    _fortran_cscal(n, <npy_complex64*>ca, <npy_complex64*>cx, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_csrot "F_FUNC(csrot,CSROT)"(int *n, npy_complex64 *cx, int *incx, npy_complex64 *cy, int *incy, s *c, s *s) nogil
cdef void csrot(int *n, c *cx, int *incx, c *cy, int *incy, s *c, s *s) nogil:
    _fortran_csrot(n, <npy_complex64*>cx, incx, <npy_complex64*>cy, incy, c, s)

cdef extern from "_blas_subroutines.h":
    void _fortran_csscal "F_FUNC(csscal,CSSCAL)"(int *n, s *sa, npy_complex64 *cx, int *incx) nogil
cdef void csscal(int *n, s *sa, c *cx, int *incx) nogil:
    _fortran_csscal(n, sa, <npy_complex64*>cx, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_cswap "F_FUNC(cswap,CSWAP)"(int *n, npy_complex64 *cx, int *incx, npy_complex64 *cy, int *incy) nogil
cdef void cswap(int *n, c *cx, int *incx, c *cy, int *incy) nogil:
    _fortran_cswap(n, <npy_complex64*>cx, incx, <npy_complex64*>cy, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_csymm "F_FUNC(csymm,CSYMM)"(char *side, char *uplo, int *m, int *n, npy_complex64 *alpha, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *beta, npy_complex64 *c, int *ldc) nogil
cdef void csymm(char *side, char *uplo, int *m, int *n, c *alpha, c *a, int *lda, c *b, int *ldb, c *beta, c *c, int *ldc) nogil:
    _fortran_csymm(side, uplo, m, n, <npy_complex64*>alpha, <npy_complex64*>a, lda, <npy_complex64*>b, ldb, <npy_complex64*>beta, <npy_complex64*>c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_csyr2k "F_FUNC(csyr2k,CSYR2K)"(char *uplo, char *trans, int *n, int *k, npy_complex64 *alpha, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb, npy_complex64 *beta, npy_complex64 *c, int *ldc) nogil
cdef void csyr2k(char *uplo, char *trans, int *n, int *k, c *alpha, c *a, int *lda, c *b, int *ldb, c *beta, c *c, int *ldc) nogil:
    _fortran_csyr2k(uplo, trans, n, k, <npy_complex64*>alpha, <npy_complex64*>a, lda, <npy_complex64*>b, ldb, <npy_complex64*>beta, <npy_complex64*>c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_csyrk "F_FUNC(csyrk,CSYRK)"(char *uplo, char *trans, int *n, int *k, npy_complex64 *alpha, npy_complex64 *a, int *lda, npy_complex64 *beta, npy_complex64 *c, int *ldc) nogil
cdef void csyrk(char *uplo, char *trans, int *n, int *k, c *alpha, c *a, int *lda, c *beta, c *c, int *ldc) nogil:
    _fortran_csyrk(uplo, trans, n, k, <npy_complex64*>alpha, <npy_complex64*>a, lda, <npy_complex64*>beta, <npy_complex64*>c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_ctbmv "F_FUNC(ctbmv,CTBMV)"(char *uplo, char *trans, char *diag, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *x, int *incx) nogil
cdef void ctbmv(char *uplo, char *trans, char *diag, int *n, int *k, c *a, int *lda, c *x, int *incx) nogil:
    _fortran_ctbmv(uplo, trans, diag, n, k, <npy_complex64*>a, lda, <npy_complex64*>x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_ctbsv "F_FUNC(ctbsv,CTBSV)"(char *uplo, char *trans, char *diag, int *n, int *k, npy_complex64 *a, int *lda, npy_complex64 *x, int *incx) nogil
cdef void ctbsv(char *uplo, char *trans, char *diag, int *n, int *k, c *a, int *lda, c *x, int *incx) nogil:
    _fortran_ctbsv(uplo, trans, diag, n, k, <npy_complex64*>a, lda, <npy_complex64*>x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_ctpmv "F_FUNC(ctpmv,CTPMV)"(char *uplo, char *trans, char *diag, int *n, npy_complex64 *ap, npy_complex64 *x, int *incx) nogil
cdef void ctpmv(char *uplo, char *trans, char *diag, int *n, c *ap, c *x, int *incx) nogil:
    _fortran_ctpmv(uplo, trans, diag, n, <npy_complex64*>ap, <npy_complex64*>x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_ctpsv "F_FUNC(ctpsv,CTPSV)"(char *uplo, char *trans, char *diag, int *n, npy_complex64 *ap, npy_complex64 *x, int *incx) nogil
cdef void ctpsv(char *uplo, char *trans, char *diag, int *n, c *ap, c *x, int *incx) nogil:
    _fortran_ctpsv(uplo, trans, diag, n, <npy_complex64*>ap, <npy_complex64*>x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_ctrmm "F_FUNC(ctrmm,CTRMM)"(char *side, char *uplo, char *transa, char *diag, int *m, int *n, npy_complex64 *alpha, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb) nogil
cdef void ctrmm(char *side, char *uplo, char *transa, char *diag, int *m, int *n, c *alpha, c *a, int *lda, c *b, int *ldb) nogil:
    _fortran_ctrmm(side, uplo, transa, diag, m, n, <npy_complex64*>alpha, <npy_complex64*>a, lda, <npy_complex64*>b, ldb)

cdef extern from "_blas_subroutines.h":
    void _fortran_ctrmv "F_FUNC(ctrmv,CTRMV)"(char *uplo, char *trans, char *diag, int *n, npy_complex64 *a, int *lda, npy_complex64 *x, int *incx) nogil
cdef void ctrmv(char *uplo, char *trans, char *diag, int *n, c *a, int *lda, c *x, int *incx) nogil:
    _fortran_ctrmv(uplo, trans, diag, n, <npy_complex64*>a, lda, <npy_complex64*>x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_ctrsm "F_FUNC(ctrsm,CTRSM)"(char *side, char *uplo, char *transa, char *diag, int *m, int *n, npy_complex64 *alpha, npy_complex64 *a, int *lda, npy_complex64 *b, int *ldb) nogil
cdef void ctrsm(char *side, char *uplo, char *transa, char *diag, int *m, int *n, c *alpha, c *a, int *lda, c *b, int *ldb) nogil:
    _fortran_ctrsm(side, uplo, transa, diag, m, n, <npy_complex64*>alpha, <npy_complex64*>a, lda, <npy_complex64*>b, ldb)

cdef extern from "_blas_subroutines.h":
    void _fortran_ctrsv "F_FUNC(ctrsv,CTRSV)"(char *uplo, char *trans, char *diag, int *n, npy_complex64 *a, int *lda, npy_complex64 *x, int *incx) nogil
cdef void ctrsv(char *uplo, char *trans, char *diag, int *n, c *a, int *lda, c *x, int *incx) nogil:
    _fortran_ctrsv(uplo, trans, diag, n, <npy_complex64*>a, lda, <npy_complex64*>x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_daxpy "F_FUNC(daxpy,DAXPY)"(int *n, d *da, d *dx, int *incx, d *dy, int *incy) nogil
cdef void daxpy(int *n, d *da, d *dx, int *incx, d *dy, int *incy) nogil:
    _fortran_daxpy(n, da, dx, incx, dy, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_dcopy "F_FUNC(dcopy,DCOPY)"(int *n, d *dx, int *incx, d *dy, int *incy) nogil
cdef void dcopy(int *n, d *dx, int *incx, d *dy, int *incy) nogil:
    _fortran_dcopy(n, dx, incx, dy, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_dgbmv "F_FUNC(dgbmv,DGBMV)"(char *trans, int *m, int *n, int *kl, int *ku, d *alpha, d *a, int *lda, d *x, int *incx, d *beta, d *y, int *incy) nogil
cdef void dgbmv(char *trans, int *m, int *n, int *kl, int *ku, d *alpha, d *a, int *lda, d *x, int *incx, d *beta, d *y, int *incy) nogil:
    _fortran_dgbmv(trans, m, n, kl, ku, alpha, a, lda, x, incx, beta, y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_dgemm "F_FUNC(dgemm,DGEMM)"(char *transa, char *transb, int *m, int *n, int *k, d *alpha, d *a, int *lda, d *b, int *ldb, d *beta, d *c, int *ldc) nogil
cdef void dgemm(char *transa, char *transb, int *m, int *n, int *k, d *alpha, d *a, int *lda, d *b, int *ldb, d *beta, d *c, int *ldc) nogil:
    _fortran_dgemm(transa, transb, m, n, k, alpha, a, lda, b, ldb, beta, c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_dgemv "F_FUNC(dgemv,DGEMV)"(char *trans, int *m, int *n, d *alpha, d *a, int *lda, d *x, int *incx, d *beta, d *y, int *incy) nogil
cdef void dgemv(char *trans, int *m, int *n, d *alpha, d *a, int *lda, d *x, int *incx, d *beta, d *y, int *incy) nogil:
    _fortran_dgemv(trans, m, n, alpha, a, lda, x, incx, beta, y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_dger "F_FUNC(dger,DGER)"(int *m, int *n, d *alpha, d *x, int *incx, d *y, int *incy, d *a, int *lda) nogil
cdef void dger(int *m, int *n, d *alpha, d *x, int *incx, d *y, int *incy, d *a, int *lda) nogil:
    _fortran_dger(m, n, alpha, x, incx, y, incy, a, lda)

cdef extern from "_blas_subroutines.h":
    void _fortran_drot "F_FUNC(drot,DROT)"(int *n, d *dx, int *incx, d *dy, int *incy, d *c, d *s) nogil
cdef void drot(int *n, d *dx, int *incx, d *dy, int *incy, d *c, d *s) nogil:
    _fortran_drot(n, dx, incx, dy, incy, c, s)

cdef extern from "_blas_subroutines.h":
    void _fortran_drotg "F_FUNC(drotg,DROTG)"(d *da, d *db, d *c, d *s) nogil
cdef void drotg(d *da, d *db, d *c, d *s) nogil:
    _fortran_drotg(da, db, c, s)

cdef extern from "_blas_subroutines.h":
    void _fortran_drotm "F_FUNC(drotm,DROTM)"(int *n, d *dx, int *incx, d *dy, int *incy, d *dparam) nogil
cdef void drotm(int *n, d *dx, int *incx, d *dy, int *incy, d *dparam) nogil:
    _fortran_drotm(n, dx, incx, dy, incy, dparam)

cdef extern from "_blas_subroutines.h":
    void _fortran_drotmg "F_FUNC(drotmg,DROTMG)"(d *dd1, d *dd2, d *dx1, d *dy1, d *dparam) nogil
cdef void drotmg(d *dd1, d *dd2, d *dx1, d *dy1, d *dparam) nogil:
    _fortran_drotmg(dd1, dd2, dx1, dy1, dparam)

cdef extern from "_blas_subroutines.h":
    void _fortran_dsbmv "F_FUNC(dsbmv,DSBMV)"(char *uplo, int *n, int *k, d *alpha, d *a, int *lda, d *x, int *incx, d *beta, d *y, int *incy) nogil
cdef void dsbmv(char *uplo, int *n, int *k, d *alpha, d *a, int *lda, d *x, int *incx, d *beta, d *y, int *incy) nogil:
    _fortran_dsbmv(uplo, n, k, alpha, a, lda, x, incx, beta, y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_dscal "F_FUNC(dscal,DSCAL)"(int *n, d *da, d *dx, int *incx) nogil
cdef void dscal(int *n, d *da, d *dx, int *incx) nogil:
    _fortran_dscal(n, da, dx, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_dspmv "F_FUNC(dspmv,DSPMV)"(char *uplo, int *n, d *alpha, d *ap, d *x, int *incx, d *beta, d *y, int *incy) nogil
cdef void dspmv(char *uplo, int *n, d *alpha, d *ap, d *x, int *incx, d *beta, d *y, int *incy) nogil:
    _fortran_dspmv(uplo, n, alpha, ap, x, incx, beta, y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_dspr "F_FUNC(dspr,DSPR)"(char *uplo, int *n, d *alpha, d *x, int *incx, d *ap) nogil
cdef void dspr(char *uplo, int *n, d *alpha, d *x, int *incx, d *ap) nogil:
    _fortran_dspr(uplo, n, alpha, x, incx, ap)

cdef extern from "_blas_subroutines.h":
    void _fortran_dspr2 "F_FUNC(dspr2,DSPR2)"(char *uplo, int *n, d *alpha, d *x, int *incx, d *y, int *incy, d *ap) nogil
cdef void dspr2(char *uplo, int *n, d *alpha, d *x, int *incx, d *y, int *incy, d *ap) nogil:
    _fortran_dspr2(uplo, n, alpha, x, incx, y, incy, ap)

cdef extern from "_blas_subroutines.h":
    void _fortran_dswap "F_FUNC(dswap,DSWAP)"(int *n, d *dx, int *incx, d *dy, int *incy) nogil
cdef void dswap(int *n, d *dx, int *incx, d *dy, int *incy) nogil:
    _fortran_dswap(n, dx, incx, dy, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_dsymm "F_FUNC(dsymm,DSYMM)"(char *side, char *uplo, int *m, int *n, d *alpha, d *a, int *lda, d *b, int *ldb, d *beta, d *c, int *ldc) nogil
cdef void dsymm(char *side, char *uplo, int *m, int *n, d *alpha, d *a, int *lda, d *b, int *ldb, d *beta, d *c, int *ldc) nogil:
    _fortran_dsymm(side, uplo, m, n, alpha, a, lda, b, ldb, beta, c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_dsymv "F_FUNC(dsymv,DSYMV)"(char *uplo, int *n, d *alpha, d *a, int *lda, d *x, int *incx, d *beta, d *y, int *incy) nogil
cdef void dsymv(char *uplo, int *n, d *alpha, d *a, int *lda, d *x, int *incx, d *beta, d *y, int *incy) nogil:
    _fortran_dsymv(uplo, n, alpha, a, lda, x, incx, beta, y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_dsyr "F_FUNC(dsyr,DSYR)"(char *uplo, int *n, d *alpha, d *x, int *incx, d *a, int *lda) nogil
cdef void dsyr(char *uplo, int *n, d *alpha, d *x, int *incx, d *a, int *lda) nogil:
    _fortran_dsyr(uplo, n, alpha, x, incx, a, lda)

cdef extern from "_blas_subroutines.h":
    void _fortran_dsyr2 "F_FUNC(dsyr2,DSYR2)"(char *uplo, int *n, d *alpha, d *x, int *incx, d *y, int *incy, d *a, int *lda) nogil
cdef void dsyr2(char *uplo, int *n, d *alpha, d *x, int *incx, d *y, int *incy, d *a, int *lda) nogil:
    _fortran_dsyr2(uplo, n, alpha, x, incx, y, incy, a, lda)

cdef extern from "_blas_subroutines.h":
    void _fortran_dsyr2k "F_FUNC(dsyr2k,DSYR2K)"(char *uplo, char *trans, int *n, int *k, d *alpha, d *a, int *lda, d *b, int *ldb, d *beta, d *c, int *ldc) nogil
cdef void dsyr2k(char *uplo, char *trans, int *n, int *k, d *alpha, d *a, int *lda, d *b, int *ldb, d *beta, d *c, int *ldc) nogil:
    _fortran_dsyr2k(uplo, trans, n, k, alpha, a, lda, b, ldb, beta, c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_dsyrk "F_FUNC(dsyrk,DSYRK)"(char *uplo, char *trans, int *n, int *k, d *alpha, d *a, int *lda, d *beta, d *c, int *ldc) nogil
cdef void dsyrk(char *uplo, char *trans, int *n, int *k, d *alpha, d *a, int *lda, d *beta, d *c, int *ldc) nogil:
    _fortran_dsyrk(uplo, trans, n, k, alpha, a, lda, beta, c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_dtbmv "F_FUNC(dtbmv,DTBMV)"(char *uplo, char *trans, char *diag, int *n, int *k, d *a, int *lda, d *x, int *incx) nogil
cdef void dtbmv(char *uplo, char *trans, char *diag, int *n, int *k, d *a, int *lda, d *x, int *incx) nogil:
    _fortran_dtbmv(uplo, trans, diag, n, k, a, lda, x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_dtbsv "F_FUNC(dtbsv,DTBSV)"(char *uplo, char *trans, char *diag, int *n, int *k, d *a, int *lda, d *x, int *incx) nogil
cdef void dtbsv(char *uplo, char *trans, char *diag, int *n, int *k, d *a, int *lda, d *x, int *incx) nogil:
    _fortran_dtbsv(uplo, trans, diag, n, k, a, lda, x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_dtpmv "F_FUNC(dtpmv,DTPMV)"(char *uplo, char *trans, char *diag, int *n, d *ap, d *x, int *incx) nogil
cdef void dtpmv(char *uplo, char *trans, char *diag, int *n, d *ap, d *x, int *incx) nogil:
    _fortran_dtpmv(uplo, trans, diag, n, ap, x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_dtpsv "F_FUNC(dtpsv,DTPSV)"(char *uplo, char *trans, char *diag, int *n, d *ap, d *x, int *incx) nogil
cdef void dtpsv(char *uplo, char *trans, char *diag, int *n, d *ap, d *x, int *incx) nogil:
    _fortran_dtpsv(uplo, trans, diag, n, ap, x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_dtrmm "F_FUNC(dtrmm,DTRMM)"(char *side, char *uplo, char *transa, char *diag, int *m, int *n, d *alpha, d *a, int *lda, d *b, int *ldb) nogil
cdef void dtrmm(char *side, char *uplo, char *transa, char *diag, int *m, int *n, d *alpha, d *a, int *lda, d *b, int *ldb) nogil:
    _fortran_dtrmm(side, uplo, transa, diag, m, n, alpha, a, lda, b, ldb)

cdef extern from "_blas_subroutines.h":
    void _fortran_dtrmv "F_FUNC(dtrmv,DTRMV)"(char *uplo, char *trans, char *diag, int *n, d *a, int *lda, d *x, int *incx) nogil
cdef void dtrmv(char *uplo, char *trans, char *diag, int *n, d *a, int *lda, d *x, int *incx) nogil:
    _fortran_dtrmv(uplo, trans, diag, n, a, lda, x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_dtrsm "F_FUNC(dtrsm,DTRSM)"(char *side, char *uplo, char *transa, char *diag, int *m, int *n, d *alpha, d *a, int *lda, d *b, int *ldb) nogil
cdef void dtrsm(char *side, char *uplo, char *transa, char *diag, int *m, int *n, d *alpha, d *a, int *lda, d *b, int *ldb) nogil:
    _fortran_dtrsm(side, uplo, transa, diag, m, n, alpha, a, lda, b, ldb)

cdef extern from "_blas_subroutines.h":
    void _fortran_dtrsv "F_FUNC(dtrsv,DTRSV)"(char *uplo, char *trans, char *diag, int *n, d *a, int *lda, d *x, int *incx) nogil
cdef void dtrsv(char *uplo, char *trans, char *diag, int *n, d *a, int *lda, d *x, int *incx) nogil:
    _fortran_dtrsv(uplo, trans, diag, n, a, lda, x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_saxpy "F_FUNC(saxpy,SAXPY)"(int *n, s *sa, s *sx, int *incx, s *sy, int *incy) nogil
cdef void saxpy(int *n, s *sa, s *sx, int *incx, s *sy, int *incy) nogil:
    _fortran_saxpy(n, sa, sx, incx, sy, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_scopy "F_FUNC(scopy,SCOPY)"(int *n, s *sx, int *incx, s *sy, int *incy) nogil
cdef void scopy(int *n, s *sx, int *incx, s *sy, int *incy) nogil:
    _fortran_scopy(n, sx, incx, sy, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_sgbmv "F_FUNC(sgbmv,SGBMV)"(char *trans, int *m, int *n, int *kl, int *ku, s *alpha, s *a, int *lda, s *x, int *incx, s *beta, s *y, int *incy) nogil
cdef void sgbmv(char *trans, int *m, int *n, int *kl, int *ku, s *alpha, s *a, int *lda, s *x, int *incx, s *beta, s *y, int *incy) nogil:
    _fortran_sgbmv(trans, m, n, kl, ku, alpha, a, lda, x, incx, beta, y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_sgemm "F_FUNC(sgemm,SGEMM)"(char *transa, char *transb, int *m, int *n, int *k, s *alpha, s *a, int *lda, s *b, int *ldb, s *beta, s *c, int *ldc) nogil
cdef void sgemm(char *transa, char *transb, int *m, int *n, int *k, s *alpha, s *a, int *lda, s *b, int *ldb, s *beta, s *c, int *ldc) nogil:
    _fortran_sgemm(transa, transb, m, n, k, alpha, a, lda, b, ldb, beta, c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_sgemv "F_FUNC(sgemv,SGEMV)"(char *trans, int *m, int *n, s *alpha, s *a, int *lda, s *x, int *incx, s *beta, s *y, int *incy) nogil
cdef void sgemv(char *trans, int *m, int *n, s *alpha, s *a, int *lda, s *x, int *incx, s *beta, s *y, int *incy) nogil:
    _fortran_sgemv(trans, m, n, alpha, a, lda, x, incx, beta, y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_sger "F_FUNC(sger,SGER)"(int *m, int *n, s *alpha, s *x, int *incx, s *y, int *incy, s *a, int *lda) nogil
cdef void sger(int *m, int *n, s *alpha, s *x, int *incx, s *y, int *incy, s *a, int *lda) nogil:
    _fortran_sger(m, n, alpha, x, incx, y, incy, a, lda)

cdef extern from "_blas_subroutines.h":
    void _fortran_srot "F_FUNC(srot,SROT)"(int *n, s *sx, int *incx, s *sy, int *incy, s *c, s *s) nogil
cdef void srot(int *n, s *sx, int *incx, s *sy, int *incy, s *c, s *s) nogil:
    _fortran_srot(n, sx, incx, sy, incy, c, s)

cdef extern from "_blas_subroutines.h":
    void _fortran_srotg "F_FUNC(srotg,SROTG)"(s *sa, s *sb, s *c, s *s) nogil
cdef void srotg(s *sa, s *sb, s *c, s *s) nogil:
    _fortran_srotg(sa, sb, c, s)

cdef extern from "_blas_subroutines.h":
    void _fortran_srotm "F_FUNC(srotm,SROTM)"(int *n, s *sx, int *incx, s *sy, int *incy, s *sparam) nogil
cdef void srotm(int *n, s *sx, int *incx, s *sy, int *incy, s *sparam) nogil:
    _fortran_srotm(n, sx, incx, sy, incy, sparam)

cdef extern from "_blas_subroutines.h":
    void _fortran_srotmg "F_FUNC(srotmg,SROTMG)"(s *sd1, s *sd2, s *sx1, s *sy1, s *sparam) nogil
cdef void srotmg(s *sd1, s *sd2, s *sx1, s *sy1, s *sparam) nogil:
    _fortran_srotmg(sd1, sd2, sx1, sy1, sparam)

cdef extern from "_blas_subroutines.h":
    void _fortran_ssbmv "F_FUNC(ssbmv,SSBMV)"(char *uplo, int *n, int *k, s *alpha, s *a, int *lda, s *x, int *incx, s *beta, s *y, int *incy) nogil
cdef void ssbmv(char *uplo, int *n, int *k, s *alpha, s *a, int *lda, s *x, int *incx, s *beta, s *y, int *incy) nogil:
    _fortran_ssbmv(uplo, n, k, alpha, a, lda, x, incx, beta, y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_sscal "F_FUNC(sscal,SSCAL)"(int *n, s *sa, s *sx, int *incx) nogil
cdef void sscal(int *n, s *sa, s *sx, int *incx) nogil:
    _fortran_sscal(n, sa, sx, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_sspmv "F_FUNC(sspmv,SSPMV)"(char *uplo, int *n, s *alpha, s *ap, s *x, int *incx, s *beta, s *y, int *incy) nogil
cdef void sspmv(char *uplo, int *n, s *alpha, s *ap, s *x, int *incx, s *beta, s *y, int *incy) nogil:
    _fortran_sspmv(uplo, n, alpha, ap, x, incx, beta, y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_sspr "F_FUNC(sspr,SSPR)"(char *uplo, int *n, s *alpha, s *x, int *incx, s *ap) nogil
cdef void sspr(char *uplo, int *n, s *alpha, s *x, int *incx, s *ap) nogil:
    _fortran_sspr(uplo, n, alpha, x, incx, ap)

cdef extern from "_blas_subroutines.h":
    void _fortran_sspr2 "F_FUNC(sspr2,SSPR2)"(char *uplo, int *n, s *alpha, s *x, int *incx, s *y, int *incy, s *ap) nogil
cdef void sspr2(char *uplo, int *n, s *alpha, s *x, int *incx, s *y, int *incy, s *ap) nogil:
    _fortran_sspr2(uplo, n, alpha, x, incx, y, incy, ap)

cdef extern from "_blas_subroutines.h":
    void _fortran_sswap "F_FUNC(sswap,SSWAP)"(int *n, s *sx, int *incx, s *sy, int *incy) nogil
cdef void sswap(int *n, s *sx, int *incx, s *sy, int *incy) nogil:
    _fortran_sswap(n, sx, incx, sy, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_ssymm "F_FUNC(ssymm,SSYMM)"(char *side, char *uplo, int *m, int *n, s *alpha, s *a, int *lda, s *b, int *ldb, s *beta, s *c, int *ldc) nogil
cdef void ssymm(char *side, char *uplo, int *m, int *n, s *alpha, s *a, int *lda, s *b, int *ldb, s *beta, s *c, int *ldc) nogil:
    _fortran_ssymm(side, uplo, m, n, alpha, a, lda, b, ldb, beta, c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_ssymv "F_FUNC(ssymv,SSYMV)"(char *uplo, int *n, s *alpha, s *a, int *lda, s *x, int *incx, s *beta, s *y, int *incy) nogil
cdef void ssymv(char *uplo, int *n, s *alpha, s *a, int *lda, s *x, int *incx, s *beta, s *y, int *incy) nogil:
    _fortran_ssymv(uplo, n, alpha, a, lda, x, incx, beta, y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_ssyr "F_FUNC(ssyr,SSYR)"(char *uplo, int *n, s *alpha, s *x, int *incx, s *a, int *lda) nogil
cdef void ssyr(char *uplo, int *n, s *alpha, s *x, int *incx, s *a, int *lda) nogil:
    _fortran_ssyr(uplo, n, alpha, x, incx, a, lda)

cdef extern from "_blas_subroutines.h":
    void _fortran_ssyr2 "F_FUNC(ssyr2,SSYR2)"(char *uplo, int *n, s *alpha, s *x, int *incx, s *y, int *incy, s *a, int *lda) nogil
cdef void ssyr2(char *uplo, int *n, s *alpha, s *x, int *incx, s *y, int *incy, s *a, int *lda) nogil:
    _fortran_ssyr2(uplo, n, alpha, x, incx, y, incy, a, lda)

cdef extern from "_blas_subroutines.h":
    void _fortran_ssyr2k "F_FUNC(ssyr2k,SSYR2K)"(char *uplo, char *trans, int *n, int *k, s *alpha, s *a, int *lda, s *b, int *ldb, s *beta, s *c, int *ldc) nogil
cdef void ssyr2k(char *uplo, char *trans, int *n, int *k, s *alpha, s *a, int *lda, s *b, int *ldb, s *beta, s *c, int *ldc) nogil:
    _fortran_ssyr2k(uplo, trans, n, k, alpha, a, lda, b, ldb, beta, c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_ssyrk "F_FUNC(ssyrk,SSYRK)"(char *uplo, char *trans, int *n, int *k, s *alpha, s *a, int *lda, s *beta, s *c, int *ldc) nogil
cdef void ssyrk(char *uplo, char *trans, int *n, int *k, s *alpha, s *a, int *lda, s *beta, s *c, int *ldc) nogil:
    _fortran_ssyrk(uplo, trans, n, k, alpha, a, lda, beta, c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_stbmv "F_FUNC(stbmv,STBMV)"(char *uplo, char *trans, char *diag, int *n, int *k, s *a, int *lda, s *x, int *incx) nogil
cdef void stbmv(char *uplo, char *trans, char *diag, int *n, int *k, s *a, int *lda, s *x, int *incx) nogil:
    _fortran_stbmv(uplo, trans, diag, n, k, a, lda, x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_stbsv "F_FUNC(stbsv,STBSV)"(char *uplo, char *trans, char *diag, int *n, int *k, s *a, int *lda, s *x, int *incx) nogil
cdef void stbsv(char *uplo, char *trans, char *diag, int *n, int *k, s *a, int *lda, s *x, int *incx) nogil:
    _fortran_stbsv(uplo, trans, diag, n, k, a, lda, x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_stpmv "F_FUNC(stpmv,STPMV)"(char *uplo, char *trans, char *diag, int *n, s *ap, s *x, int *incx) nogil
cdef void stpmv(char *uplo, char *trans, char *diag, int *n, s *ap, s *x, int *incx) nogil:
    _fortran_stpmv(uplo, trans, diag, n, ap, x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_stpsv "F_FUNC(stpsv,STPSV)"(char *uplo, char *trans, char *diag, int *n, s *ap, s *x, int *incx) nogil
cdef void stpsv(char *uplo, char *trans, char *diag, int *n, s *ap, s *x, int *incx) nogil:
    _fortran_stpsv(uplo, trans, diag, n, ap, x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_strmm "F_FUNC(strmm,STRMM)"(char *side, char *uplo, char *transa, char *diag, int *m, int *n, s *alpha, s *a, int *lda, s *b, int *ldb) nogil
cdef void strmm(char *side, char *uplo, char *transa, char *diag, int *m, int *n, s *alpha, s *a, int *lda, s *b, int *ldb) nogil:
    _fortran_strmm(side, uplo, transa, diag, m, n, alpha, a, lda, b, ldb)

cdef extern from "_blas_subroutines.h":
    void _fortran_strmv "F_FUNC(strmv,STRMV)"(char *uplo, char *trans, char *diag, int *n, s *a, int *lda, s *x, int *incx) nogil
cdef void strmv(char *uplo, char *trans, char *diag, int *n, s *a, int *lda, s *x, int *incx) nogil:
    _fortran_strmv(uplo, trans, diag, n, a, lda, x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_strsm "F_FUNC(strsm,STRSM)"(char *side, char *uplo, char *transa, char *diag, int *m, int *n, s *alpha, s *a, int *lda, s *b, int *ldb) nogil
cdef void strsm(char *side, char *uplo, char *transa, char *diag, int *m, int *n, s *alpha, s *a, int *lda, s *b, int *ldb) nogil:
    _fortran_strsm(side, uplo, transa, diag, m, n, alpha, a, lda, b, ldb)

cdef extern from "_blas_subroutines.h":
    void _fortran_strsv "F_FUNC(strsv,STRSV)"(char *uplo, char *trans, char *diag, int *n, s *a, int *lda, s *x, int *incx) nogil
cdef void strsv(char *uplo, char *trans, char *diag, int *n, s *a, int *lda, s *x, int *incx) nogil:
    _fortran_strsv(uplo, trans, diag, n, a, lda, x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_zaxpy "F_FUNC(zaxpy,ZAXPY)"(int *n, npy_complex128 *za, npy_complex128 *zx, int *incx, npy_complex128 *zy, int *incy) nogil
cdef void zaxpy(int *n, z *za, z *zx, int *incx, z *zy, int *incy) nogil:
    _fortran_zaxpy(n, <npy_complex128*>za, <npy_complex128*>zx, incx, <npy_complex128*>zy, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_zcopy "F_FUNC(zcopy,ZCOPY)"(int *n, npy_complex128 *zx, int *incx, npy_complex128 *zy, int *incy) nogil
cdef void zcopy(int *n, z *zx, int *incx, z *zy, int *incy) nogil:
    _fortran_zcopy(n, <npy_complex128*>zx, incx, <npy_complex128*>zy, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_zdrot "F_FUNC(zdrot,ZDROT)"(int *n, npy_complex128 *cx, int *incx, npy_complex128 *cy, int *incy, d *c, d *s) nogil
cdef void zdrot(int *n, z *cx, int *incx, z *cy, int *incy, d *c, d *s) nogil:
    _fortran_zdrot(n, <npy_complex128*>cx, incx, <npy_complex128*>cy, incy, c, s)

cdef extern from "_blas_subroutines.h":
    void _fortran_zdscal "F_FUNC(zdscal,ZDSCAL)"(int *n, d *da, npy_complex128 *zx, int *incx) nogil
cdef void zdscal(int *n, d *da, z *zx, int *incx) nogil:
    _fortran_zdscal(n, da, <npy_complex128*>zx, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_zgbmv "F_FUNC(zgbmv,ZGBMV)"(char *trans, int *m, int *n, int *kl, int *ku, npy_complex128 *alpha, npy_complex128 *a, int *lda, npy_complex128 *x, int *incx, npy_complex128 *beta, npy_complex128 *y, int *incy) nogil
cdef void zgbmv(char *trans, int *m, int *n, int *kl, int *ku, z *alpha, z *a, int *lda, z *x, int *incx, z *beta, z *y, int *incy) nogil:
    _fortran_zgbmv(trans, m, n, kl, ku, <npy_complex128*>alpha, <npy_complex128*>a, lda, <npy_complex128*>x, incx, <npy_complex128*>beta, <npy_complex128*>y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_zgemm "F_FUNC(zgemm,ZGEMM)"(char *transa, char *transb, int *m, int *n, int *k, npy_complex128 *alpha, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *beta, npy_complex128 *c, int *ldc) nogil
cdef void zgemm(char *transa, char *transb, int *m, int *n, int *k, z *alpha, z *a, int *lda, z *b, int *ldb, z *beta, z *c, int *ldc) nogil:
    _fortran_zgemm(transa, transb, m, n, k, <npy_complex128*>alpha, <npy_complex128*>a, lda, <npy_complex128*>b, ldb, <npy_complex128*>beta, <npy_complex128*>c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_zgemv "F_FUNC(zgemv,ZGEMV)"(char *trans, int *m, int *n, npy_complex128 *alpha, npy_complex128 *a, int *lda, npy_complex128 *x, int *incx, npy_complex128 *beta, npy_complex128 *y, int *incy) nogil
cdef void zgemv(char *trans, int *m, int *n, z *alpha, z *a, int *lda, z *x, int *incx, z *beta, z *y, int *incy) nogil:
    _fortran_zgemv(trans, m, n, <npy_complex128*>alpha, <npy_complex128*>a, lda, <npy_complex128*>x, incx, <npy_complex128*>beta, <npy_complex128*>y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_zgerc "F_FUNC(zgerc,ZGERC)"(int *m, int *n, npy_complex128 *alpha, npy_complex128 *x, int *incx, npy_complex128 *y, int *incy, npy_complex128 *a, int *lda) nogil
cdef void zgerc(int *m, int *n, z *alpha, z *x, int *incx, z *y, int *incy, z *a, int *lda) nogil:
    _fortran_zgerc(m, n, <npy_complex128*>alpha, <npy_complex128*>x, incx, <npy_complex128*>y, incy, <npy_complex128*>a, lda)

cdef extern from "_blas_subroutines.h":
    void _fortran_zgeru "F_FUNC(zgeru,ZGERU)"(int *m, int *n, npy_complex128 *alpha, npy_complex128 *x, int *incx, npy_complex128 *y, int *incy, npy_complex128 *a, int *lda) nogil
cdef void zgeru(int *m, int *n, z *alpha, z *x, int *incx, z *y, int *incy, z *a, int *lda) nogil:
    _fortran_zgeru(m, n, <npy_complex128*>alpha, <npy_complex128*>x, incx, <npy_complex128*>y, incy, <npy_complex128*>a, lda)

cdef extern from "_blas_subroutines.h":
    void _fortran_zhbmv "F_FUNC(zhbmv,ZHBMV)"(char *uplo, int *n, int *k, npy_complex128 *alpha, npy_complex128 *a, int *lda, npy_complex128 *x, int *incx, npy_complex128 *beta, npy_complex128 *y, int *incy) nogil
cdef void zhbmv(char *uplo, int *n, int *k, z *alpha, z *a, int *lda, z *x, int *incx, z *beta, z *y, int *incy) nogil:
    _fortran_zhbmv(uplo, n, k, <npy_complex128*>alpha, <npy_complex128*>a, lda, <npy_complex128*>x, incx, <npy_complex128*>beta, <npy_complex128*>y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_zhemm "F_FUNC(zhemm,ZHEMM)"(char *side, char *uplo, int *m, int *n, npy_complex128 *alpha, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *beta, npy_complex128 *c, int *ldc) nogil
cdef void zhemm(char *side, char *uplo, int *m, int *n, z *alpha, z *a, int *lda, z *b, int *ldb, z *beta, z *c, int *ldc) nogil:
    _fortran_zhemm(side, uplo, m, n, <npy_complex128*>alpha, <npy_complex128*>a, lda, <npy_complex128*>b, ldb, <npy_complex128*>beta, <npy_complex128*>c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_zhemv "F_FUNC(zhemv,ZHEMV)"(char *uplo, int *n, npy_complex128 *alpha, npy_complex128 *a, int *lda, npy_complex128 *x, int *incx, npy_complex128 *beta, npy_complex128 *y, int *incy) nogil
cdef void zhemv(char *uplo, int *n, z *alpha, z *a, int *lda, z *x, int *incx, z *beta, z *y, int *incy) nogil:
    _fortran_zhemv(uplo, n, <npy_complex128*>alpha, <npy_complex128*>a, lda, <npy_complex128*>x, incx, <npy_complex128*>beta, <npy_complex128*>y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_zher "F_FUNC(zher,ZHER)"(char *uplo, int *n, d *alpha, npy_complex128 *x, int *incx, npy_complex128 *a, int *lda) nogil
cdef void zher(char *uplo, int *n, d *alpha, z *x, int *incx, z *a, int *lda) nogil:
    _fortran_zher(uplo, n, alpha, <npy_complex128*>x, incx, <npy_complex128*>a, lda)

cdef extern from "_blas_subroutines.h":
    void _fortran_zher2 "F_FUNC(zher2,ZHER2)"(char *uplo, int *n, npy_complex128 *alpha, npy_complex128 *x, int *incx, npy_complex128 *y, int *incy, npy_complex128 *a, int *lda) nogil
cdef void zher2(char *uplo, int *n, z *alpha, z *x, int *incx, z *y, int *incy, z *a, int *lda) nogil:
    _fortran_zher2(uplo, n, <npy_complex128*>alpha, <npy_complex128*>x, incx, <npy_complex128*>y, incy, <npy_complex128*>a, lda)

cdef extern from "_blas_subroutines.h":
    void _fortran_zher2k "F_FUNC(zher2k,ZHER2K)"(char *uplo, char *trans, int *n, int *k, npy_complex128 *alpha, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, d *beta, npy_complex128 *c, int *ldc) nogil
cdef void zher2k(char *uplo, char *trans, int *n, int *k, z *alpha, z *a, int *lda, z *b, int *ldb, d *beta, z *c, int *ldc) nogil:
    _fortran_zher2k(uplo, trans, n, k, <npy_complex128*>alpha, <npy_complex128*>a, lda, <npy_complex128*>b, ldb, beta, <npy_complex128*>c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_zherk "F_FUNC(zherk,ZHERK)"(char *uplo, char *trans, int *n, int *k, d *alpha, npy_complex128 *a, int *lda, d *beta, npy_complex128 *c, int *ldc) nogil
cdef void zherk(char *uplo, char *trans, int *n, int *k, d *alpha, z *a, int *lda, d *beta, z *c, int *ldc) nogil:
    _fortran_zherk(uplo, trans, n, k, alpha, <npy_complex128*>a, lda, beta, <npy_complex128*>c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_zhpmv "F_FUNC(zhpmv,ZHPMV)"(char *uplo, int *n, npy_complex128 *alpha, npy_complex128 *ap, npy_complex128 *x, int *incx, npy_complex128 *beta, npy_complex128 *y, int *incy) nogil
cdef void zhpmv(char *uplo, int *n, z *alpha, z *ap, z *x, int *incx, z *beta, z *y, int *incy) nogil:
    _fortran_zhpmv(uplo, n, <npy_complex128*>alpha, <npy_complex128*>ap, <npy_complex128*>x, incx, <npy_complex128*>beta, <npy_complex128*>y, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_zhpr "F_FUNC(zhpr,ZHPR)"(char *uplo, int *n, d *alpha, npy_complex128 *x, int *incx, npy_complex128 *ap) nogil
cdef void zhpr(char *uplo, int *n, d *alpha, z *x, int *incx, z *ap) nogil:
    _fortran_zhpr(uplo, n, alpha, <npy_complex128*>x, incx, <npy_complex128*>ap)

cdef extern from "_blas_subroutines.h":
    void _fortran_zhpr2 "F_FUNC(zhpr2,ZHPR2)"(char *uplo, int *n, npy_complex128 *alpha, npy_complex128 *x, int *incx, npy_complex128 *y, int *incy, npy_complex128 *ap) nogil
cdef void zhpr2(char *uplo, int *n, z *alpha, z *x, int *incx, z *y, int *incy, z *ap) nogil:
    _fortran_zhpr2(uplo, n, <npy_complex128*>alpha, <npy_complex128*>x, incx, <npy_complex128*>y, incy, <npy_complex128*>ap)

cdef extern from "_blas_subroutines.h":
    void _fortran_zrotg "F_FUNC(zrotg,ZROTG)"(npy_complex128 *ca, npy_complex128 *cb, d *c, npy_complex128 *s) nogil
cdef void zrotg(z *ca, z *cb, d *c, z *s) nogil:
    _fortran_zrotg(<npy_complex128*>ca, <npy_complex128*>cb, c, <npy_complex128*>s)

cdef extern from "_blas_subroutines.h":
    void _fortran_zscal "F_FUNC(zscal,ZSCAL)"(int *n, npy_complex128 *za, npy_complex128 *zx, int *incx) nogil
cdef void zscal(int *n, z *za, z *zx, int *incx) nogil:
    _fortran_zscal(n, <npy_complex128*>za, <npy_complex128*>zx, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_zswap "F_FUNC(zswap,ZSWAP)"(int *n, npy_complex128 *zx, int *incx, npy_complex128 *zy, int *incy) nogil
cdef void zswap(int *n, z *zx, int *incx, z *zy, int *incy) nogil:
    _fortran_zswap(n, <npy_complex128*>zx, incx, <npy_complex128*>zy, incy)

cdef extern from "_blas_subroutines.h":
    void _fortran_zsymm "F_FUNC(zsymm,ZSYMM)"(char *side, char *uplo, int *m, int *n, npy_complex128 *alpha, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *beta, npy_complex128 *c, int *ldc) nogil
cdef void zsymm(char *side, char *uplo, int *m, int *n, z *alpha, z *a, int *lda, z *b, int *ldb, z *beta, z *c, int *ldc) nogil:
    _fortran_zsymm(side, uplo, m, n, <npy_complex128*>alpha, <npy_complex128*>a, lda, <npy_complex128*>b, ldb, <npy_complex128*>beta, <npy_complex128*>c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_zsyr2k "F_FUNC(zsyr2k,ZSYR2K)"(char *uplo, char *trans, int *n, int *k, npy_complex128 *alpha, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb, npy_complex128 *beta, npy_complex128 *c, int *ldc) nogil
cdef void zsyr2k(char *uplo, char *trans, int *n, int *k, z *alpha, z *a, int *lda, z *b, int *ldb, z *beta, z *c, int *ldc) nogil:
    _fortran_zsyr2k(uplo, trans, n, k, <npy_complex128*>alpha, <npy_complex128*>a, lda, <npy_complex128*>b, ldb, <npy_complex128*>beta, <npy_complex128*>c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_zsyrk "F_FUNC(zsyrk,ZSYRK)"(char *uplo, char *trans, int *n, int *k, npy_complex128 *alpha, npy_complex128 *a, int *lda, npy_complex128 *beta, npy_complex128 *c, int *ldc) nogil
cdef void zsyrk(char *uplo, char *trans, int *n, int *k, z *alpha, z *a, int *lda, z *beta, z *c, int *ldc) nogil:
    _fortran_zsyrk(uplo, trans, n, k, <npy_complex128*>alpha, <npy_complex128*>a, lda, <npy_complex128*>beta, <npy_complex128*>c, ldc)

cdef extern from "_blas_subroutines.h":
    void _fortran_ztbmv "F_FUNC(ztbmv,ZTBMV)"(char *uplo, char *trans, char *diag, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *x, int *incx) nogil
cdef void ztbmv(char *uplo, char *trans, char *diag, int *n, int *k, z *a, int *lda, z *x, int *incx) nogil:
    _fortran_ztbmv(uplo, trans, diag, n, k, <npy_complex128*>a, lda, <npy_complex128*>x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_ztbsv "F_FUNC(ztbsv,ZTBSV)"(char *uplo, char *trans, char *diag, int *n, int *k, npy_complex128 *a, int *lda, npy_complex128 *x, int *incx) nogil
cdef void ztbsv(char *uplo, char *trans, char *diag, int *n, int *k, z *a, int *lda, z *x, int *incx) nogil:
    _fortran_ztbsv(uplo, trans, diag, n, k, <npy_complex128*>a, lda, <npy_complex128*>x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_ztpmv "F_FUNC(ztpmv,ZTPMV)"(char *uplo, char *trans, char *diag, int *n, npy_complex128 *ap, npy_complex128 *x, int *incx) nogil
cdef void ztpmv(char *uplo, char *trans, char *diag, int *n, z *ap, z *x, int *incx) nogil:
    _fortran_ztpmv(uplo, trans, diag, n, <npy_complex128*>ap, <npy_complex128*>x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_ztpsv "F_FUNC(ztpsv,ZTPSV)"(char *uplo, char *trans, char *diag, int *n, npy_complex128 *ap, npy_complex128 *x, int *incx) nogil
cdef void ztpsv(char *uplo, char *trans, char *diag, int *n, z *ap, z *x, int *incx) nogil:
    _fortran_ztpsv(uplo, trans, diag, n, <npy_complex128*>ap, <npy_complex128*>x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_ztrmm "F_FUNC(ztrmm,ZTRMM)"(char *side, char *uplo, char *transa, char *diag, int *m, int *n, npy_complex128 *alpha, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb) nogil
cdef void ztrmm(char *side, char *uplo, char *transa, char *diag, int *m, int *n, z *alpha, z *a, int *lda, z *b, int *ldb) nogil:
    _fortran_ztrmm(side, uplo, transa, diag, m, n, <npy_complex128*>alpha, <npy_complex128*>a, lda, <npy_complex128*>b, ldb)

cdef extern from "_blas_subroutines.h":
    void _fortran_ztrmv "F_FUNC(ztrmv,ZTRMV)"(char *uplo, char *trans, char *diag, int *n, npy_complex128 *a, int *lda, npy_complex128 *x, int *incx) nogil
cdef void ztrmv(char *uplo, char *trans, char *diag, int *n, z *a, int *lda, z *x, int *incx) nogil:
    _fortran_ztrmv(uplo, trans, diag, n, <npy_complex128*>a, lda, <npy_complex128*>x, incx)

cdef extern from "_blas_subroutines.h":
    void _fortran_ztrsm "F_FUNC(ztrsm,ZTRSM)"(char *side, char *uplo, char *transa, char *diag, int *m, int *n, npy_complex128 *alpha, npy_complex128 *a, int *lda, npy_complex128 *b, int *ldb) nogil
cdef void ztrsm(char *side, char *uplo, char *transa, char *diag, int *m, int *n, z *alpha, z *a, int *lda, z *b, int *ldb) nogil:
    _fortran_ztrsm(side, uplo, transa, diag, m, n, <npy_complex128*>alpha, <npy_complex128*>a, lda, <npy_complex128*>b, ldb)

cdef extern from "_blas_subroutines.h":
    void _fortran_ztrsv "F_FUNC(ztrsv,ZTRSV)"(char *uplo, char *trans, char *diag, int *n, npy_complex128 *a, int *lda, npy_complex128 *x, int *incx) nogil
cdef void ztrsv(char *uplo, char *trans, char *diag, int *n, z *a, int *lda, z *x, int *incx) nogil:
    _fortran_ztrsv(uplo, trans, diag, n, <npy_complex128*>a, lda, <npy_complex128*>x, incx)


# Python-accessible wrappers for testing:

cdef inline bint _is_contiguous(double[:,:] a, int axis) nogil:
    return (a.strides[axis] == sizeof(a[0,0]) or a.shape[axis] == 1)

cpdef float complex _test_cdotc(float complex[:] cx, float complex[:] cy) nogil:
    cdef:
        int n = cx.shape[0]
        int incx = cx.strides[0] // sizeof(cx[0])
        int incy = cy.strides[0] // sizeof(cy[0])
    return cdotc(&n, &cx[0], &incx, &cy[0], &incy)

cpdef float complex _test_cdotu(float complex[:] cx, float complex[:] cy) nogil:
    cdef:
        int n = cx.shape[0]
        int incx = cx.strides[0] // sizeof(cx[0])
        int incy = cy.strides[0] // sizeof(cy[0])
    return cdotu(&n, &cx[0], &incx, &cy[0], &incy)

cpdef double _test_dasum(double[:] dx) nogil:
    cdef:
        int n = dx.shape[0]
        int incx = dx.strides[0] // sizeof(dx[0])
    return dasum(&n, &dx[0], &incx)

cpdef double _test_ddot(double[:] dx, double[:] dy) nogil:
    cdef:
        int n = dx.shape[0]
        int incx = dx.strides[0] // sizeof(dx[0])
        int incy = dy.strides[0] // sizeof(dy[0])
    return ddot(&n, &dx[0], &incx, &dy[0], &incy)

cpdef int _test_dgemm(double alpha, double[:,:] a, double[:,:] b, double beta,
                double[:,:] c) nogil except -1:
    cdef:
        char *transa
        char *transb
        int m, n, k, lda, ldb, ldc
        double *a0=&a[0,0]
        double *b0=&b[0,0]
        double *c0=&c[0,0]
    # In the case that c is C contiguous, swap a and b and
    # swap whether or not each of them is transposed.
    # This can be done because a.dot(b) = b.T.dot(a.T).T.
    if _is_contiguous(c, 1):
        if _is_contiguous(a, 1):
            transb = 'n'
            ldb = (&a[1,0]) - a0 if a.shape[0] > 1 else 1
        elif _is_contiguous(a, 0):
            transb = 't'
            ldb = (&a[0,1]) - a0 if a.shape[1] > 1 else 1
        else:
            with gil:
                raise ValueError("Input 'a' is neither C nor Fortran contiguous.")
        if _is_contiguous(b, 1):
            transa = 'n'
            lda = (&b[1,0]) - b0 if b.shape[0] > 1 else 1
        elif _is_contiguous(b, 0):
            transa = 't'
            lda = (&b[0,1]) - b0 if b.shape[1] > 1 else 1
        else:
            with gil:
                raise ValueError("Input 'b' is neither C nor Fortran contiguous.")
        k = b.shape[0]
        if k != a.shape[1]:
            with gil:
                raise ValueError("Shape mismatch in input arrays.")
        m = b.shape[1]
        n = a.shape[0]
        if n != c.shape[0] or m != c.shape[1]:
            with gil:
                raise ValueError("Output array does not have the correct shape.")
        ldc = (&c[1,0]) - c0 if c.shape[0] > 1 else 1
        dgemm(transa, transb, &m, &n, &k, &alpha, b0, &lda, a0,
                   &ldb, &beta, c0, &ldc)
    elif _is_contiguous(c, 0):
        if _is_contiguous(a, 1):
            transa = 't'
            lda = (&a[1,0]) - a0 if a.shape[0] > 1 else 1
        elif _is_contiguous(a, 0):
            transa = 'n'
            lda = (&a[0,1]) - a0 if a.shape[1] > 1 else 1
        else:
            with gil:
                raise ValueError("Input 'a' is neither C nor Fortran contiguous.")
        if _is_contiguous(b, 1):
            transb = 't'
            ldb = (&b[1,0]) - b0 if b.shape[0] > 1 else 1
        elif _is_contiguous(b, 0):
            transb = 'n'
            ldb = (&b[0,1]) - b0 if b.shape[1] > 1 else 1
        else:
            with gil:
                raise ValueError("Input 'b' is neither C nor Fortran contiguous.")
        m = a.shape[0]
        k = a.shape[1]
        if k != b.shape[0]:
            with gil:
                raise ValueError("Shape mismatch in input arrays.")
        n = b.shape[1]
        if m != c.shape[0] or n != c.shape[1]:
            with gil:
                raise ValueError("Output array does not have the correct shape.")
        ldc = (&c[0,1]) - c0 if c.shape[1] > 1 else 1
        dgemm(transa, transb, &m, &n, &k, &alpha, a0, &lda, b0,
                   &ldb, &beta, c0, &ldc)
    else:
        with gil:
            raise ValueError("Input 'c' is neither C nor Fortran contiguous.")
    return 0

cpdef double _test_dnrm2(double[:] x) nogil:
    cdef:
        int n = x.shape[0]
        int incx = x.strides[0] // sizeof(x[0])
    return dnrm2(&n, &x[0], &incx)

cpdef double _test_dzasum(double complex[:] zx) nogil:
    cdef:
        int n = zx.shape[0]
        int incx = zx.strides[0] // sizeof(zx[0])
    return dzasum(&n, &zx[0], &incx)

cpdef double _test_dznrm2(double complex[:] x) nogil:
    cdef:
        int n = x.shape[0]
        int incx = x.strides[0] // sizeof(x[0])
    return dznrm2(&n, &x[0], &incx)

cpdef int _test_icamax(float complex[:] cx) nogil:
    cdef:
        int n = cx.shape[0]
        int incx = cx.strides[0] // sizeof(cx[0])
    return icamax(&n, &cx[0], &incx)

cpdef int _test_idamax(double[:] dx) nogil:
    cdef:
        int n = dx.shape[0]
        int incx = dx.strides[0] // sizeof(dx[0])
    return idamax(&n, &dx[0], &incx)

cpdef int _test_isamax(float[:] sx) nogil:
    cdef:
        int n = sx.shape[0]
        int incx = sx.strides[0] // sizeof(sx[0])
    return isamax(&n, &sx[0], &incx)

cpdef int _test_izamax(double complex[:] zx) nogil:
    cdef:
        int n = zx.shape[0]
        int incx = zx.strides[0] // sizeof(zx[0])
    return izamax(&n, &zx[0], &incx)

cpdef float _test_sasum(float[:] sx) nogil:
    cdef:
        int n = sx.shape[0]
        int incx = sx.shape[0] // sizeof(sx[0])
    return sasum(&n, &sx[0], &incx)

cpdef float _test_scasum(float complex[:] cx) nogil:
    cdef:
        int n = cx.shape[0]
        int incx = cx.strides[0] // sizeof(cx[0])
    return scasum(&n, &cx[0], &incx)

cpdef float _test_scnrm2(float complex[:] x) nogil:
    cdef:
        int n = x.shape[0]
        int incx = x.strides[0] // sizeof(x[0])
    return scnrm2(&n, &x[0], &incx)

cpdef float _test_sdot(float[:] sx, float[:] sy) nogil:
    cdef:
        int n = sx.shape[0]
        int incx = sx.strides[0] // sizeof(sx[0])
        int incy = sy.strides[0] // sizeof(sy[0])
    return sdot(&n, &sx[0], &incx, &sy[0], &incy)

cpdef float _test_snrm2(float[:] x) nogil:
    cdef:
        int n = x.shape[0]
        int incx = x.shape[0] // sizeof(x[0])
    return snrm2(&n, &x[0], &incx)

cpdef double complex _test_zdotc(double complex[:] zx, double complex[:] zy) nogil:
    cdef:
        int n = zx.shape[0]
        int incx = zx.strides[0] // sizeof(zx[0])
        int incy = zy.strides[0] // sizeof(zy[0])
    return zdotc(&n, &zx[0], &incx, &zy[0], &incy)

cpdef double complex _test_zdotu(double complex[:] zx, double complex[:] zy) nogil:
    cdef:
        int n = zx.shape[0]
        int incx = zx.strides[0] // sizeof(zx[0])
        int incy = zy.strides[0] // sizeof(zy[0])
    return zdotu(&n, &zx[0], &incx, &zy[0], &incy)
