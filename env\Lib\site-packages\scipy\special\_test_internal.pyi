from typing import <PERSON><PERSON>
import numpy as np

def have_fenv() -> bool: ...
def random_double(size: int) -> np.float64: ...
def test_add_round(size: int, mode: str): ...

def _dd_exp(xhi: float, xlo: float) -> <PERSON><PERSON>[float, float]: ...
def _dd_log(xhi: float, xlo: float) -> <PERSON><PERSON>[float, float]: ...
def _dd_expm1(xhi: float, xlo: float) -> <PERSON><PERSON>[float, float]: ...
