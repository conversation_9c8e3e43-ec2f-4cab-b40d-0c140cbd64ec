# This file is automatically generated by _generate_pyx.py.
# Do not edit manually!

from libc.math cimport NAN

include "_ufuncs_extra_code_common.pxi"

cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double _func_faddeeva_dawsn "faddeeva_dawsn"(double) nogil
cdef void *_export_faddeeva_dawsn = <void*>_func_faddeeva_dawsn
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double complex _func_faddeeva_dawsn_complex "faddeeva_dawsn_complex"(double complex) nogil
cdef void *_export_faddeeva_dawsn_complex = <void*>_func_faddeeva_dawsn_complex
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double _func_fellint_RC "fellint_RC"(double, double) nogil
cdef void *_export_fellint_RC = <void*>_func_fellint_RC
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double complex _func_cellint_RC "cellint_RC"(double complex, double complex) nogil
cdef void *_export_cellint_RC = <void*>_func_cellint_RC
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double _func_fellint_RD "fellint_RD"(double, double, double) nogil
cdef void *_export_fellint_RD = <void*>_func_fellint_RD
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double complex _func_cellint_RD "cellint_RD"(double complex, double complex, double complex) nogil
cdef void *_export_cellint_RD = <void*>_func_cellint_RD
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double _func_fellint_RF "fellint_RF"(double, double, double) nogil
cdef void *_export_fellint_RF = <void*>_func_fellint_RF
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double complex _func_cellint_RF "cellint_RF"(double complex, double complex, double complex) nogil
cdef void *_export_cellint_RF = <void*>_func_cellint_RF
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double _func_fellint_RG "fellint_RG"(double, double, double) nogil
cdef void *_export_fellint_RG = <void*>_func_fellint_RG
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double complex _func_cellint_RG "cellint_RG"(double complex, double complex, double complex) nogil
cdef void *_export_cellint_RG = <void*>_func_cellint_RG
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double _func_fellint_RJ "fellint_RJ"(double, double, double, double) nogil
cdef void *_export_fellint_RJ = <void*>_func_fellint_RJ
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double complex _func_cellint_RJ "cellint_RJ"(double complex, double complex, double complex, double complex) nogil
cdef void *_export_cellint_RJ = <void*>_func_cellint_RJ
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double complex _func_faddeeva_erf "faddeeva_erf"(double complex) nogil
cdef void *_export_faddeeva_erf = <void*>_func_faddeeva_erf
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double complex _func_faddeeva_erfc_complex "faddeeva_erfc_complex"(double complex) nogil
cdef void *_export_faddeeva_erfc_complex = <void*>_func_faddeeva_erfc_complex
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double _func_faddeeva_erfcx "faddeeva_erfcx"(double) nogil
cdef void *_export_faddeeva_erfcx = <void*>_func_faddeeva_erfcx
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double complex _func_faddeeva_erfcx_complex "faddeeva_erfcx_complex"(double complex) nogil
cdef void *_export_faddeeva_erfcx_complex = <void*>_func_faddeeva_erfcx_complex
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double _func_faddeeva_erfi "faddeeva_erfi"(double) nogil
cdef void *_export_faddeeva_erfi = <void*>_func_faddeeva_erfi
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double complex _func_faddeeva_erfi_complex "faddeeva_erfi_complex"(double complex) nogil
cdef void *_export_faddeeva_erfi_complex = <void*>_func_faddeeva_erfi_complex
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef float _func_erfinv_float "erfinv_float"(float) nogil
cdef void *_export_erfinv_float = <void*>_func_erfinv_float
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double _func_erfinv_double "erfinv_double"(double) nogil
cdef void *_export_erfinv_double = <void*>_func_erfinv_double
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double _func_expit "expit"(double) nogil
cdef void *_export_expit = <void*>_func_expit
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef float _func_expitf "expitf"(float) nogil
cdef void *_export_expitf = <void*>_func_expitf
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef long double _func_expitl "expitl"(long double) nogil
cdef void *_export_expitl = <void*>_func_expitl
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double _func_hyp1f1_double "hyp1f1_double"(double, double, double) nogil
cdef void *_export_hyp1f1_double = <void*>_func_hyp1f1_double
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double _func_log_expit "log_expit"(double) nogil
cdef void *_export_log_expit = <void*>_func_log_expit
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef float _func_log_expitf "log_expitf"(float) nogil
cdef void *_export_log_expitf = <void*>_func_log_expitf
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef long double _func_log_expitl "log_expitl"(long double) nogil
cdef void *_export_log_expitl = <void*>_func_log_expitl
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double _func_faddeeva_log_ndtr "faddeeva_log_ndtr"(double) nogil
cdef void *_export_faddeeva_log_ndtr = <void*>_func_faddeeva_log_ndtr
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double complex _func_faddeeva_log_ndtr_complex "faddeeva_log_ndtr_complex"(double complex) nogil
cdef void *_export_faddeeva_log_ndtr_complex = <void*>_func_faddeeva_log_ndtr_complex
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double _func_logit "logit"(double) nogil
cdef void *_export_logit = <void*>_func_logit
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef float _func_logitf "logitf"(float) nogil
cdef void *_export_logitf = <void*>_func_logitf
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef long double _func_logitl "logitl"(long double) nogil
cdef void *_export_logitl = <void*>_func_logitl
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double complex _func_faddeeva_ndtr "faddeeva_ndtr"(double complex) nogil
cdef void *_export_faddeeva_ndtr = <void*>_func_faddeeva_ndtr
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef float _func_powm1_float "powm1_float"(float, float) nogil
cdef void *_export_powm1_float = <void*>_func_powm1_float
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double _func_powm1_double "powm1_double"(double, double) nogil
cdef void *_export_powm1_double = <void*>_func_powm1_double
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double _func_faddeeva_voigt_profile "faddeeva_voigt_profile"(double, double, double) nogil
cdef void *_export_faddeeva_voigt_profile = <void*>_func_faddeeva_voigt_profile
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double complex _func_faddeeva_w "faddeeva_w"(double complex) nogil
cdef void *_export_faddeeva_w = <void*>_func_faddeeva_w
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double complex _func_wrightomega "wrightomega"(double complex) nogil
cdef void *_export_wrightomega = <void*>_func_wrightomega
cdef extern from r"D:\a\scipy\scipy\.mesonpy-347boczx\build\scipy/special\_ufuncs_cxx_defs.h":
    cdef double _func_wrightomega_real "wrightomega_real"(double) nogil
cdef void *_export_wrightomega_real = <void*>_func_wrightomega_real
# distutils: language = c++
