#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/celu_ops.h>

namespace at {


// aten::celu(Tensor self, Scalar alpha=1.0) -> Tensor
inline at::Tensor celu(const at::Tensor & self, const at::Scalar & alpha=1.0) {
    return at::_ops::celu::call(self, alpha);
}

// aten::celu_(Tensor(a!) self, Scalar alpha=1.0) -> Tensor(a!)
inline at::Tensor & celu_(at::Tensor & self, const at::Scalar & alpha=1.0) {
    return at::_ops::celu_::call(self, alpha);
}

// aten::celu.out(Tensor self, Scalar alpha=1.0, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & celu_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & alpha=1.0) {
    return at::_ops::celu_out::call(self, alpha, out);
}
// aten::celu.out(Tensor self, Scalar alpha=1.0, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & celu_outf(const at::Tensor & self, const at::Scalar & alpha, at::Tensor & out) {
    return at::_ops::celu_out::call(self, alpha, out);
}

}
