#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/clamp_min_ops.h>

namespace at {


// aten::clamp_min(Tensor self, Scalar min) -> Tensor
inline at::Tensor clamp_min(const at::Tensor & self, const at::Scalar & min) {
    return at::_ops::clamp_min::call(self, min);
}

// aten::clamp_min.Tensor(Tensor self, Tensor min) -> Tensor
inline at::Tensor clamp_min(const at::Tensor & self, const at::Tensor & min) {
    return at::_ops::clamp_min_Tensor::call(self, min);
}

// aten::clamp_min_(Tensor(a!) self, Scalar min) -> Tensor(a!)
inline at::Tensor & clamp_min_(at::Tensor & self, const at::Scalar & min) {
    return at::_ops::clamp_min_::call(self, min);
}

// aten::clamp_min_.Tensor(Tensor(a!) self, Tensor min) -> Tensor(a!)
inline at::Tensor & clamp_min_(at::Tensor & self, const at::Tensor & min) {
    return at::_ops::clamp_min__Tensor::call(self, min);
}

// aten::clamp_min.out(Tensor self, Scalar min, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & clamp_min_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & min) {
    return at::_ops::clamp_min_out::call(self, min, out);
}
// aten::clamp_min.out(Tensor self, Scalar min, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & clamp_min_outf(const at::Tensor & self, const at::Scalar & min, at::Tensor & out) {
    return at::_ops::clamp_min_out::call(self, min, out);
}

// aten::clamp_min.Tensor_out(Tensor self, Tensor min, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & clamp_min_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & min) {
    return at::_ops::clamp_min_Tensor_out::call(self, min, out);
}
// aten::clamp_min.Tensor_out(Tensor self, Tensor min, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & clamp_min_outf(const at::Tensor & self, const at::Tensor & min, at::Tensor & out) {
    return at::_ops::clamp_min_Tensor_out::call(self, min, out);
}

}
