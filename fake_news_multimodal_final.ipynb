# Installation des packages nécessaires
!pip install torch torchvision transformers pandas numpy matplotlib seaborn
!pip install scikit-learn requests pillow tqdm
!pip install datasets accelerate

# Vérification des installations
try:
    import sklearn
    print(f"✅ scikit-learn version: {sklearn.__version__}")
except ImportError:
    print("❌ Erreur: scikit-learn non installé")
    !pip install --upgrade scikit-learn

try:
    import torch
    print(f"✅ PyTorch version: {torch.__version__}")
except ImportError:
    print("❌ Erreur: PyTorch non installé")
    !pip install --upgrade torch torchvision

try:
    import transformers
    print(f"✅ Transformers version: {transformers.__version__}")
except ImportError:
    print("❌ Erreur: Transformers non installé")
    !pip install --upgrade transformers

# Imports de base
import pandas as pd
import numpy as np
import os
import glob
import hashlib
import requests
from PIL import Image
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Vérification et import de PyTorch
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import Dataset, DataLoader
    import torchvision.transforms as transforms
    from torchvision.models import resnet18
    print(f"✅ PyTorch importé avec succès")
except ImportError as e:
    print(f"❌ Erreur PyTorch: {e}")
    !pip install torch torchvision
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import Dataset, DataLoader
    import torchvision.transforms as transforms
    from torchvision.models import resnet18

# Vérification et import de Transformers
try:
    from transformers import AutoTokenizer, AutoModel
    print(f"✅ Transformers importé avec succès")
except ImportError as e:
    print(f"❌ Erreur Transformers: {e}")
    !pip install transformers
    from transformers import AutoTokenizer, AutoModel

# Vérification et import de Sklearn
try:
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix
    from sklearn.utils import resample
    print(f"✅ Scikit-learn importé avec succès")
except ImportError as e:
    print(f"❌ Erreur Scikit-learn: {e}")
    !pip install scikit-learn
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix
    from sklearn.utils import resample

# Configuration
try:
    plt.style.use('seaborn-v0_8')
except:
    plt.style.use('seaborn')

sns.set_palette("husl")
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"\n🖥️ Device utilisé: {device}")
if torch.cuda.is_available():
    print(f"🚀 GPU détecté: {torch.cuda.get_device_name(0)}")
else:
    print(f"💻 Utilisation du CPU")

def load_all_csv_files(data_folder='FakeNewsNetData'):
    """
    Charge tous les fichiers CSV du dossier et assigne automatiquement les labels
    selon le nom du fichier (fake = 0, real = 1)
    """
    all_dataframes = []
    csv_files = glob.glob(os.path.join(data_folder, '*.csv'))

    print(f"📁 Fichiers CSV trouvés: {len(csv_files)}")

    for file_path in csv_files:
        filename = os.path.basename(file_path)
        print(f"\n📄 Traitement de: {filename}")

        try:
            # Lecture du fichier CSV
            df = pd.read_csv(file_path)

            # Détection automatique du label selon le nom du fichier
            if 'fake' in filename.lower():
                df['label'] = 0  # Fake news
                label_type = "FAKE"
            elif 'real' in filename.lower():
                df['label'] = 1  # Real news
                label_type = "REAL"
            else:
                print(f"⚠️ Impossible de déterminer le label pour {filename}")
                continue

            # Ajout de la source du fichier
            df['source_file'] = filename

            print(f"   ✅ {len(df)} articles chargés - Label: {label_type}")
            print(f"   📊 Colonnes: {list(df.columns)}")

            all_dataframes.append(df)

        except Exception as e:
            print(f"   ❌ Erreur lors du chargement de {filename}: {str(e)}")

    # Fusion de tous les DataFrames
    if all_dataframes:
        df_all = pd.concat(all_dataframes, ignore_index=True, sort=False)
        print(f"\n🎯 TOTAL: {len(df_all)} articles chargés")
        print(f"📈 Répartition des labels:")
        print(df_all['label'].value_counts())
        return df_all
    else:
        print("❌ Aucun fichier n'a pu être chargé")
        return None

# Chargement des données
df_all = load_all_csv_files()
print(f"\n📋 Colonnes disponibles: {list(df_all.columns)}")
print(f"📏 Shape du dataset: {df_all.shape}")

def preprocess_data(df):
    """
    Prétraitement complet des données:
    - Suppression des lignes avec title/text manquants
    - Création d'une colonne image_url unifiée
    - Nettoyage des données
    """
    print("🧹 Début du prétraitement...")
    initial_count = len(df)

    # 1. Suppression des lignes avec title ou text manquants
    print(f"\n1️⃣ Suppression des lignes avec title/text manquants...")
    df = df.dropna(subset=['title'])
    print(f"   Après suppression title manquant: {len(df)} articles")

    # Gestion de la colonne text (peut être 'text' ou autre selon le fichier)
    text_columns = [col for col in df.columns if 'text' in col.lower()]
    if text_columns:
        main_text_col = text_columns[0]
        df = df.dropna(subset=[main_text_col])
        # Renommer la colonne en 'text' pour uniformiser
        if main_text_col != 'text':
            df = df.rename(columns={main_text_col: 'text'})
        print(f"   Après suppression text manquant: {len(df)} articles")

    # 2. Création d'une colonne image_url unifiée
    print(f"\n2️⃣ Création de la colonne image_url unifiée...")

    # Initialiser la colonne image_url
    df['image_url'] = None

    # Priorité: image_url > top_img > images
    if 'image_url' in df.columns and df['image_url'].notna().any():
        df['image_url'] = df['image_url'].fillna('')

    if 'top_img' in df.columns:
        df['image_url'] = df['image_url'].fillna(df['top_img'])

    if 'images' in df.columns:
        # Extraire la première image de la liste si c'est une chaîne
        def extract_first_image(images_str):
            if pd.isna(images_str) or images_str == '':
                return None
            # Si c'est une liste d'URLs séparées par des espaces ou virgules
            images = str(images_str).split()
            return images[0] if images else None

        df['temp_images'] = df['images'].apply(extract_first_image)
        df['image_url'] = df['image_url'].fillna(df['temp_images'])
        df = df.drop('temp_images', axis=1)

    # Nettoyage des URLs d'images
    df['image_url'] = df['image_url'].astype(str)
    df['image_url'] = df['image_url'].replace(['nan', 'None', ''], None)

    # Filtrer les URLs valides
    def is_valid_image_url(url):
        if pd.isna(url) or url is None:
            return False
        url = str(url).strip()
        return url.startswith(('http://', 'https://')) and len(url) > 10

    df['has_valid_image_url'] = df['image_url'].apply(is_valid_image_url)

    print(f"   Articles avec URL d'image valide: {df['has_valid_image_url'].sum()}")
    print(f"   Articles sans URL d'image: {(~df['has_valid_image_url']).sum()}")

    # 3. Nettoyage des textes
    print(f"\n3️⃣ Nettoyage des textes...")

    def clean_text(text):
        if pd.isna(text):
            return ""
        text = str(text).strip()
        # Supprimer les caractères de contrôle
        text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\t')
        return text

    df['title'] = df['title'].apply(clean_text)
    df['text'] = df['text'].apply(clean_text)

    # Supprimer les articles avec des textes trop courts
    df = df[df['title'].str.len() >= 10]
    df = df[df['text'].str.len() >= 50]

    print(f"   Après nettoyage des textes: {len(df)} articles")

    print(f"\n✅ Prétraitement terminé: {initial_count} → {len(df)} articles ({len(df)/initial_count*100:.1f}%)")

    return df

# Prétraitement des données
df_clean = preprocess_data(df_all)
print(f"\n📊 Répartition finale des labels:")
print(df_clean['label'].value_counts())

def download_images(df, images_folder='images_final', max_images=None):
    """
    Télécharge toutes les images valides et met à jour le DataFrame
    avec les chemins locaux des images téléchargées
    """
    print(f"📥 Début du téléchargement des images dans {images_folder}/")

    # Créer le dossier d'images
    os.makedirs(images_folder, exist_ok=True)

    # Filtrer les articles avec des URLs d'images valides
    df_with_images = df[df['has_valid_image_url']].copy()

    if max_images:
        df_with_images = df_with_images.head(max_images)

    print(f"🎯 {len(df_with_images)} images à télécharger")

    successful_downloads = []
    failed_downloads = []

    for idx, row in tqdm(df_with_images.iterrows(), total=len(df_with_images), desc="Téléchargement"):
        try:
            image_url = row['image_url']

            # Créer un nom de fichier unique avec hash MD5
            url_hash = hashlib.md5(image_url.encode()).hexdigest()

            # Déterminer l'extension du fichier
            if image_url.lower().endswith(('.jpg', '.jpeg')):
                ext = '.jpg'
            elif image_url.lower().endswith('.png'):
                ext = '.png'
            elif image_url.lower().endswith('.gif'):
                ext = '.gif'
            else:
                ext = '.jpg'  # Par défaut

            filename = f"{url_hash}{ext}"
            filepath = os.path.join(images_folder, filename)

            # Vérifier si l'image existe déjà
            if os.path.exists(filepath):
                successful_downloads.append((idx, filepath))
                continue

            # Télécharger l'image
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(image_url, headers=headers, timeout=10, stream=True)
            response.raise_for_status()

            # Vérifier que c'est bien une image
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                failed_downloads.append((idx, f"Type de contenu invalide: {content_type}"))
                continue

            # Sauvegarder l'image
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            # Vérifier que l'image peut être ouverte
            try:
                with Image.open(filepath) as img:
                    img.verify()
                successful_downloads.append((idx, filepath))
            except Exception:
                os.remove(filepath)
                failed_downloads.append((idx, "Image corrompue"))

        except Exception as e:
            failed_downloads.append((idx, str(e)))

    print(f"\n✅ Téléchargements réussis: {len(successful_downloads)}")
    print(f"❌ Téléchargements échoués: {len(failed_downloads)}")

    # Mettre à jour le DataFrame avec les chemins des images
    df['image_path'] = None
    for idx, filepath in successful_downloads:
        df.loc[idx, 'image_path'] = filepath

    # Filtrer pour ne garder que les articles avec images téléchargées
    df_final = df[df['image_path'].notna()].copy()

    print(f"\n🎯 Dataset final: {len(df_final)} articles avec images")
    print(f"📊 Répartition des labels:")
    print(df_final['label'].value_counts())

    return df_final

# Téléchargement des images (limité à 1000 pour les tests)
df_with_images = download_images(df_clean, max_images=1000)

def balance_dataset(df, min_samples=200, method='undersample'):
    """
    Équilibre le dataset selon la méthode choisie
    """
    print(f"⚖️ Équilibrage du dataset (méthode: {method})")

    label_counts = df['label'].value_counts()
    print(f"\n📊 Répartition actuelle:")
    print(label_counts)

    # Vérifier si nous avons assez de données
    if label_counts.min() < min_samples:
        print(f"\n⚠️ Pas assez de données (minimum: {min_samples} par classe)")
        print(f"🔄 Application de l'oversampling...")

        # Oversampling de la classe minoritaire
        df_majority = df[df['label'] == label_counts.idxmax()]
        df_minority = df[df['label'] == label_counts.idxmin()]

        # Calculer le nombre d'échantillons à générer
        target_size = max(min_samples, len(df_majority))

        # Oversampling avec remplacement
        df_minority_upsampled = resample(df_minority,
                                       replace=True,
                                       n_samples=target_size,
                                       random_state=42)

        df_balanced = pd.concat([df_majority, df_minority_upsampled])

    elif method == 'undersample':
        # Undersampling - prendre le minimum
        min_count = label_counts.min()

        df_fake = df[df['label'] == 0].sample(n=min_count, random_state=42)
        df_real = df[df['label'] == 1].sample(n=min_count, random_state=42)

        df_balanced = pd.concat([df_fake, df_real])

    elif method == 'oversample':
        # Oversampling - égaliser au maximum
        max_count = label_counts.max()

        df_majority = df[df['label'] == label_counts.idxmax()]
        df_minority = df[df['label'] == label_counts.idxmin()]

        df_minority_upsampled = resample(df_minority,
                                       replace=True,
                                       n_samples=max_count,
                                       random_state=42)

        df_balanced = pd.concat([df_majority, df_minority_upsampled])

    # Mélanger les données
    df_balanced = df_balanced.sample(frac=1, random_state=42).reset_index(drop=True)

    print(f"\n✅ Dataset équilibré:")
    print(df_balanced['label'].value_counts())
    print(f"📏 Taille finale: {len(df_balanced)} articles")

    return df_balanced

# Équilibrage du dataset
df_balanced = balance_dataset(df_with_images, min_samples=200, method='oversample')

def exploratory_analysis(df):
    """
    Analyse exploratoire complète du dataset
    """
    print("📊 ANALYSE EXPLORATOIRE DES DONNÉES")
    print("=" * 50)

    # 1. Statistiques générales
    print(f"\n1️⃣ STATISTIQUES GÉNÉRALES")
    print(f"   📏 Nombre total d'articles: {len(df)}")
    print(f"   📊 Répartition des labels:")
    label_counts = df['label'].value_counts()
    for label, count in label_counts.items():
        label_name = "FAKE" if label == 0 else "REAL"
        percentage = count / len(df) * 100
        print(f"      {label_name}: {count} ({percentage:.1f}%)")

    # 2. Analyse des textes
    print(f"\n2️⃣ ANALYSE DES TEXTES")

    # Longueurs des titres
    df['title_length'] = df['title'].str.len()
    df['text_length'] = df['text'].str.len()
    df['title_words'] = df['title'].str.split().str.len()
    df['text_words'] = df['text'].str.split().str.len()

    print(f"   📝 Longueur des titres (caractères):")
    print(f"      Moyenne: {df['title_length'].mean():.1f}")
    print(f"      Médiane: {df['title_length'].median():.1f}")
    print(f"      Min-Max: {df['title_length'].min()}-{df['title_length'].max()}")

    print(f"   📄 Longueur des textes (caractères):")
    print(f"      Moyenne: {df['text_length'].mean():.1f}")
    print(f"      Médiane: {df['text_length'].median():.1f}")
    print(f"      Min-Max: {df['text_length'].min()}-{df['text_length'].max()}")

    # 3. Exemples d'articles
    print(f"\n3️⃣ EXEMPLES D'ARTICLES")

    print(f"\n🔴 EXEMPLE FAKE NEWS:")
    fake_example = df[df['label'] == 0].iloc[0]
    print(f"   Titre: {fake_example['title'][:100]}...")
    print(f"   Texte: {fake_example['text'][:200]}...")
    print(f"   Image: {fake_example['image_path']}")

    print(f"\n🟢 EXEMPLE REAL NEWS:")
    real_example = df[df['label'] == 1].iloc[0]
    print(f"   Titre: {real_example['title'][:100]}...")
    print(f"   Texte: {real_example['text'][:200]}...")
    print(f"   Image: {real_example['image_path']}")

    # 4. Visualisations
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # Distribution des labels
    label_counts.plot(kind='bar', ax=axes[0,0], color=['red', 'green'])
    axes[0,0].set_title('Répartition des Labels')
    axes[0,0].set_xlabel('Label (0=Fake, 1=Real)')
    axes[0,0].set_ylabel('Nombre d\'articles')

    # Distribution des longueurs de titres par label
    df.boxplot(column='title_length', by='label', ax=axes[0,1])
    axes[0,1].set_title('Longueur des Titres par Label')
    axes[0,1].set_xlabel('Label (0=Fake, 1=Real)')
    axes[0,1].set_ylabel('Longueur (caractères)')

    # Distribution des longueurs de textes par label
    df.boxplot(column='text_length', by='label', ax=axes[1,0])
    axes[1,0].set_title('Longueur des Textes par Label')
    axes[1,0].set_xlabel('Label (0=Fake, 1=Real)')
    axes[1,0].set_ylabel('Longueur (caractères)')

    # Histogramme des longueurs de titres
    df[df['label']==0]['title_length'].hist(alpha=0.5, label='Fake', bins=30, ax=axes[1,1], color='red')
    df[df['label']==1]['title_length'].hist(alpha=0.5, label='Real', bins=30, ax=axes[1,1], color='green')
    axes[1,1].set_title('Distribution des Longueurs de Titres')
    axes[1,1].set_xlabel('Longueur (caractères)')
    axes[1,1].set_ylabel('Fréquence')
    axes[1,1].legend()

    plt.tight_layout()
    plt.show()

    return df

# Analyse exploratoire
df_analyzed = exploratory_analysis(df_balanced)

# Configuration des modèles
BERT_MODEL = 'distilbert-base-uncased'
MAX_LENGTH = 512
BATCH_SIZE = 16
IMAGE_SIZE = 224

# Initialisation du tokenizer BERT
tokenizer = AutoTokenizer.from_pretrained(BERT_MODEL)
bert_model = AutoModel.from_pretrained(BERT_MODEL)

print(f"✅ Modèle BERT chargé: {BERT_MODEL}")
print(f"🖼️ Taille des images: {IMAGE_SIZE}x{IMAGE_SIZE}")
print(f"📦 Taille des batches: {BATCH_SIZE}")

class MultimodalDataset(Dataset):
    """
    Dataset personnalisé pour les données multimodales (texte + image)
    """

    def __init__(self, df, tokenizer, max_length=512, image_size=224, transform=None):
        self.df = df.reset_index(drop=True)
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.image_size = image_size

        # Transformations pour les images
        if transform is None:
            self.transform = transforms.Compose([
                transforms.Resize((image_size, image_size)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                   std=[0.229, 0.224, 0.225])
            ])
        else:
            self.transform = transform

    def __len__(self):
        return len(self.df)

    def __getitem__(self, idx):
        row = self.df.iloc[idx]

        # Préparation du texte (titre + texte)
        text = f"{row['title']} [SEP] {row['text']}"

        # Tokenisation
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )

        # Chargement et transformation de l'image
        try:
            image = Image.open(row['image_path']).convert('RGB')
            image = self.transform(image)
        except Exception as e:
            # Image par défaut en cas d'erreur
            image = torch.zeros(3, self.image_size, self.image_size)

        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'image': image,
            'label': torch.tensor(row['label'], dtype=torch.long)
        }

print("✅ Classe MultimodalDataset définie")

class MultimodalFakeNewsDetector(nn.Module):
    """
    Modèle multimodal combinant BERT pour le texte et ResNet pour les images
    """

    def __init__(self, bert_model_name='distilbert-base-uncased', num_classes=2, dropout=0.3):
        super(MultimodalFakeNewsDetector, self).__init__()

        # Encodeur de texte (BERT)
        self.bert = AutoModel.from_pretrained(bert_model_name)
        self.bert_dropout = nn.Dropout(dropout)

        # Encodeur d'image (ResNet18 pré-entraîné)
        self.resnet = resnet18(pretrained=True)
        # Supprimer la dernière couche de classification
        self.resnet = nn.Sequential(*list(self.resnet.children())[:-1])
        self.image_dropout = nn.Dropout(dropout)

        # Dimensions des features
        self.bert_dim = 768  # DistilBERT hidden size
        self.image_dim = 512  # ResNet18 feature size

        # Couches de fusion
        self.fusion_dim = 256
        self.text_projection = nn.Linear(self.bert_dim, self.fusion_dim)
        self.image_projection = nn.Linear(self.image_dim, self.fusion_dim)

        # Couches de classification
        self.classifier = nn.Sequential(
            nn.Linear(self.fusion_dim * 2, 128),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, num_classes)
        )

    def forward(self, input_ids, attention_mask, image):
        # Encodage du texte avec BERT
        bert_output = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        text_features = bert_output.last_hidden_state[:, 0, :]  # [CLS] token
        text_features = self.bert_dropout(text_features)

        # Encodage de l'image avec ResNet
        image_features = self.resnet(image)
        image_features = image_features.view(image_features.size(0), -1)  # Flatten
        image_features = self.image_dropout(image_features)

        # Projection vers l'espace de fusion
        text_projected = self.text_projection(text_features)
        image_projected = self.image_projection(image_features)

        # Fusion des modalités (concaténation)
        fused_features = torch.cat([text_projected, image_projected], dim=1)

        # Classification
        output = self.classifier(fused_features)

        return output

print("✅ Modèle MultimodalFakeNewsDetector défini")

# Séparation des données en train/test
train_df, test_df = train_test_split(
    df_analyzed,
    test_size=0.2,
    random_state=42,
    stratify=df_analyzed['label']
)

print(f"📊 Données d'entraînement: {len(train_df)} articles")
print(f"📊 Données de test: {len(test_df)} articles")
print(f"\n🎯 Répartition train:")
print(train_df['label'].value_counts())
print(f"\n🎯 Répartition test:")
print(test_df['label'].value_counts())

# Création des datasets
train_dataset = MultimodalDataset(train_df, tokenizer, MAX_LENGTH, IMAGE_SIZE)
test_dataset = MultimodalDataset(test_df, tokenizer, MAX_LENGTH, IMAGE_SIZE)

# Création des dataloaders
train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True)
test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False)

print(f"\n✅ Dataloaders créés")
print(f"   📦 Batches d'entraînement: {len(train_loader)}")
print(f"   📦 Batches de test: {len(test_loader)}")

def train_model(model, train_loader, test_loader, num_epochs=5, learning_rate=2e-5):
    """
    Entraîne le modèle multimodal
    """
    print(f"🚀 Début de l'entraînement sur {device}")
    print(f"📊 Paramètres: {num_epochs} epochs, LR={learning_rate}")

    # Déplacer le modèle sur le device
    model = model.to(device)

    # Optimiseur et fonction de perte
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=0.01)
    criterion = nn.CrossEntropyLoss()

    # Scheduler pour ajuster le learning rate
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=2, gamma=0.5)

    # Historique des métriques
    train_losses = []
    train_accuracies = []
    val_losses = []
    val_accuracies = []

    best_val_acc = 0.0

    for epoch in range(num_epochs):
        print(f"\n📅 Epoch {epoch+1}/{num_epochs}")
        print("-" * 50)

        # Phase d'entraînement
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0

        train_pbar = tqdm(train_loader, desc="Entraînement")
        for batch in train_pbar:
            # Déplacer les données sur le device
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            images = batch['image'].to(device)
            labels = batch['label'].to(device)

            # Forward pass
            optimizer.zero_grad()
            outputs = model(input_ids, attention_mask, images)
            loss = criterion(outputs, labels)

            # Backward pass
            loss.backward()
            optimizer.step()

            # Statistiques
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()

            # Mise à jour de la barre de progression
            train_acc = 100 * train_correct / train_total
            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{train_acc:.2f}%'
            })

        # Moyennes pour l'epoch
        avg_train_loss = train_loss / len(train_loader)
        avg_train_acc = 100 * train_correct / train_total

        # Phase de validation
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0

        with torch.no_grad():
            val_pbar = tqdm(test_loader, desc="Validation")
            for batch in val_pbar:
                input_ids = batch['input_ids'].to(device)
                attention_mask = batch['attention_mask'].to(device)
                images = batch['image'].to(device)
                labels = batch['label'].to(device)

                outputs = model(input_ids, attention_mask, images)
                loss = criterion(outputs, labels)

                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()

                val_acc = 100 * val_correct / val_total
                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Acc': f'{val_acc:.2f}%'
                })

        avg_val_loss = val_loss / len(test_loader)
        avg_val_acc = 100 * val_correct / val_total

        # Sauvegarde du meilleur modèle
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            torch.save(model.state_dict(), 'best_multimodal_model.pth')
            print(f"💾 Nouveau meilleur modèle sauvegardé (Acc: {best_val_acc:.2f}%)")

        # Mise à jour du scheduler
        scheduler.step()

        # Stockage des métriques
        train_losses.append(avg_train_loss)
        train_accuracies.append(avg_train_acc)
        val_losses.append(avg_val_loss)
        val_accuracies.append(avg_val_acc)

        # Affichage des résultats de l'epoch
        print(f"\n📊 Résultats Epoch {epoch+1}:")
        print(f"   🏋️ Train - Loss: {avg_train_loss:.4f}, Acc: {avg_train_acc:.2f}%")
        print(f"   🎯 Val   - Loss: {avg_val_loss:.4f}, Acc: {avg_val_acc:.2f}%")
        print(f"   📈 LR: {scheduler.get_last_lr()[0]:.2e}")

    print(f"\n🎉 Entraînement terminé!")
    print(f"🏆 Meilleure accuracy de validation: {best_val_acc:.2f}%")

    return {
        'train_losses': train_losses,
        'train_accuracies': train_accuracies,
        'val_losses': val_losses,
        'val_accuracies': val_accuracies,
        'best_val_acc': best_val_acc
    }

# Initialisation et entraînement du modèle
model = MultimodalFakeNewsDetector(BERT_MODEL, num_classes=2, dropout=0.3)
print(f"\n🧠 Modèle initialisé")
print(f"📊 Nombre de paramètres: {sum(p.numel() for p in model.parameters()):,}")

# Entraînement
history = train_model(model, train_loader, test_loader, num_epochs=3, learning_rate=2e-5)

def plot_training_history(history):
    """
    Affiche les courbes de loss et d'accuracy
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

    epochs = range(1, len(history['train_losses']) + 1)

    # Courbes de loss
    ax1.plot(epochs, history['train_losses'], 'b-', label='Train Loss', marker='o')
    ax1.plot(epochs, history['val_losses'], 'r-', label='Validation Loss', marker='s')
    ax1.set_title('Évolution de la Loss')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True)

    # Courbes d'accuracy
    ax2.plot(epochs, history['train_accuracies'], 'b-', label='Train Accuracy', marker='o')
    ax2.plot(epochs, history['val_accuracies'], 'r-', label='Validation Accuracy', marker='s')
    ax2.set_title('Évolution de l\'Accuracy')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy (%)')
    ax2.legend()
    ax2.grid(True)

    plt.tight_layout()
    plt.show()

    # Affichage des métriques finales
    print(f"📊 RÉSULTATS FINAUX:")
    print(f"   🏋️ Train Accuracy: {history['train_accuracies'][-1]:.2f}%")
    print(f"   🎯 Validation Accuracy: {history['val_accuracies'][-1]:.2f}%")
    print(f"   🏆 Meilleure Val Accuracy: {history['best_val_acc']:.2f}%")

# Affichage des courbes
plot_training_history(history)

def evaluate_model(model, test_loader, class_names=['Fake', 'Real']):
    """
    Évaluation complète du modèle avec métriques détaillées
    """
    print("🔍 ÉVALUATION DÉTAILLÉE DU MODÈLE")
    print("=" * 50)

    # Charger le meilleur modèle
    model.load_state_dict(torch.load('best_multimodal_model.pth'))
    model.eval()
    model = model.to(device)

    all_predictions = []
    all_labels = []
    all_probabilities = []

    print("📊 Prédiction sur le jeu de test...")

    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Évaluation"):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            images = batch['image'].to(device)
            labels = batch['label'].to(device)

            outputs = model(input_ids, attention_mask, images)
            probabilities = torch.softmax(outputs, dim=1)
            _, predicted = torch.max(outputs, 1)

            all_predictions.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_probabilities.extend(probabilities.cpu().numpy())

    # Conversion en arrays numpy
    y_true = np.array(all_labels)
    y_pred = np.array(all_predictions)
    y_prob = np.array(all_probabilities)

    # Calcul des métriques
    accuracy = accuracy_score(y_true, y_pred)
    f1 = f1_score(y_true, y_pred, average='weighted')

    print(f"\n📈 MÉTRIQUES GLOBALES:")
    print(f"   🎯 Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"   📊 F1-Score: {f1:.4f}")

    # Rapport de classification détaillé
    print(f"\n📋 RAPPORT DE CLASSIFICATION:")
    print(classification_report(y_true, y_pred, target_names=class_names))

    # Matrice de confusion
    cm = confusion_matrix(y_true, y_pred)

    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names)
    plt.title('Matrice de Confusion')
    plt.xlabel('Prédictions')
    plt.ylabel('Vraies étiquettes')
    plt.show()

    # Analyse des erreurs
    print(f"\n🔍 ANALYSE DES ERREURS:")

    # Faux positifs (prédits comme Real mais sont Fake)
    false_positives = (y_true == 0) & (y_pred == 1)
    fp_count = np.sum(false_positives)

    # Faux négatifs (prédits comme Fake mais sont Real)
    false_negatives = (y_true == 1) & (y_pred == 0)
    fn_count = np.sum(false_negatives)

    print(f"   🔴 Faux Positifs (Fake → Real): {fp_count}")
    print(f"   🟡 Faux Négatifs (Real → Fake): {fn_count}")

    # Distribution des probabilités
    plt.figure(figsize=(12, 4))

    plt.subplot(1, 2, 1)
    plt.hist(y_prob[y_true == 0, 0], alpha=0.5, label='Fake (vraies)', bins=20, color='red')
    plt.hist(y_prob[y_true == 1, 0], alpha=0.5, label='Real (vraies)', bins=20, color='green')
    plt.xlabel('Probabilité de Fake')
    plt.ylabel('Fréquence')
    plt.title('Distribution des Probabilités - Classe Fake')
    plt.legend()

    plt.subplot(1, 2, 2)
    plt.hist(y_prob[y_true == 0, 1], alpha=0.5, label='Fake (vraies)', bins=20, color='red')
    plt.hist(y_prob[y_true == 1, 1], alpha=0.5, label='Real (vraies)', bins=20, color='green')
    plt.xlabel('Probabilité de Real')
    plt.ylabel('Fréquence')
    plt.title('Distribution des Probabilités - Classe Real')
    plt.legend()

    plt.tight_layout()
    plt.show()

    return {
        'accuracy': accuracy,
        'f1_score': f1,
        'y_true': y_true,
        'y_pred': y_pred,
        'y_prob': y_prob,
        'confusion_matrix': cm
    }

# Évaluation du modèle
evaluation_results = evaluate_model(model, test_loader)

def predict_fake_news(model, tokenizer, title, text, image_path, device=device):
    """
    Prédit si un article est fake ou real à partir du titre, texte et image
    """
    model.eval()

    # Préparation du texte
    combined_text = f"{title} [SEP] {text}"

    # Tokenisation
    encoding = tokenizer(
        combined_text,
        truncation=True,
        padding='max_length',
        max_length=MAX_LENGTH,
        return_tensors='pt'
    )

    # Préparation de l'image
    transform = transforms.Compose([
        transforms.Resize((IMAGE_SIZE, IMAGE_SIZE)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406],
                           std=[0.229, 0.224, 0.225])
    ])

    try:
        image = Image.open(image_path).convert('RGB')
        image = transform(image).unsqueeze(0)  # Ajouter dimension batch
    except Exception as e:
        print(f"⚠️ Erreur lors du chargement de l'image: {e}")
        image = torch.zeros(1, 3, IMAGE_SIZE, IMAGE_SIZE)

    # Déplacer sur le device
    input_ids = encoding['input_ids'].to(device)
    attention_mask = encoding['attention_mask'].to(device)
    image = image.to(device)

    # Prédiction
    with torch.no_grad():
        outputs = model(input_ids, attention_mask, image)
        probabilities = torch.softmax(outputs, dim=1)
        _, predicted = torch.max(outputs, 1)

    # Résultats
    prediction = predicted.item()
    fake_prob = probabilities[0][0].item()
    real_prob = probabilities[0][1].item()

    return {
        'prediction': prediction,
        'label': 'REAL' if prediction == 1 else 'FAKE',
        'fake_probability': fake_prob,
        'real_probability': real_prob,
        'confidence': max(fake_prob, real_prob)
    }

def test_article_prediction():
    """
    Teste la prédiction sur quelques articles du jeu de test
    """
    print("🧪 TEST DE PRÉDICTION EN TEMPS RÉEL")
    print("=" * 50)

    # Charger le meilleur modèle
    model.load_state_dict(torch.load('best_multimodal_model.pth'))

    # Prendre quelques exemples du jeu de test
    test_samples = test_df.sample(n=3, random_state=42)

    for idx, (_, row) in enumerate(test_samples.iterrows()):
        print(f"\n📰 ARTICLE {idx+1}:")
        print(f"   📝 Titre: {row['title'][:100]}...")
        print(f"   📄 Texte: {row['text'][:150]}...")
        print(f"   🖼️ Image: {row['image_path']}")
        print(f"   🏷️ Vraie étiquette: {'REAL' if row['label'] == 1 else 'FAKE'}")

        # Prédiction
        result = predict_fake_news(
            model, tokenizer,
            row['title'], row['text'], row['image_path']
        )

        print(f"\n🤖 PRÉDICTION:")
        print(f"   🎯 Prédiction: {result['label']}")
        print(f"   📊 Confiance: {result['confidence']:.3f}")
        print(f"   🔴 Prob. Fake: {result['fake_probability']:.3f}")
        print(f"   🟢 Prob. Real: {result['real_probability']:.3f}")

        # Vérification
        true_label = 'REAL' if row['label'] == 1 else 'FAKE'
        is_correct = result['label'] == true_label
        print(f"   ✅ Correct: {'OUI' if is_correct else 'NON'}")

        print("-" * 50)

# Test de prédiction
test_article_prediction()

def predict_custom_article(title, text, image_path):
    """
    Interface simple pour tester un article personnalisé
    """
    print("🔍 ANALYSE D'ARTICLE PERSONNALISÉ")
    print("=" * 50)

    print(f"📝 Titre: {title}")
    print(f"📄 Texte: {text[:200]}{'...' if len(text) > 200 else ''}")
    print(f"🖼️ Image: {image_path}")

    # Charger le modèle
    model.load_state_dict(torch.load('best_multimodal_model.pth'))

    # Prédiction
    result = predict_fake_news(model, tokenizer, title, text, image_path)

    print(f"\n🤖 RÉSULTAT DE L'ANALYSE:")
    print(f"   🎯 Classification: {result['label']}")
    print(f"   📊 Niveau de confiance: {result['confidence']:.1%}")

    if result['label'] == 'FAKE':
        print(f"   🔴 Probabilité d'être FAKE: {result['fake_probability']:.1%}")
        print(f"   ⚠️ Cet article semble être une FAKE NEWS")
    else:
        print(f"   🟢 Probabilité d'être REAL: {result['real_probability']:.1%}")
        print(f"   ✅ Cet article semble être une VRAIE NEWS")

    return result

# Exemple d'utilisation (décommentez et modifiez selon vos besoins)
# result = predict_custom_article(
#     title="Votre titre d'article ici",
#     text="Le contenu complet de votre article ici...",
#     image_path="chemin/vers/votre/image.jpg"
# )

print("\n🎉 NOTEBOOK TERMINÉ!")
print("\n📋 RÉSUMÉ DU PROJET:")
print(f"   ✅ Données chargées et prétraitées")
print(f"   ✅ Images téléchargées et traitées")
print(f"   ✅ Modèle multimodal entraîné (BERT + ResNet)")
print(f"   ✅ Évaluation complète effectuée")
print(f"   ✅ Interface de test disponible")
print(f"\n🚀 Vous pouvez maintenant utiliser predict_custom_article() pour tester vos propres articles!")

