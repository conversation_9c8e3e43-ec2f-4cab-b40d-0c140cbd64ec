# Installation des packages nécessaires
!pip install torch torchvision transformers pandas numpy matplotlib seaborn
!pip install scikit-learn requests pillow tqdm
!pip install imbalanced-learn  # Pour SMOTE
!pip install datasets accelerate

# Vérification spécifique pour imbalanced-learn
try:
    import imblearn
    print(f"✅ imbalanced-learn version: {imblearn.__version__}")
except ImportError:
    print("❌ Installation d'imbalanced-learn...")
    !pip install --upgrade imbalanced-learn
    import imblearn
    print(f"✅ imbalanced-learn installé: {imblearn.__version__}")

print("✅ Installation terminée")

import pandas as pd
import numpy as np
import os
import glob
import hashlib
import requests
from PIL import Image
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# PyTorch
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, TensorDataset
import torchvision.transforms as transforms
from torchvision.models import resnet18

# Transformers
from transformers import AutoTokenizer, AutoModel

# Sklearn et SMOTE
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler
# Import sécurisé d'imbalanced-learn
try:
    from imblearn.over_sampling import SMOTE
    from imblearn.combine import SMOTETomek
    print("✅ SMOTE importé avec succès")
except ImportError as e:
    print(f"❌ Erreur import SMOTE: {e}")
    print("🔄 Installation d'imbalanced-learn...")
    !pip install --upgrade imbalanced-learn
    from imblearn.over_sampling import SMOTE
    from imblearn.combine import SMOTETomek
    print("✅ SMOTE importé après installation")

# Configuration
plt.style.use('default')
sns.set_palette("husl")
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"🖥️ Device: {device}")

# Paramètres globaux
BERT_MODEL = 'distilbert-base-uncased'
MAX_LENGTH = 512
IMAGE_SIZE = 224
BATCH_SIZE = 16

import os
import glob
import pandas as pd

def load_and_preprocess_data(data_folder='FakeNewsNetData'):
    """
    Charge toutes les données CSV sans filtrage strict.
    Nettoie les colonnes, garde le maximum d'articles possibles.
    """
    print("📁 CHARGEMENT DES DONNÉES")
    print("=" * 40)

    all_dfs = []
    csv_files = glob.glob(os.path.join(data_folder, '*.csv'))

    for file_path in csv_files:
        filename = os.path.basename(file_path)
        print(f"📄 {filename}")

        try:
            df = pd.read_csv(file_path)

            # Détection du label
            if 'fake' in filename.lower():
                df['label'] = 0
                label_type = "FAKE"
            elif 'real' in filename.lower():
                df['label'] = 1
                label_type = "REAL"
            else:
                continue

            df['source_file'] = filename
            print(f"   ✅ {len(df)} articles - {label_type}")
            all_dfs.append(df)

        except Exception as e:
            print(f"   ❌ Erreur: {e}")

    # Fusion
    df_all = pd.concat(all_dfs, ignore_index=True, sort=False)
    initial_count = len(df_all)

    print(f"\n🧹 PRÉTRAITEMENT")

    # Nettoyage des colonnes title
    df_all = df_all.dropna(subset=['title'])
    df_all['title'] = df_all['title'].astype(str).str.strip()

    # Récupérer colonne text/content si existe
    text_cols = [col for col in df_all.columns if 'text' in col.lower() or 'content' in col.lower()]
    if text_cols:
        main_text_col = text_cols[0]
        df_all['text'] = df_all[main_text_col].fillna('').astype(str).str.strip()
    else:
        df_all['text'] = ''

    # Construction de la colonne image_url (si existe)
    df_all['image_url'] = None
    if 'top_img' in df_all.columns:
        df_all['image_url'] = df_all['top_img']
    if 'image_url' in df_all.columns:
        df_all['image_url'] = df_all['image_url'].fillna(df_all.get('top_img', ''))

    # Vérification de l'image valide
    def is_valid_url(url):
        if pd.isna(url):
            return False
        url = str(url).strip()
        return url.startswith(('http://', 'https://')) and len(url) > 10

    df_all['has_valid_image'] = df_all['image_url'].apply(is_valid_url)

    print(f"   📊 {initial_count} → {len(df_all)} articles conservés (pas de filtrage)")
    print(f"   🖼️ Articles avec images valides : {df_all['has_valid_image'].sum()}")

    return df_all

# Chargement des données
df_raw = load_and_preprocess_data()

print(f"\n📊 Répartition des labels :")
print(df_raw['label'].value_counts())

# Diagnostic supplémentaire
print("\n📊 DIAGNOSTIC COMPLET :")
print(f"Total articles       : {len(df_raw)}")
print(f"  Avec titre long    : {(df_raw['title'].str.len() >= 10).sum()}")
print(f"  Avec texte long    : {(df_raw['text'].str.len() >= 50).sum()}")
print(f"  Avec image valide  : {df_raw['has_valid_image'].sum()}")


def download_sample_images(df, max_images=500, images_folder='images_smote'):
    """
    Télécharge un échantillon d'images pour le test
    """
    print(f"📥 TÉLÉCHARGEMENT D'IMAGES (max: {max_images})")

    os.makedirs(images_folder, exist_ok=True)

    # Échantillonner de manière équilibrée
    df_with_images = df[df['has_valid_image']].copy()

    # Prendre un échantillon équilibré
    sample_per_class = max_images // 2
    df_fake = df_with_images[df_with_images['label'] == 0].sample(n=min(sample_per_class, len(df_with_images[df_with_images['label'] == 0])), random_state=42)
    df_real = df_with_images[df_with_images['label'] == 1].sample(n=min(sample_per_class, len(df_with_images[df_with_images['label'] == 1])), random_state=42)

    df_sample = pd.concat([df_fake, df_real]).sample(frac=1, random_state=42).reset_index(drop=True)

    print(f"📊 Échantillon sélectionné: {len(df_sample)} articles")
    print(df_sample['label'].value_counts())

    successful_downloads = []

    for idx, row in tqdm(df_sample.iterrows(), total=len(df_sample), desc="Téléchargement"):
        try:
            url = row['image_url']
            url_hash = hashlib.md5(url.encode()).hexdigest()
            filename = f"{url_hash}.jpg"
            filepath = os.path.join(images_folder, filename)

            if os.path.exists(filepath):
                successful_downloads.append((idx, filepath))
                continue

            headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            response = requests.get(url, headers=headers, timeout=10, stream=True)
            response.raise_for_status()

            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            # Vérifier que l'image est valide
            try:
                with Image.open(filepath) as img:
                    img.verify()
                successful_downloads.append((idx, filepath))
            except:
                os.remove(filepath)

        except Exception:
            continue

    # Mettre à jour le DataFrame
    df_sample['image_path'] = None
    for idx, filepath in successful_downloads:
        df_sample.loc[idx, 'image_path'] = filepath

    # Filtrer les articles avec images téléchargées
    df_final = df_sample[df_sample['image_path'].notna()].copy()

    print(f"\n✅ Images téléchargées: {len(successful_downloads)}")
    print(f"📊 Dataset final: {len(df_final)} articles")
    print(df_final['label'].value_counts())

    return df_final

# Téléchargement des images
df_with_images = download_sample_images(df_raw, max_images=600)

def extract_multimodal_features(df, max_samples=None):
    """
    Extrait les features BERT (texte) + ResNet (image) pour chaque échantillon
    """
    print("🔧 EXTRACTION DES FEATURES MULTIMODALES")
    print("=" * 50)

    if max_samples and len(df) > max_samples:
        df = df.sample(n=max_samples, random_state=42, stratify=df['label'])
        print(f"📊 Échantillonnage: {len(df)} articles")

    # Initialisation des modèles
    print("🤖 Chargement des modèles...")
    tokenizer = AutoTokenizer.from_pretrained(BERT_MODEL)
    bert_model = AutoModel.from_pretrained(BERT_MODEL).to(device)
    bert_model.eval()

    # ResNet pour les images
    resnet = resnet18(pretrained=True)
    resnet = nn.Sequential(*list(resnet.children())[:-1])  # Supprimer la couche finale
    resnet = resnet.to(device)
    resnet.eval()

    # Transformation des images
    transform = transforms.Compose([
        transforms.Resize((IMAGE_SIZE, IMAGE_SIZE)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    text_features = []
    image_features = []
    labels = []

    print("\n🔤 Extraction des features textuelles (BERT)...")

    with torch.no_grad():
        for idx, row in tqdm(df.iterrows(), total=len(df), desc="Features BERT+CNN"):
            try:
                # === FEATURES TEXTUELLES (BERT) ===
                text = f"{row['title']} [SEP] {row['text']}"
                encoding = tokenizer(
                    text,
                    truncation=True,
                    padding='max_length',
                    max_length=MAX_LENGTH,
                    return_tensors='pt'
                )

                input_ids = encoding['input_ids'].to(device)
                attention_mask = encoding['attention_mask'].to(device)

                bert_output = bert_model(input_ids=input_ids, attention_mask=attention_mask)
                text_feature = bert_output.last_hidden_state[:, 0, :].cpu().numpy().flatten()  # [CLS] token

                # === FEATURES D'IMAGES (ResNet) ===
                try:
                    image = Image.open(row['image_path']).convert('RGB')
                    image_tensor = transform(image).unsqueeze(0).to(device)

                    image_output = resnet(image_tensor)
                    image_feature = image_output.view(image_output.size(0), -1).cpu().numpy().flatten()
                except Exception as e:
                    # Image par défaut en cas d'erreur
                    image_feature = np.zeros(512)  # ResNet18 feature size

                text_features.append(text_feature)
                image_features.append(image_feature)
                labels.append(row['label'])

            except Exception as e:
                print(f"⚠️ Erreur pour l'article {idx}: {e}")
                continue

    # Conversion en arrays numpy
    X_text = np.array(text_features)
    X_image = np.array(image_features)
    y = np.array(labels)

    # Fusion des features multimodales
    X_combined = np.concatenate([X_text, X_image], axis=1)

    print(f"\n📊 FEATURES EXTRAITES:")
    print(f"   🔤 Features textuelles: {X_text.shape}")
    print(f"   🖼️ Features d'images: {X_image.shape}")
    print(f"   🔗 Features combinées: {X_combined.shape}")
    print(f"   🏷️ Labels: {y.shape}")

    return X_combined, y, df

# Extraction des features
X_features, y_labels, df_processed = extract_multimodal_features(df_with_images, max_samples=400)

def apply_smote_balancing(X, y, strategy='auto'):
    """
    Applique SMOTE pour équilibrer les données sur les features numériques
    """
    print("⚖️ APPLICATION DE SMOTE")
    print("=" * 30)

    # Analyse de l'équilibre actuel
    unique, counts = np.unique(y, return_counts=True)
    label_counts = dict(zip(unique, counts))

    print(f"📊 Avant SMOTE:")
    for label, count in label_counts.items():
        label_name = "FAKE" if label == 0 else "REAL"
        print(f"   {label_name}: {count} échantillons")

    ratio = max(counts) / min(counts)
    print(f"   ⚖️ Ratio déséquilibre: {ratio:.2f}:1")

    # Décider si SMOTE est nécessaire
    if ratio < 1.5:
        print(f"\n✅ Données déjà équilibrées (ratio < 1.5)")
        return X, y, False

    # Normalisation des features avant SMOTE
    print(f"\n🔄 Normalisation des features...")
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # Application de SMOTE
    print(f"🔄 Application de SMOTE...")

    # Ajuster k_neighbors selon la taille de la classe minoritaire
    min_samples = min(counts)
    k_neighbors = min(5, min_samples - 1) if min_samples > 1 else 1

    try:
        # SMOTE standard
        smote = SMOTE(
            random_state=42,
            k_neighbors=k_neighbors,
            sampling_strategy=strategy
        )
        X_resampled, y_resampled = smote.fit_resample(X_scaled, y)

        # Dénormalisation
        X_resampled = scaler.inverse_transform(X_resampled)

    except Exception as e:
        print(f"⚠️ Erreur SMOTE standard: {e}")
        print(f"🔄 Tentative avec SMOTETomek...")

        try:
            smote_tomek = SMOTETomek(random_state=42)
            X_resampled, y_resampled = smote_tomek.fit_resample(X_scaled, y)
            X_resampled = scaler.inverse_transform(X_resampled)
        except Exception as e2:
            print(f"❌ Erreur SMOTETomek: {e2}")
            print(f"🔄 Retour aux données originales")
            return X, y, False

    # Analyse après SMOTE
    unique_after, counts_after = np.unique(y_resampled, return_counts=True)
    label_counts_after = dict(zip(unique_after, counts_after))

    print(f"\n📊 Après SMOTE:")
    for label, count in label_counts_after.items():
        label_name = "FAKE" if label == 0 else "REAL"
        print(f"   {label_name}: {count} échantillons")

    ratio_after = max(counts_after) / min(counts_after)
    print(f"   ⚖️ Nouveau ratio: {ratio_after:.2f}:1")
    print(f"   📈 Augmentation: {len(X_resampled) - len(X)} échantillons (+{(len(X_resampled)/len(X)-1)*100:.1f}%)")

    return X_resampled, y_resampled, True

# Application de SMOTE
X_balanced, y_balanced, smote_applied = apply_smote_balancing(X_features, y_labels)

print(f"\n🎯 RÉSULTAT FINAL:")
print(f"   📏 Taille du dataset: {len(X_balanced)} échantillons")
print(f"   🔧 SMOTE appliqué: {'OUI' if smote_applied else 'NON'}")
print(f"   📊 Répartition finale: {np.bincount(y_balanced)}")

class PreExtractedMultimodalClassifier(nn.Module):
    """
    Modèle pour classifier les features pré-extraites (BERT + CNN)
    """
    def __init__(self, input_dim, hidden_dims=[512, 256, 128], num_classes=2, dropout=0.3):
        super(PreExtractedMultimodalClassifier, self).__init__()

        layers = []
        prev_dim = input_dim

        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.BatchNorm1d(hidden_dim)
            ])
            prev_dim = hidden_dim

        # Couche de sortie
        layers.append(nn.Linear(prev_dim, num_classes))

        self.classifier = nn.Sequential(*layers)

    def forward(self, x):
        return self.classifier(x)

# Séparation train/test
X_train, X_test, y_train, y_test = train_test_split(
    X_balanced, y_balanced,
    test_size=0.2,
    random_state=42,
    stratify=y_balanced
)

print(f"📊 SÉPARATION DES DONNÉES:")
print(f"   🏋️ Train: {len(X_train)} échantillons")
print(f"   🎯 Test: {len(X_test)} échantillons")
print(f"   📊 Train labels: {np.bincount(y_train)}")
print(f"   📊 Test labels: {np.bincount(y_test)}")

# Conversion en tenseurs PyTorch
X_train_tensor = torch.FloatTensor(X_train)
y_train_tensor = torch.LongTensor(y_train)
X_test_tensor = torch.FloatTensor(X_test)
y_test_tensor = torch.LongTensor(y_test)

# Création des DataLoaders
train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
test_dataset = TensorDataset(X_test_tensor, y_test_tensor)

train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True)
test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False)

# Initialisation du modèle
input_dim = X_train.shape[1]  # Dimension des features combinées
model = PreExtractedMultimodalClassifier(input_dim=input_dim).to(device)

print(f"\n🧠 MODÈLE INITIALISÉ:")
print(f"   📏 Dimension d'entrée: {input_dim}")
print(f"   🔢 Paramètres: {sum(p.numel() for p in model.parameters()):,}")
print(f"   🖥️ Device: {device}")

def train_preextracted_model(model, train_loader, test_loader, num_epochs=10, lr=0.001):
    """
    Entraîne le modèle sur les features pré-extraites
    """
    print(f"🚀 ENTRAÎNEMENT DU MODÈLE")
    print(f"📊 Paramètres: {num_epochs} epochs, LR={lr}")
    print("=" * 50)

    # Optimiseur et fonction de perte
    optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=0.01)
    criterion = nn.CrossEntropyLoss()
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=3, gamma=0.5)

    # Historique
    history = {
        'train_loss': [], 'train_acc': [],
        'val_loss': [], 'val_acc': []
    }

    best_val_acc = 0.0

    for epoch in range(num_epochs):
        print(f"\n📅 Epoch {epoch+1}/{num_epochs}")

        # === PHASE D'ENTRAÎNEMENT ===
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0

        for batch_features, batch_labels in tqdm(train_loader, desc="Train"):
            batch_features = batch_features.to(device)
            batch_labels = batch_labels.to(device)

            optimizer.zero_grad()
            outputs = model(batch_features)
            loss = criterion(outputs, batch_labels)
            loss.backward()
            optimizer.step()

            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += batch_labels.size(0)
            train_correct += (predicted == batch_labels).sum().item()

        avg_train_loss = train_loss / len(train_loader)
        train_acc = 100 * train_correct / train_total

        # === PHASE DE VALIDATION ===
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0

        with torch.no_grad():
            for batch_features, batch_labels in test_loader:
                batch_features = batch_features.to(device)
                batch_labels = batch_labels.to(device)

                outputs = model(batch_features)
                loss = criterion(outputs, batch_labels)

                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += batch_labels.size(0)
                val_correct += (predicted == batch_labels).sum().item()

        avg_val_loss = val_loss / len(test_loader)
        val_acc = 100 * val_correct / val_total

        # Sauvegarde du meilleur modèle
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            torch.save(model.state_dict(), 'best_smote_model.pth')

        # Mise à jour du scheduler
        scheduler.step()

        # Stockage de l'historique
        history['train_loss'].append(avg_train_loss)
        history['train_acc'].append(train_acc)
        history['val_loss'].append(avg_val_loss)
        history['val_acc'].append(val_acc)

        # Affichage des résultats
        print(f"   🏋️ Train - Loss: {avg_train_loss:.4f}, Acc: {train_acc:.2f}%")
        print(f"   🎯 Val   - Loss: {avg_val_loss:.4f}, Acc: {val_acc:.2f}%")
        print(f"   📈 LR: {scheduler.get_last_lr()[0]:.2e}")

    print(f"\n🎉 ENTRAÎNEMENT TERMINÉ!")
    print(f"🏆 Meilleure accuracy: {best_val_acc:.2f}%")

    return history, best_val_acc

# Entraînement
training_history, best_accuracy = train_preextracted_model(
    model, train_loader, test_loader,
    num_epochs=15, lr=0.001
)

def evaluate_and_visualize(model, test_loader, history, y_test):
    """
    Évaluation complète et visualisation des résultats
    """
    print("📊 ÉVALUATION FINALE")
    print("=" * 30)

    # Charger le meilleur modèle
    model.load_state_dict(torch.load('best_smote_model.pth'))
    model.eval()

    # Prédictions sur le jeu de test
    all_predictions = []
    all_labels = []
    all_probabilities = []

    with torch.no_grad():
        for batch_features, batch_labels in test_loader:
            batch_features = batch_features.to(device)
            outputs = model(batch_features)
            probabilities = torch.softmax(outputs, dim=1)
            _, predicted = torch.max(outputs, 1)

            all_predictions.extend(predicted.cpu().numpy())
            all_labels.extend(batch_labels.numpy())
            all_probabilities.extend(probabilities.cpu().numpy())

    # Métriques
    accuracy = accuracy_score(all_labels, all_predictions)
    f1 = f1_score(all_labels, all_predictions, average='weighted')

    print(f"🎯 MÉTRIQUES FINALES:")
    print(f"   📈 Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"   📊 F1-Score: {f1:.4f}")

    # Rapport détaillé
    print(f"\n📋 RAPPORT DE CLASSIFICATION:")
    print(classification_report(all_labels, all_predictions,
                              target_names=['Fake', 'Real']))

    # Visualisations
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))

    # 1. Courbes d'entraînement
    epochs = range(1, len(history['train_loss']) + 1)

    axes[0,0].plot(epochs, history['train_loss'], 'b-', label='Train Loss')
    axes[0,0].plot(epochs, history['val_loss'], 'r-', label='Val Loss')
    axes[0,0].set_title('Évolution de la Loss')
    axes[0,0].set_xlabel('Epoch')
    axes[0,0].set_ylabel('Loss')
    axes[0,0].legend()
    axes[0,0].grid(True)

    axes[0,1].plot(epochs, history['train_acc'], 'b-', label='Train Acc')
    axes[0,1].plot(epochs, history['val_acc'], 'r-', label='Val Acc')
    axes[0,1].set_title('Évolution de l\'Accuracy')
    axes[0,1].set_xlabel('Epoch')
    axes[0,1].set_ylabel('Accuracy (%)')
    axes[0,1].legend()
    axes[0,1].grid(True)

    # 2. Matrice de confusion
    cm = confusion_matrix(all_labels, all_predictions)
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=['Fake', 'Real'], yticklabels=['Fake', 'Real'],
                ax=axes[1,0])
    axes[1,0].set_title('Matrice de Confusion')
    axes[1,0].set_xlabel('Prédictions')
    axes[1,0].set_ylabel('Vraies étiquettes')

    # 3. Distribution des probabilités
    probs = np.array(all_probabilities)
    labels_array = np.array(all_labels)

    axes[1,1].hist(probs[labels_array == 0, 1], alpha=0.5, label='Fake (vraies)', bins=20, color='red')
    axes[1,1].hist(probs[labels_array == 1, 1], alpha=0.5, label='Real (vraies)', bins=20, color='green')
    axes[1,1].set_xlabel('Probabilité de Real')
    axes[1,1].set_ylabel('Fréquence')
    axes[1,1].set_title('Distribution des Probabilités')
    axes[1,1].legend()

    plt.tight_layout()
    plt.show()

    # Analyse des erreurs
    false_positives = np.sum((labels_array == 0) & (np.array(all_predictions) == 1))
    false_negatives = np.sum((labels_array == 1) & (np.array(all_predictions) == 0))

    print(f"\n🔍 ANALYSE DES ERREURS:")
    print(f"   🔴 Faux Positifs (Fake → Real): {false_positives}")
    print(f"   🟡 Faux Négatifs (Real → Fake): {false_negatives}")

    return {
        'accuracy': accuracy,
        'f1_score': f1,
        'predictions': all_predictions,
        'probabilities': all_probabilities
    }

# Évaluation finale
results = evaluate_and_visualize(model, test_loader, training_history, y_test)

# Affichage du résumé final
print("🎉 PROJET TERMINÉ AVEC SUCCÈS!")
print("=" * 50)
print(f"\n📊 RÉSULTATS FINAUX:")
print(f"   🎯 Accuracy finale: {results['accuracy']:.1%}")
print(f"   📈 F1-Score: {results['f1_score']:.3f}")
print(f"   🔧 SMOTE appliqué: {'OUI' if smote_applied else 'NON'}")
print(f"   📏 Dataset final: {len(X_balanced)} échantillons")

print(f"\n🔬 MÉTHODOLOGIE UTILISÉE:")
print(f"   1️⃣ Extraction features BERT + ResNet")
print(f"   2️⃣ Application SMOTE sur vecteurs numériques")
print(f"   3️⃣ Entraînement modèle multimodal")
print(f"   4️⃣ Évaluation complète")

print(f"\n✅ Cette approche respecte les bonnes pratiques:")
print(f"   • SMOTE sur données numériques uniquement")
print(f"   • Augmentation équilibrée des deux classes")
print(f"   • Features multimodales de haute qualité")
print(f"   • Évite l'overfitting par design")