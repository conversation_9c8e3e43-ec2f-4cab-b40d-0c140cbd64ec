# Installation des packages nécessaires
!pip install torch torchvision transformers pandas numpy matplotlib seaborn
!pip install scikit-learn requests pillow tqdm datasets accelerate

print("✅ Installation terminée")

import pandas as pd
import numpy as np
import os
import glob
import hashlib
import requests
from PIL import Image
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import warnings
import shutil
from pathlib import Path
warnings.filterwarnings('ignore')

# PyTorch
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from torchvision.models import resnet18

# Transformers
from transformers import AutoTokenizer, AutoModel

# Sklearn
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix

# Configuration
plt.style.use('default')
sns.set_palette("husl")
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"🖥️ Device: {device}")

# Paramètres globaux
BERT_MODEL = 'distilbert-base-uncased'
MAX_LENGTH = 512
IMAGE_SIZE = 224
BATCH_SIZE = 16

# Création de la structure de dossiers
BASE_DIR = Path('multimodal_fakenews_complete')
DATASETS_DIR = BASE_DIR / 'datasets'
IMAGES_DIR = BASE_DIR / 'images'
MODELS_DIR = BASE_DIR / 'models'

# Créer les dossiers
for dir_path in [BASE_DIR, DATASETS_DIR, IMAGES_DIR, MODELS_DIR]:
    dir_path.mkdir(exist_ok=True)
    print(f"📁 Dossier créé/vérifié: {dir_path}")

print("\n✅ Configuration terminée")

def load_all_csv_comprehensive(data_folder='FakeNewsNetData'):
    """
    Charge TOUS les fichiers CSV sans filtrage strict
    Objectif: Maximiser la couverture (3k-5k articles)
    """
    print("📁 CHARGEMENT COMPLET - MAXIMISER LA COUVERTURE")
    print("=" * 60)
    
    all_dataframes = []
    csv_files = glob.glob(os.path.join(data_folder, '*.csv'))
    
    print(f"📄 Fichiers CSV trouvés: {len(csv_files)}")
    
    total_articles = 0
    
    for file_path in csv_files:
        filename = os.path.basename(file_path)
        print(f"\n📄 Traitement: {filename}")
        
        try:
            # Lecture avec gestion d'erreurs
            try:
                df = pd.read_csv(file_path, encoding='utf-8')
            except UnicodeDecodeError:
                df = pd.read_csv(file_path, encoding='latin-1')
            except Exception:
                df = pd.read_csv(file_path, encoding='utf-8', errors='ignore')
            
            print(f"   📊 Articles bruts: {len(df)}")
            
            # Détection automatique du label
            if 'fake' in filename.lower():
                df['label'] = 0
                label_type = "FAKE"
            elif 'real' in filename.lower():
                df['label'] = 1
                label_type = "REAL"
            else:
                print(f"   ⚠️ Label non détecté pour {filename} - IGNORÉ")
                continue
            
            # Métadonnées
            df['source_file'] = filename
            df['article_id'] = df.index.astype(str) + '_' + filename.replace('.csv', '')
            
            print(f"   ✅ {len(df)} articles - Label: {label_type}")
            print(f"   📋 Colonnes: {list(df.columns)}")
            
            # Analyse des colonnes d'images
            image_cols = [col for col in df.columns if any(img_word in col.lower() 
                         for img_word in ['image', 'img', 'photo', 'picture'])]
            if image_cols:
                print(f"   🖼️ Colonnes d'images: {image_cols}")
                for col in image_cols:
                    non_null = df[col].notna().sum()
                    print(f"      {col}: {non_null}/{len(df)} ({non_null/len(df)*100:.1f}%)")
            
            all_dataframes.append(df)
            total_articles += len(df)
            
        except Exception as e:
            print(f"   ❌ Erreur: {str(e)}")
            continue
    
    if not all_dataframes:
        print("❌ Aucun fichier chargé avec succès")
        return None
    
    # Fusion avec gestion des colonnes différentes
    print(f"\n🔗 FUSION DES DATAFRAMES...")
    df_all = pd.concat(all_dataframes, ignore_index=True, sort=False)
    
    print(f"\n🎯 RÉSULTATS DU CHARGEMENT:")
    print(f"   📊 Total articles: {len(df_all)}")
    print(f"   📈 Répartition des labels:")
    
    label_counts = df_all['label'].value_counts()
    for label, count in label_counts.items():
        label_name = "FAKE" if label == 0 else "REAL"
        print(f"      {label_name}: {count} ({count/len(df_all)*100:.1f}%)")
    
    print(f"   📋 Colonnes finales: {len(df_all.columns)}")
    print(f"   📏 Shape: {df_all.shape}")
    
    # Objectif atteint ?
    if len(df_all) >= 3000:
        print(f"   ✅ Objectif atteint: {len(df_all)} >= 3000 articles")
    else:
        print(f"   ⚠️ Objectif partiel: {len(df_all)} < 3000 articles")
    
    return df_all

# Chargement des données
df_all = load_all_csv_comprehensive()

if df_all is not None:
    print(f"\n📋 Colonnes disponibles: {list(df_all.columns)}")
else:
    print("❌ Échec du chargement des données")

def preprocess_maximize_coverage(df):
    """
    Prétraitement qui maximise la couverture
    - Pas de suppression stricte
    - Garde articles avec image même sans texte
    - Garde articles avec texte même sans image
    """
    print("🧹 PRÉTRAITEMENT - MAXIMISER LA COUVERTURE")
    print("=" * 50)
    
    initial_count = len(df)
    print(f"📊 Articles initiaux: {initial_count}")
    
    # === 1. GESTION DES COLONNES TEXTUELLES ===
    print(f"\n1️⃣ Gestion des textes...")
    
    # Identifier les colonnes de texte
    text_columns = [col for col in df.columns if col.lower() in ['text', 'content', 'body', 'article']]
    if not text_columns and 'text' not in df.columns:
        # Chercher d'autres colonnes qui pourraient contenir du texte
        potential_text_cols = [col for col in df.columns if 'text' in col.lower()]
        if potential_text_cols:
            text_columns = [potential_text_cols[0]]
    
    # Standardiser les colonnes
    if 'title' not in df.columns:
        title_cols = [col for col in df.columns if 'title' in col.lower()]
        if title_cols:
            df = df.rename(columns={title_cols[0]: 'title'})
    
    if text_columns and 'text' not in df.columns:
        df = df.rename(columns={text_columns[0]: 'text'})
    
    # Créer les colonnes manquantes avec des valeurs par défaut
    if 'title' not in df.columns:
        df['title'] = 'No title available'
    if 'text' not in df.columns:
        df['text'] = 'No text available'
    
    # Nettoyage minimal (pas de suppression)
    def safe_clean_text(text):
        if pd.isna(text) or text is None:
            return "No content"
        return str(text).strip() if str(text).strip() else "No content"
    
    df['title'] = df['title'].apply(safe_clean_text)
    df['text'] = df['text'].apply(safe_clean_text)
    
    # Marquer la disponibilité du texte
    df['has_valid_title'] = (df['title'] != "No content") & (df['title'] != "No title available")
    df['has_valid_text'] = (df['text'] != "No content") & (df['text'] != "No text available")
    df['has_any_text'] = df['has_valid_title'] | df['has_valid_text']
    
    valid_text_count = df['has_any_text'].sum()
    print(f"   📝 Articles avec texte valide: {valid_text_count}/{len(df)} ({valid_text_count/len(df)*100:.1f}%)")
    
    # === 2. GESTION DES IMAGES ===
    print(f"\n2️⃣ Gestion des images...")
    
    # Créer une colonne image_url unifiée
    df['image_url'] = None
    
    # Priorité: image_url > top_img > images > img_url
    image_source_cols = ['image_url', 'top_img', 'images', 'img_url', 'image', 'photo_url']
    
    for col in image_source_cols:
        if col in df.columns:
            print(f"   🔍 Traitement colonne: {col}")
            
            # Fonction pour extraire la première URL valide
            def extract_image_url(value):
                if pd.isna(value) or value is None:
                    return None
                
                value_str = str(value).strip()
                if not value_str or value_str.lower() in ['nan', 'none', '']:
                    return None
                
                # Si c'est une liste d'URLs (séparées par des espaces, virgules, etc.)
                urls = value_str.replace(',', ' ').replace(';', ' ').split()
                for url in urls:
                    url = url.strip()
                    if url.startswith(('http://', 'https://')) and len(url) > 10:
                        return url
                
                # Si c'est une URL simple
                if value_str.startswith(('http://', 'https://')) and len(value_str) > 10:
                    return value_str
                
                return None
            
            # Remplir les URLs manquantes
            temp_urls = df[col].apply(extract_image_url)
            df['image_url'] = df['image_url'].fillna(temp_urls)
            
            filled_count = temp_urls.notna().sum()
            print(f"      ✅ {filled_count} URLs extraites de {col}")
    
    # Marquer la disponibilité des images
    df['has_image_url'] = df['image_url'].notna()
    
    valid_image_count = df['has_image_url'].sum()
    print(f"   🖼️ Articles avec URL d'image: {valid_image_count}/{len(df)} ({valid_image_count/len(df)*100:.1f}%)")
    
    # === 3. CLASSIFICATION DES ARTICLES ===
    print(f"\n3️⃣ Classification des articles...")
    
    # Créer les catégories
    df['article_type'] = 'unknown'
    
    # Text only
    text_only_mask = df['has_any_text'] & (~df['has_image_url'])
    df.loc[text_only_mask, 'article_type'] = 'text_only'
    
    # Image only
    image_only_mask = (~df['has_any_text']) & df['has_image_url']
    df.loc[image_only_mask, 'article_type'] = 'image_only'
    
    # Multimodal
    multimodal_mask = df['has_any_text'] & df['has_image_url']
    df.loc[multimodal_mask, 'article_type'] = 'multimodal'
    
    # Statistiques par type
    type_counts = df['article_type'].value_counts()
    print(f"   📊 Répartition par type:")
    for article_type, count in type_counts.items():
        print(f"      {article_type}: {count} ({count/len(df)*100:.1f}%)")
    
    # === 4. STATISTIQUES FINALES ===
    print(f"\n4️⃣ RÉSULTATS DU PRÉTRAITEMENT:")
    print(f"   📊 Articles conservés: {len(df)}/{initial_count} (100% - pas de suppression)")
    print(f"   📝 Avec texte: {df['has_any_text'].sum()}")
    print(f"   🖼️ Avec image: {df['has_image_url'].sum()}")
    print(f"   🔗 Multimodal: {(df['article_type'] == 'multimodal').sum()}")
    
    # Répartition par label
    print(f"\n   📈 Répartition par label:")
    for label in [0, 1]:
        label_name = "FAKE" if label == 0 else "REAL"
        total = (df['label'] == label).sum()
        with_text = ((df['label'] == label) & df['has_any_text']).sum()
        with_image = ((df['label'] == label) & df['has_image_url']).sum()
        multimodal = ((df['label'] == label) & (df['article_type'] == 'multimodal')).sum()
        
        print(f"      {label_name}: {total} total ({with_text} texte, {with_image} image, {multimodal} multimodal)")
    
    return df

# Prétraitement
if df_all is not None:
    df_processed = preprocess_maximize_coverage(df_all)
    print(f"\n✅ Prétraitement terminé: {len(df_processed)} articles prêts")
else:
    print("❌ Impossible de prétraiter - pas de données")

def download_images_massive(df, max_workers=10, timeout=15):
    """
    Téléchargement massif et robuste des images
    """
    print(f"📥 TÉLÉCHARGEMENT MASSIF DES IMAGES")
    print("=" * 50)
    
    # Filtrer les articles avec URLs d'images
    df_with_images = df[df['has_image_url']].copy()
    print(f"🎯 Articles à traiter: {len(df_with_images)}")
    
    if len(df_with_images) == 0:
        print("❌ Aucune image à télécharger")
        df['image_path'] = None
        df['image_downloaded'] = False
        return df
    
    # Headers pour éviter les blocages
    headers_list = [
        {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'},
        {'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'},
        {'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'},
        {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'}
    ]
    
    successful_downloads = []
    failed_downloads = []
    
    print(f"🔄 Début du téléchargement...")
    
    for idx, row in tqdm(df_with_images.iterrows(), total=len(df_with_images), desc="Téléchargement"):
        image_url = row['image_url']
        
        # Créer un nom de fichier unique avec MD5
        url_hash = hashlib.md5(image_url.encode()).hexdigest()
        
        # Vérifier si l'image existe déjà
        existing_files = list(IMAGES_DIR.glob(f"{url_hash}.*"))
        if existing_files:
            successful_downloads.append((idx, str(existing_files[0])))
            continue
        
        # Tentatives de téléchargement
        downloaded = False
        
        for attempt in range(3):  # 3 tentatives max
            try:
                headers = headers_list[attempt % len(headers_list)]
                
                # Nettoyer l'URL
                clean_url = image_url.strip()
                if not clean_url.startswith(('http://', 'https://')):
                    clean_url = 'https://' + clean_url
                
                # Télécharger
                response = requests.get(
                    clean_url,
                    headers=headers,
                    timeout=timeout,
                    stream=True,
                    allow_redirects=True
                )
                response.raise_for_status()
                
                # Vérifier le type de contenu
                content_type = response.headers.get('content-type', '').lower()
                if not any(img_type in content_type for img_type in ['image/', 'jpeg', 'png', 'gif', 'webp']):
                    continue
                
                # Déterminer l'extension
                if 'jpeg' in content_type or 'jpg' in content_type:
                    ext = '.jpg'
                elif 'png' in content_type:
                    ext = '.png'
                elif 'gif' in content_type:
                    ext = '.gif'
                elif 'webp' in content_type:
                    ext = '.webp'
                else:
                    ext = '.jpg'  # Par défaut
                
                filepath = IMAGES_DIR / f"{url_hash}{ext}"
                
                # Sauvegarder
                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                
                # Vérifier l'image
                try:
                    with Image.open(filepath) as img:
                        # Vérifier la taille minimale
                        if img.size[0] >= 32 and img.size[1] >= 32:
                            successful_downloads.append((idx, str(filepath)))
                            downloaded = True
                            break
                        else:
                            filepath.unlink()  # Supprimer image trop petite
                except Exception:
                    if filepath.exists():
                        filepath.unlink()
                    continue
                
            except Exception as e:
                if attempt == 2:  # Dernière tentative
                    failed_downloads.append((idx, str(e)))
                continue
        
        if not downloaded:
            failed_downloads.append((idx, "Toutes les tentatives ont échoué"))
    
    # Mettre à jour le DataFrame
    df['image_path'] = None
    df['image_downloaded'] = False
    
    for idx, filepath in successful_downloads:
        df.loc[idx, 'image_path'] = filepath
        df.loc[idx, 'image_downloaded'] = True
    
    # Statistiques
    success_rate = len(successful_downloads) / len(df_with_images) * 100
    
    print(f"\n📊 RÉSULTATS DU TÉLÉCHARGEMENT:")
    print(f"   ✅ Succès: {len(successful_downloads)}")
    print(f"   ❌ Échecs: {len(failed_downloads)}")
    print(f"   📈 Taux de succès: {success_rate:.1f}%")
    print(f"   💾 Images sauvées dans: {IMAGES_DIR}")
    
    return df

# Téléchargement des images
if 'df_processed' in locals():
    df_with_images = download_images_massive(df_processed)
    print(f"\n✅ Téléchargement terminé")
else:
    print("❌ Pas de données à traiter")

def create_three_datasets(df):
    """
    Crée les 3 datasets demandés:
    - text_only.csv
    - image_only.csv  
    - multimodal.csv
    """
    print(f"💾 CRÉATION DES 3 DATASETS")
    print("=" * 40)
    
    # Dataset complet
    complete_path = DATASETS_DIR / 'complete_dataset.csv'
    df.to_csv(complete_path, index=False)
    print(f"✅ Dataset complet sauvé: {complete_path} ({len(df)} articles)")
    
    # 1. TEXT ONLY - Articles avec texte valide (avec ou sans image)
    text_only_df = df[df['has_any_text']].copy()
    
    # Colonnes essentielles pour NLP
    text_columns = ['article_id', 'title', 'text', 'label', 'source_file', 
                   'has_valid_title', 'has_valid_text', 'has_any_text']
    
    # Ajouter colonnes existantes
    available_text_cols = [col for col in text_columns if col in text_only_df.columns]
    text_only_final = text_only_df[available_text_cols].copy()
    
    text_path = DATASETS_DIR / 'text_only.csv'
    text_only_final.to_csv(text_path, index=False)
    
    print(f"📝 Text-only dataset: {text_path} ({len(text_only_final)} articles)")
    print(f"   Colonnes: {list(text_only_final.columns)}")
    
    # 2. IMAGE ONLY - Articles avec images téléchargées
    image_only_df = df[df['image_downloaded']].copy()
    
    # Colonnes essentielles pour CNN
    image_columns = ['article_id', 'image_path', 'image_url', 'label', 'source_file',
                    'has_image_url', 'image_downloaded']
    
    # Ajouter titre si disponible (pour contexte)
    if 'title' in image_only_df.columns:
        image_columns.append('title')
    
    available_image_cols = [col for col in image_columns if col in image_only_df.columns]
    image_only_final = image_only_df[available_image_cols].copy()
    
    image_path = DATASETS_DIR / 'image_only.csv'
    image_only_final.to_csv(image_path, index=False)
    
    print(f"🖼️ Image-only dataset: {image_path} ({len(image_only_final)} articles)")
    print(f"   Colonnes: {list(image_only_final.columns)}")
    
    # 3. MULTIMODAL - Articles avec texte ET image
    multimodal_df = df[df['has_any_text'] & df['image_downloaded']].copy()
    
    # Colonnes complètes pour multimodal
    multimodal_columns = ['article_id', 'title', 'text', 'image_path', 'image_url', 
                         'label', 'source_file', 'has_valid_title', 'has_valid_text', 
                         'has_any_text', 'has_image_url', 'image_downloaded', 'article_type']
    
    available_multi_cols = [col for col in multimodal_columns if col in multimodal_df.columns]
    multimodal_final = multimodal_df[available_multi_cols].copy()
    
    multimodal_path = DATASETS_DIR / 'multimodal.csv'
    multimodal_final.to_csv(multimodal_path, index=False)
    
    print(f"🔗 Multimodal dataset: {multimodal_path} ({len(multimodal_final)} articles)")
    print(f"   Colonnes: {list(multimodal_final.columns)}")
    
    # Statistiques par label pour chaque dataset
    print(f"\n📊 RÉPARTITION PAR LABEL:")
    
    for name, dataset in [('Text-only', text_only_final), 
                         ('Image-only', image_only_final), 
                         ('Multimodal', multimodal_final)]:
        if len(dataset) > 0:
            label_counts = dataset['label'].value_counts()
            fake_count = label_counts.get(0, 0)
            real_count = label_counts.get(1, 0)
            print(f"   {name}: FAKE={fake_count}, REAL={real_count} (Total: {len(dataset)})")
        else:
            print(f"   {name}: Aucun article")
    
    # Résumé des fichiers créés
    print(f"\n📁 FICHIERS CRÉÉS DANS {DATASETS_DIR}:")
    for file_path in DATASETS_DIR.glob('*.csv'):
        size_mb = file_path.stat().st_size / (1024 * 1024)
        print(f"   📄 {file_path.name}: {size_mb:.2f} MB")
    
    return {
        'complete': df,
        'text_only': text_only_final,
        'image_only': image_only_final,
        'multimodal': multimodal_final
    }

# Création des datasets
if 'df_with_images' in locals():
    datasets = create_three_datasets(df_with_images)
    print(f"\n✅ Tous les datasets créés avec succès!")
else:
    print("❌ Pas de données à sauvegarder")

def quick_exploratory_analysis(datasets):
    """
    Analyse exploratoire rapide des 3 datasets
    """
    print("📊 ANALYSE EXPLORATOIRE RAPIDE")
    print("=" * 40)
    
    # Analyse du dataset complet
    df_complete = datasets['complete']
    
    print(f"\n🎯 DATASET COMPLET ({len(df_complete)} articles):")
    print(f"   📈 Répartition des labels:")
    label_counts = df_complete['label'].value_counts()
    for label, count in label_counts.items():
        label_name = "FAKE" if label == 0 else "REAL"
        print(f"      {label_name}: {count} ({count/len(df_complete)*100:.1f}%)")
    
    # Analyse des longueurs de texte
    if 'title' in df_complete.columns and 'text' in df_complete.columns:
        # Calculer les longueurs pour les articles avec texte valide
        valid_text_df = df_complete[df_complete['has_any_text']]
        
        if len(valid_text_df) > 0:
            title_lengths = valid_text_df['title'].str.len()
            text_lengths = valid_text_df['text'].str.len()
            
            print(f"\n📝 ANALYSE DES TEXTES ({len(valid_text_df)} articles avec texte):")
            print(f"   📏 Longueur des titres:")
            print(f"      Moyenne: {title_lengths.mean():.1f} caractères")
            print(f"      Médiane: {title_lengths.median():.1f} caractères")
            print(f"      Min-Max: {title_lengths.min()}-{title_lengths.max()}")
            
            print(f"   📄 Longueur des textes:")
            print(f"      Moyenne: {text_lengths.mean():.1f} caractères")
            print(f"      Médiane: {text_lengths.median():.1f} caractères")
            print(f"      Min-Max: {text_lengths.min()}-{text_lengths.max()}")
    
    # Analyse des images
    images_downloaded = df_complete['image_downloaded'].sum() if 'image_downloaded' in df_complete.columns else 0
    images_urls = df_complete['has_image_url'].sum() if 'has_image_url' in df_complete.columns else 0
    
    print(f"\n🖼️ ANALYSE DES IMAGES:")
    print(f"   🔗 URLs d'images trouvées: {images_urls}")
    print(f"   ✅ Images téléchargées: {images_downloaded}")
    if images_urls > 0:
        success_rate = images_downloaded / images_urls * 100
        print(f"   📈 Taux de succès téléchargement: {success_rate:.1f}%")
    
    # Analyse par type d'article
    if 'article_type' in df_complete.columns:
        print(f"\n📊 RÉPARTITION PAR TYPE:")
        type_counts = df_complete['article_type'].value_counts()
        for article_type, count in type_counts.items():
            print(f"   {article_type}: {count} ({count/len(df_complete)*100:.1f}%)")
    
    # Exemples d'articles
    print(f"\n📰 EXEMPLES D'ARTICLES:")
    
    # Exemple FAKE
    fake_examples = df_complete[df_complete['label'] == 0]
    if len(fake_examples) > 0:
        fake_sample = fake_examples.iloc[0]
        print(f"\n🔴 EXEMPLE FAKE NEWS:")
        if 'title' in fake_sample and pd.notna(fake_sample['title']):
            print(f"   Titre: {str(fake_sample['title'])[:100]}...")
        if 'text' in fake_sample and pd.notna(fake_sample['text']):
            print(f"   Texte: {str(fake_sample['text'])[:150]}...")
        if 'image_path' in fake_sample and pd.notna(fake_sample['image_path']):
            print(f"   Image: {fake_sample['image_path']}")
    
    # Exemple REAL
    real_examples = df_complete[df_complete['label'] == 1]
    if len(real_examples) > 0:
        real_sample = real_examples.iloc[0]
        print(f"\n🟢 EXEMPLE REAL NEWS:")
        if 'title' in real_sample and pd.notna(real_sample['title']):
            print(f"   Titre: {str(real_sample['title'])[:100]}...")
        if 'text' in real_sample and pd.notna(real_sample['text']):
            print(f"   Texte: {str(real_sample['text'])[:150]}...")
        if 'image_path' in real_sample and pd.notna(real_sample['image_path']):
            print(f"   Image: {real_sample['image_path']}")
    
    # Visualisations
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. Répartition des labels
    label_counts.plot(kind='bar', ax=axes[0,0], color=['red', 'green'])
    axes[0,0].set_title('Répartition des Labels')
    axes[0,0].set_xlabel('Label (0=Fake, 1=Real)')
    axes[0,0].set_ylabel('Nombre d\'articles')
    axes[0,0].tick_params(axis='x', rotation=0)
    
    # 2. Répartition par type
    if 'article_type' in df_complete.columns:
        type_counts.plot(kind='bar', ax=axes[0,1], color=['skyblue', 'lightcoral', 'lightgreen', 'orange'])
        axes[0,1].set_title('Répartition par Type')
        axes[0,1].set_xlabel('Type d\'article')
        axes[0,1].set_ylabel('Nombre d\'articles')
        axes[0,1].tick_params(axis='x', rotation=45)
    
    # 3. Longueurs des titres par label
    if 'title' in df_complete.columns and len(valid_text_df) > 0:
        valid_text_df.boxplot(column='title', by='label', ax=axes[1,0])
        axes[1,0].set_title('Longueur des Titres par Label')
        axes[1,0].set_xlabel('Label (0=Fake, 1=Real)')
        axes[1,0].set_ylabel('Longueur (caractères)')
    
    # 4. Comparaison des datasets
    dataset_sizes = {
        'Complete': len(datasets['complete']),
        'Text-only': len(datasets['text_only']),
        'Image-only': len(datasets['image_only']),
        'Multimodal': len(datasets['multimodal'])
    }
    
    bars = axes[1,1].bar(dataset_sizes.keys(), dataset_sizes.values(), 
                        color=['gray', 'blue', 'orange', 'purple'])
    axes[1,1].set_title('Taille des Datasets')
    axes[1,1].set_ylabel('Nombre d\'articles')
    axes[1,1].tick_params(axis='x', rotation=45)
    
    # Ajouter les valeurs sur les barres
    for bar, value in zip(bars, dataset_sizes.values()):
        height = bar.get_height()
        axes[1,1].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                      f'{value}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.show()
    
    # Recommandations
    print(f"\n💡 RECOMMANDATIONS:")
    
    total_articles = len(df_complete)
    multimodal_articles = len(datasets['multimodal'])
    
    if total_articles >= 3000:
        print(f"   ✅ Excellent: {total_articles} articles (objectif 3k-5k atteint)")
    elif total_articles >= 1000:
        print(f"   ⚠️ Correct: {total_articles} articles (objectif 3k-5k partiellement atteint)")
    else:
        print(f"   ❌ Insuffisant: {total_articles} articles (objectif 3k-5k non atteint)")
    
    if multimodal_articles >= 500:
        print(f"   ✅ Dataset multimodal viable: {multimodal_articles} articles")
    elif multimodal_articles >= 200:
        print(f"   ⚠️ Dataset multimodal petit: {multimodal_articles} articles")
    else:
        print(f"   ❌ Dataset multimodal trop petit: {multimodal_articles} articles")
    
    return dataset_sizes

# Analyse exploratoire
if 'datasets' in locals():
    analysis_results = quick_exploratory_analysis(datasets)
    print(f"\n✅ Analyse exploratoire terminée")
else:
    print("❌ Pas de datasets à analyser")

# Configuration pour la modélisation
print("🤖 PRÉPARATION POUR LA MODÉLISATION")
print("=" * 45)

# Vérifier la disponibilité des datasets
if 'datasets' in locals() and len(datasets['multimodal']) > 0:
    df_multimodal = datasets['multimodal']
    print(f"✅ Dataset multimodal disponible: {len(df_multimodal)} articles")
    
    # Vérifier l'équilibre des classes
    label_counts = df_multimodal['label'].value_counts()
    print(f"📊 Répartition des labels:")
    for label, count in label_counts.items():
        label_name = "FAKE" if label == 0 else "REAL"
        print(f"   {label_name}: {count} ({count/len(df_multimodal)*100:.1f}%)")
    
    # Recommandations pour la modélisation
    min_class_size = label_counts.min()
    print(f"\n💡 RECOMMANDATIONS POUR LA MODÉLISATION:")
    
    if min_class_size >= 200:
        print(f"   ✅ Dataset viable pour entraînement (classe min: {min_class_size})")
        print(f"   🎯 Stratégie recommandée: Entraînement standard avec validation croisée")
    elif min_class_size >= 100:
        print(f"   ⚠️ Dataset petit (classe min: {min_class_size})")
        print(f"   🎯 Stratégie recommandée: Augmentation de données + validation croisée")
    else:
        print(f"   ❌ Dataset très petit (classe min: {min_class_size})")
        print(f"   🎯 Stratégie recommandée: Transfer learning + augmentation massive")
    
    # Initialisation des modèles
    print(f"\n🔧 Initialisation des composants...")
    
    try:
        # Tokenizer BERT
        tokenizer = AutoTokenizer.from_pretrained(BERT_MODEL)
        print(f"   ✅ Tokenizer BERT chargé: {BERT_MODEL}")
        
        # Modèle BERT (pour test)
        bert_model = AutoModel.from_pretrained(BERT_MODEL)
        print(f"   ✅ Modèle BERT chargé")
        
        # ResNet (pour test)
        resnet_model = resnet18(pretrained=True)
        print(f"   ✅ ResNet18 chargé")
        
        print(f"\n🎯 Prêt pour l'entraînement multimodal!")
        
    except Exception as e:
        print(f"   ❌ Erreur lors du chargement des modèles: {e}")
        print(f"   💡 Vérifiez votre connexion internet pour télécharger les modèles pré-entraînés")

else:
    print(f"❌ Pas de dataset multimodal disponible pour la modélisation")
    print(f"💡 Utilisez les datasets text_only ou image_only pour des modèles unimodaux")

# Résumé final avec statistiques
print("🎉 PIPELINE COMPLET TERMINÉ!")
print("=" * 50)

if 'datasets' in locals():
    print(f"\n📊 STATISTIQUES FINALES:")
    print(f"   📄 Dataset complet: {len(datasets['complete'])} articles")
    print(f"   📝 Text-only: {len(datasets['text_only'])} articles")
    print(f"   🖼️ Image-only: {len(datasets['image_only'])} articles")
    print(f"   🔗 Multimodal: {len(datasets['multimodal'])} articles")
    
    # Vérifier l'objectif 3k-5k
    total = len(datasets['complete'])
    if total >= 3000:
        print(f"   ✅ Objectif 3k-5k ATTEINT: {total} articles")
    else:
        print(f"   ⚠️ Objectif 3k-5k partiel: {total} articles")
    
    print(f"\n📁 FICHIERS CRÉÉS:")
    for file_path in DATASETS_DIR.glob('*.csv'):
        size_mb = file_path.stat().st_size / (1024 * 1024)
        print(f"   📄 {file_path.name}: {size_mb:.2f} MB")
    
    # Images téléchargées
    image_count = len(list(IMAGES_DIR.glob('*')))
    print(f"   🖼️ Images téléchargées: {image_count}")
    
    # Modèles
    model_files = list(MODELS_DIR.glob('*.pth'))
    if model_files:
        print(f"   🤖 Modèles sauvés: {len(model_files)}")
    
    print(f"\n🎯 PRÊT POUR LA SUITE!")
    print(f"   1️⃣ Utilisez les datasets CSV pour vos expérimentations")
    print(f"   2️⃣ Les images sont dans {IMAGES_DIR}")
    print(f"   3️⃣ Le modèle multimodal est dans {MODELS_DIR}")
    print(f"   4️⃣ Tout est sauvegardé dans {BASE_DIR}")

else:
    print("❌ Pipeline incomplet - vérifiez les étapes précédentes")

print(f"\n🚀 Pipeline de détection de fake news multimodale terminé avec succès!")